// 统一的设计规范文件

// 主色调
$primary-color: #1976d2;
$primary-dark-color: #1565c0;
$primary-light-color: #e3f2fd;

// 成功/完成状态颜色
$success-color: #4caf50;
$success-light-color: #e8f5e9;

// 警告/待处理状态颜色
$warning-color: #ff9800;
$warning-light-color: #fff3e0;

// 危险/错误状态颜色
$danger-color: #f44336;
$danger-light-color: #ffebee;

// 中性色
$white: #ffffff;
$black: #000000;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;

// 字体大小
$font-size-xs: 20rpx;
$font-size-sm: 24rpx;
$font-size-base: 28rpx;
$font-size-lg: 32rpx;
$font-size-xl: 36rpx;
$font-size-xxl: 40rpx;

// 字体粗细
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 圆角
$border-radius-sm: 12rpx;
$border-radius-base: 16rpx;
$border-radius-lg: 20rpx;
$border-radius-xl: 24rpx;
$border-radius-pill: 40rpx;

// 阴影
$box-shadow-sm: 0 4rpx 12rpx rgba(25, 118, 210, 0.05);
$box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
$box-shadow-lg: 0 12rpx 40rpx rgba(25, 118, 210, 0.15);

// 间距
$spacer-xs: 12rpx;
$spacer-sm: 16rpx;
$spacer-base: 24rpx;
$spacer-lg: 32rpx;
$spacer-xl: 40rpx;

// 通用卡片样式
.flowup-card {
  background: $white;
  border-radius: $border-radius-xl;
  overflow: hidden;
  box-shadow: $box-shadow;
  border: 1rpx solid rgba(25, 118, 210, 0.1);
  margin-bottom: $spacer-base;
}

// 通用卡片头部
.flowup-card-header {
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark-color 100%);
  padding: $spacer-lg $spacer-xl;
  color: $white;
  
  .card-header-content {
    display: flex;
    align-items: center;
    gap: $spacer-base;
    
    .header-icon {
      font-size: 32rpx;
    }
    
    .header-title {
      font-size: $font-size-xl;
      font-weight: $font-weight-bold;
    }
  }
}

// 通用卡片内容区域
.flowup-card-content {
  padding: $spacer-xl;
}

// 通用分段标题
.flowup-section-header {
  display: flex;
  align-items: center;
  gap: $spacer-base;
  padding: $spacer-base $spacer-lg;
  background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
  border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);
  
  .section-icon {
    font-size: 32rpx;
    color: $primary-dark-color;
  }
  
  .section-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-bold;
    color: $primary-dark-color;
  }
}

// 通用信息展示项
.flowup-info-item {
  margin-bottom: $spacer-base;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .info-label {
    font-size: $font-size-base;
    color: $primary-dark-color;
    font-weight: $font-weight-semibold;
    margin-bottom: $spacer-xs;
    display: block;
  }
  
  .info-value {
    padding: $spacer-sm;
    background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
    border-radius: $border-radius-base;
    border: 1rpx solid rgba(25, 118, 210, 0.1);
    font-size: $font-size-base;
    color: $gray-800;
    font-weight: $font-weight-normal;
    line-height: 1.4;
    min-height: 44rpx;
    display: flex;
    align-items: center;
    
    &.textarea-value {
      min-height: 120rpx;
      align-items: flex-start;
      padding-top: $spacer-sm;
      word-break: break-all;
      line-height: 1.6;
    }
  }
}

// 通用按钮样式
.flowup-action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: $white;
  padding: $spacer-base $spacer-sm;
  padding-bottom: calc($spacer-base + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);
  
  .action-row {
    display: flex;
    gap: $spacer-sm;
    
    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: $border-radius-lg;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: $font-weight-semibold;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;
      
      .btn-icon {
        font-size: 28rpx;
      }
      
      .btn-text {
        font-size: $font-size-base;
      }
      
      &.primary-btn {
        background: linear-gradient(135deg, $primary-color 0%, $primary-dark-color 100%);
        color: $white;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
        
        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }
        
        &.btn-loading {
          opacity: 0.8;
          pointer-events: none;
        }
      }
      
      &.secondary-btn {
        background: $gray-100;
        color: $gray-600;
        border-color: $gray-300;
        
        &:active {
          background: $gray-200;
        }
      }
      
      &.full-width-btn {
        flex: 100%;
      }
    }
  }
}

// 通用状态标签
.flowup-status-badge {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: $font-size-sm;
  font-weight: $font-weight-semibold;
  
  &.status-completed {
    background: $success-light-color;
    color: $success-color;
  }
  
  &.status-pending {
    background: $danger-light-color;
    color: $danger-color;
  }
  
  &.status-in-progress {
    background: $warning-light-color;
    color: $warning-color;
  }
}

// 通用头像组件
.flowup-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
  
  .avatar-text {
    color: $white;
    font-size: 28rpx;
    font-weight: $font-weight-bold;
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .flowup-card-header {
    padding: $spacer-base $spacer-lg;
    
    .card-header-content {
      .header-icon {
        font-size: 28rpx;
      }
      
      .header-title {
        font-size: $font-size-lg;
      }
    }
  }
  
  .flowup-card-content {
    padding: $spacer-lg;
  }
  
  .flowup-section-header {
    padding: $spacer-sm $spacer-base;
    
    .section-icon {
      font-size: 28rpx;
    }
    
    .section-title {
      font-size: $font-size-base;
    }
  }
  
  .flowup-info-item {
    margin-bottom: $spacer-sm;
    
    .info-label {
      font-size: $font-size-sm;
    }
    
    .info-value {
      padding: $spacer-xs;
      font-size: $font-size-sm;
      
      &.textarea-value {
        min-height: 100rpx;
      }
    }
  }
  
  .flowup-action-buttons {
    padding: $spacer-sm $spacer-xs;
    
    .action-row {
      gap: $spacer-xs;
      
      .action-btn {
        height: 80rpx;
        
        .btn-icon {
          font-size: 24rpx;
        }
        
        .btn-text {
          font-size: $font-size-sm;
        }
      }
    }
  }
}