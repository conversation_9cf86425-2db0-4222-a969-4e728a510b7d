<template>
    <view class="questionnaire-record-list-container">
        <!-- 顶部搜索和筛选区域 -->
        <view class="header-section">
            <view class="search-card">
                <view class="search-input-wrapper">
                    <view class="search-icon">🔍</view>
                    <input v-model="listQuery.keyword" placeholder="请输入关键词搜索" class="search-input" @input="search" />
                    <view class="clear-btn" v-if="listQuery.keyword" @click="clearSearch">✕</view>
                </view>
            </view>
        </view>

        <!-- 列表内容 -->
        <view class="list-section">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="record-list">
                    <view class="record-card" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
                        <view class="card-header">
                            <view class="patient-info">
                                <view class="questionnaire-details">
                                    <view class="questionnaire-title">{{
                                        item.questionnaireTitle || "未命名问卷"
                                    }}</view>
                                    <view class="questionnaire-desc">{{
                                        item.questionnaireTime
                                    }}</view>
                                </view>
                            </view>
                            <view class="record-status" :class="getStatusClass(item)">
                                {{ getStatusText(item) }}
                            </view>
                        </view>
                    </view>

                    <!-- 空状态 -->
                    <view class="empty-state" v-if="list.length === 0 && !loading">
                        <view class="empty-icon">📋</view>
                        <text class="empty-text">暂无问卷记录</text>
                        <text class="empty-desc">完成问卷后将在此显示</text>
                    </view>
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>

<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getList } from "@/api/flow-up/questionnaireRecord";

export default {
    mixins: [MescrollMixin],
    components: {},
    data() {
        return {
            isAuthority: true,
            icon: resources.message.nodata,
            searchForm: {
                name: undefined,
            },
            downOption: {
                use: true,
                auto: false,
            },
            dataOptions: {},
            upOption: {
                page: {
                    num: 0,
                    size: 10,
                    time: null,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无数据",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "没有更多数据",
                toTop: {
                    bottom: 250,
                },
            },
            list: [],
            appColumnList: [
                {
                    prop: "questionnaireTitle",
                    label: "问卷标题",
                },
                {
                    prop: "patientName",
                    label: "问卷患者",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "isAnswered",
                    label: "是否填写",
                    width: 100,
                    align: "center",
                    sort: true,
                },
            ],
            listQuery: {
                moduleId: "661876504619124229",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [],
            ableAll: {},
            interfaceRes: {
                name: [],
            },
            menuId: "",
            columnList: [],
            key: new Date(),
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            loading: false,
        };
    },
    computed: {},
    onLoad(e) {
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {
        // 获取状态文本
        getStatusText(item) {
            if (item.isAnswered) return "已填写";
            return "未填写";
        },
        // 获取状态样式类
        getStatusClass(item) {
            if (item.isAnswered) return "status-completed";
            return "status-pending";
        },

        // 清空搜索
        clearSearch() {
            this.listQuery.keyword = "";
            this.search();
        },
        openData(e) { },
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },

        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }
            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                myFlag: true,
            };
            getList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    const list = _list.map((o) => ({
                        show: false,
                        ...o,
                    }));
                    this.list = this.list.concat(_list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        search() {
            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },
        goDetail(item) {
            let id = item.id;
            let btnList = [];
            btnList.push("btn_detail");
            if (!item.isAnswered) {
                btnList.push("btn_fill_out");
            }
            if (btnList.length == 0) return;
            let idVal = id ? "&id=" + id : "";
            uni.navigateTo({
                url:
                    "./doQuestionnaireRecord?menuId=" +
                    this.menuId +
                    "&btnList=" +
                    btnList +
                    idVal
            });
        },
    },
};
</script>

<style lang="scss">
page {
    background-color: #f0f2f6;
    height: 100%;
    /* #ifdef MP-ALIPAY */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    /* #endif */
}

.questionnaire-record-list-container {
    min-height: 100%;
    background-color: #f0f2f6;
    padding-bottom: 20rpx;

    // 头部区域
    .header-section {
        padding: 24rpx;
        background-color: #fff;
        position: sticky;
        top: 0;
        z-index: 99;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

        .search-card {
            background: #f8f9fa;
            border-radius: 16rpx;
            padding: 16rpx 24rpx;
            margin-bottom: 24rpx;

            .search-input-wrapper {
                display: flex;
                align-items: center;
                gap: 16rpx;

                .search-icon {
                    font-size: 32rpx;
                    color: #999;
                }

                .search-input {
                    flex: 1;
                    height: 64rpx;
                    font-size: 28rpx;
                    background: transparent;
                    border: none;
                    outline: none;
                }

                .clear-btn {
                    width: 36rpx;
                    height: 36rpx;
                    border-radius: 50%;
                    background: #ddd;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 24rpx;
                    color: #999;
                }
            }
        }
    }

    // 列表区域
    .list-section {
        padding: 0 24rpx;

        .record-list {

            // 记录卡片
            .record-card {
                background: #fff;
                border-radius: 16rpx;
                padding: 24rpx;
                margin-bottom: 24rpx;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.99);
                    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
                }

                .card-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-bottom: 24rpx;
                    border-bottom: 2rpx solid #f0f0f0;
                    margin-bottom: 24rpx;

                    .patient-info {
                        display: flex;
                        align-items: center;
                        gap: 16rpx;

                        .questionnaire-details {
                            .questionnaire-title {
                                font-size: 32rpx;
                                color: #333;
                                font-weight: 600;
                                margin-bottom: 8rpx;
                            }

                            .questionnaire-desc {
                                font-size: 24rpx;
                                color: #999;
                            }
                        }
                    }

                    .record-status {
                        padding: 8rpx 16rpx;
                        border-radius: 8rpx;
                        font-size: 24rpx;
                        font-weight: 500;

                        &.status-completed {
                            background: #e8f5e9;
                            color: #4caf50;
                        }

                        &.status-pending {
                            background: #ffebee;
                            color: #f44336;
                        }
                    }
                }
            }

            // 空状态
            .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                padding: 120rpx 40rpx;
                text-align: center;

                .empty-icon {
                    font-size: 120rpx;
                    color: #e0e0e0;
                    margin-bottom: 24rpx;
                }

                .empty-text {
                    font-size: 32rpx;
                    color: #666;
                    font-weight: 600;
                    margin-bottom: 12rpx;
                }

                .empty-desc {
                    font-size: 26rpx;
                    color: #999;
                    line-height: 1.5;
                }
            }
        }
    }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
    .header-section {
        padding: 16rpx;

        .search-card {
            padding: 12rpx 16rpx;

            .search-input-wrapper {
                gap: 12rpx;

                .search-icon {
                    font-size: 28rpx;
                }

                .search-input {
                    font-size: 26rpx;
                }
            }
        }
    }

    .list-section {
        padding: 16rpx;

        .record-list {
            .record-card {
                padding: 20rpx;

                .card-header {
                    .patient-info {
                        gap: 12rpx;

                        .questionnaire-details {
                            .questionnaire-title {
                                font-size: 28rpx;
                            }

                            .questionnaire-desc {
                                font-size: 22rpx;
                            }
                        }
                    }

                    .record-status {
                        padding: 6rpx 12rpx;
                        font-size: 20rpx;
                    }
                }
            }
        }
    }
}
</style>
