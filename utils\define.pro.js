/* process.env.NODE_ENV设置生产环境模式 */
// #ifndef MP
const baseURL = process.env.NODE_ENV === "production" ? "https://flowup.api.gzbtrj.com" : "https://flowup.api.gzbtrj.com"
const webSocketUrl = process.env.NODE_ENV === "production" ? "wss://flowup.api.gzbtrj.com/api/message/websocket" :
	"wss://flowup.api.gzbtrj.com/api/message/websocket"
const report = process.env.NODE_ENV === 'production' ? 'https://flowup.api.gzbtrj.com/Report' : 'https://flowup.api.gzbtrj.com/Report'
const flow = process.env.NODE_ENV === 'production' ? 'https://flowup.api.gzbtrj.com' : 'https://flowup.api.gzbtrj.com/'
// #endif

// #ifdef MP
const baseURL = "https://flowup.api.gzbtrj.com"
const webSocketUrl = "wss://flowup.api.gzbtrj.com/api/message/websocket"
const report = 'https://flowup.api.gzbtrj.com'
const flow = 'https://flowup.api.gzbtrj.com/'
// #endif

const define = {
	copyright: "Copyright @ 2025 兴义市人民医院神经外科",
	sysVersion: "V1.0.1",
	baseURL, // 接口前缀
	report,
	flow,
	webSocketUrl,
	comUploadUrl: baseURL + '/api/file/Uploader/',
	timeout: 1000000,
	aMapWebKey: '44afb8705fd4a1d0ee2d44d5a175f0cd',
	cipherKey: 'EY8WePvjM5GGwQzn', // 加密key
}
export default define