
import {
    getDictionaryDataSelector,
    getDataInterfaceRes
} from '@/api/common'
import {
    useBaseStore
} from '@/store/modules/base'

export const columnList = [
    {
        label: '姓名',
        prop: 'name',
        key: 'name',
    },
    {
        label: '性别',
        prop: 'sex',
        key: 'sex',
    },
    {
        label: '科室',
        prop: 'department',
        key: 'department',
    }
];



export function getPhysicianAllDictionaryData(record) {

    // 性别
    getDictionaryDataSelector('sex').then(res => {
        record.sexOptions = res.data.list;
    });

    // 民族
    getDictionaryDataSelector('Nation').then(res => {
        record.nationOptions = res.data.list;
    });

    // 党派
    getDictionaryDataSelector('politicalGroupOptions').then(res => {
        record.politicalGroupOptions = res.data.list;
    });

    // 单位任职
    getDictionaryDataSelector('positionOptions').then(res => {
        record.positionOptions = res.data.list;
    });

    // 医师级别
    getDictionaryDataSelector('medicallevelOptions').then(res => {
        record.medicalLevelOptions = res.data.list;
    });

    // 职业状态
    getDictionaryDataSelector('vocationalstatusOptions').then(res => {
        record.vocationalStatusOptions = res.data.list;
    });

    // 从业类型
    getDictionaryDataSelector('workTypeOptions').then(res => {
        record.workTypeOptions = res.data.list;
    });

    // 专业方向
    getDictionaryDataSelector('majorField').then(res => {
        record.majorOptions = res.data.list;
    });

    // 学历
    getDictionaryDataSelector('educationLevel').then(res => {
        record.educationLevelOptions = res.data.list;
    });

    // 学位
    getDictionaryDataSelector('degree').then(res => {
        record.degreeOptions = res.data.list;
    });
}