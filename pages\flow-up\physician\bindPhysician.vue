<template>
  <view class="physician-container">
    <!-- 医师信息卡片 -->
    <view class="physician-card">
      <view class="physician-header">
        <image
          class="physician-avatar"
          :src="avatar || '/static/image/default-physician.png'"
        ></image>
        <view class="physician-basic-info">
          <view class="physician-name">{{
            physicianInfo.name || "未绑定医师"
          }}</view>
          <view class="physician-title">{{
            physicianInfo.title || "暂无职称"
          }}</view>
          <view class="physician-department">{{
            physicianInfo.department || "暂无科室"
          }}</view>
        </view>
      </view>
      <view class="physician-contact" v-if="physicianInfo.phone">
        <view class="contact-item" @click="makePhoneCall">
          <image class="contact-icon" src="/static/image/phone.png"></image>
          <text class="contact-text">电话咨询：{{ physicianInfo.phone }}</text>
        </view>
        <view class="contact-item" @click="sendMessage">
          <image class="contact-icon" src="/static/image/message.png"></image>
          <text class="contact-text">发送消息</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button
          class="bind-button"
          :class="{ unbind: isBound }"
          @click="handleBindAction"
        >
          {{ isBound ? "撤销绑定" : "绑定医师" }}
        </button>
      </view>
    </view>

    <!-- 医师详情 -->
    <view class="physician-details">
      <view class="details-title">医师简介</view>
      <view class="details-content">
        {{ physicianInfo.remarks || "暂无医师简介" }}
      </view>
    </view>

    <!-- 出诊信息 -->
    <view class="schedule-info" v-if="false">
      <view class="details-title">出诊信息</view>
      <view class="schedule-list">
        <view
          class="schedule-item"
          v-for="(item, index) in scheduleList"
          :key="index"
        >
          <view class="schedule-day">{{ item.day }}</view>
          <view class="schedule-time">{{ item.time }}</view>
          <view class="schedule-location">{{ item.location }}</view>
        </view>
      </view>
    </view>

    <!-- 患者评价 -->
    <view class="patient-reviews" v-if="false">
      <view class="details-title">患者评价</view>
      <view class="review-list">
        <view
          class="review-item"
          v-for="(review, index) in reviewList"
          :key="index"
        >
          <view class="review-header">
            <image
              class="review-avatar"
              :src="review.avatar || '/static/image/default-avatar.png'"
            ></image>
            <view class="review-info">
              <view class="review-name">{{ review.name }}</view>
              <view class="review-date">{{ review.date }}</view>
            </view>
            <view class="review-rating">
              <image
                class="star-icon"
                src="/static/image/star-full.png"
                v-for="i in review.rating"
                :key="i"
              ></image>
              <image
                class="star-icon"
                src="/static/image/star-empty.png"
                v-for="i in 5 - review.rating"
                :key="'empty' + i"
              ></image>
            </view>
          </view>
          <view class="review-content">{{ review.content }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import {
  getDetailAsync,
  getMyPhysicianAsync,
  removeBindAsync,
  bindPhysicianAsync,
} from "@/api/flow-up/physician";
import { useChatStore } from "@/store/modules/chat";

export default {
  data() {
    return {
      isBound: false, // 是否已绑定

      physicianInfo: {
        id: "",
        name: "",
        title: "",
        department: "",
        avatar: "/static/image/physician-avatar.png",
        phone: "",
        remarks: "",
      },
      scheduleList: [],
      reviewList: [],
      avatar: "/static/image/physician-avatar.png",
      // 其他数据...
    };
  },
  onLoad(options) {
    console.log(options);
    // 可以根据传入的医生ID获取医生详细信息
    const physicianId = options.id;
    this.physicianInfo = null;
    if (physicianId) {
      var id = this.xunda.base64.decode(physicianId);
      this.getphysicianDetail(id);
    } else {
      this.getMyPhysician();
    }
  },
  methods: {
    // 获取医生详细信息
    getphysicianDetail(physicianId) {
      getDetailAsync(physicianId).then((res) => {
        this.physicianInfo = res.data;
        if (res.data.picture) {
          this.avatar = this.define.baseURL + res.data.picture;
        }
      });
      // 这里可以调用API获取医生详细信息
      console.log("获取医生信息，ID:", physicianId);
    },
    getMyPhysician() {
      getMyPhysicianAsync()
        .then((res) => {
          this.physicianInfo = res.data;
          if (res.data.picture) {
            this.avatar = this.define.baseURL + res.data.picture;
          }
          // 如果有医师信息，说明已绑定
          this.isBound = !!res.data;
        })
        .catch((err) => {
          console.error(err);
          this.isBound = false;
        });
    },
    // 拨打电话
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: this.physicianInfo.phone,
        fail: () => {
          uni.showToast({
            title: "拨打电话失败",
            icon: "none",
          });
        },
      });
    },
    // 发送消息
    sendMessage() {
      const item = this.physicianInfo;
      const name = item.name;
      if (!item.userId) {
        uni.showToast({
          title: "暂无医生联系方式",
          icon: "none",
        });
      }
      uni.navigateTo({
        url:
          "/pages/message/im/index?name=" +
          name +
          "&formUserId=" +
          item.userId +
          "&headIcon=" +
          item.picture,
      });
    },

    // 处理绑定/解绑操作
    async handleBindAction() {
      if (this.isBound) {
        // 撤销绑定
        uni.showModal({
          title: "提示",
          content: "确定要撤销绑定该医师吗？",
          success: async (res) => {
            if (res.confirm) {
              try {
                removeBindAsync({
                  physicianId: this.physicianInfo.id,
                }).then((res) => {
                  // TODO: 调用解绑 API
                  uni.showToast({
                    title: "解绑成功",
                    icon: "success",
                  });
                  this.isBound = false;
                  // 返回上一页并刷新
                  this.backAndRefresh();
                });
              } catch (error) {
                uni.showToast({
                  title: "解绑失败",
                  icon: "none",
                });
              }
            }
          },
        });
      } else {
        // 绑定医师
        uni.showModal({
          title: "提示",
          content: "确定要绑定该医师吗？",
          success: async (res) => {
            if (res.confirm) {
              try {
                bindPhysicianAsync({
                  physicianId: this.physicianInfo.id,
                }).then((res) => {
                  // TODO: 调用绑定 API
                  uni.showToast({
                    title: "绑定成功",
                    icon: "success",
                  });
                  this.isBound = true;
                  // 返回上一页并刷新
                  this.backAndRefresh();
                });
              } catch (error) {
                uni.showToast({
                  title: "绑定失败",
                  icon: "none",
                });
              }
            }
          },
        });
      }
    },

    // 返回上一页并刷新
    backAndRefresh() {
      const pages = getCurrentPages();
      const prevPage = pages[pages.length - 2];
      if (prevPage && prevPage.$vm.$refs.patientRef) {
        prevPage.$vm.$refs.patientRef.loadMyInfo();
      }
      setTimeout(() => {
        uni.navigateBack();
      }, 1000);
    },
  },
};
</script>

<style lang="scss" scoped>
.physician-container {
  padding: 20rpx;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.physician-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.action-buttons {
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.bind-button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #4a86e8;
  color: #ffffff;
  border-radius: 40rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  border: none;

  &.unbind {
    background: #f56c6c;
  }

  &:active {
    transform: scale(0.98);
    opacity: 0.9;
  }
}

.physician-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.physician-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  background-color: #f0f0f0;
}

.physician-basic-info {
  flex: 1;
}

.physician-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.physician-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.physician-department {
  font-size: 26rpx;
  color: #999;
}

.physician-contact {
  display: flex;
  justify-content: space-around;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.contact-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.contact-text {
  font-size: 24rpx;
  color: #4a86e8;
}

.physician-details {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.details-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.details-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.schedule-info {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.schedule-list {
  margin-top: 20rpx;
}

.schedule-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.schedule-item:last-child {
  border-bottom: none;
}

.schedule-day {
  width: 100rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.schedule-time {
  flex: 1;
  font-size: 26rpx;
  color: #666;
  padding-left: 20rpx;
}

.schedule-location {
  font-size: 26rpx;
  color: #999;
  padding-left: 20rpx;
}

.patient-reviews {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
}

.review-list {
  margin-top: 20rpx;
}

.review-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.review-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 15rpx;
  background-color: #f0f0f0;
}

.review-info {
  flex: 1;
}

.review-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.review-date {
  font-size: 24rpx;
  color: #999;
  margin-top: 5rpx;
}

.review-rating {
  display: flex;
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
  margin-left: 5rpx;
}

.review-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 75rpx;
}
</style>
