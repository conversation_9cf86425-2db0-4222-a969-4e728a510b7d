{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^Xunda(.*)": "@/components/Xunda/$1/index.vue",
			"^xunda-(.*)": "@/components/Xunda/$1/index.vue"
		}
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/launch/index",
			"style": {
				"app-plus": {
					"titleNView": false,
					"bounce": "none"
				}
			}
		},
		// #ifdef APP-PLUS
		{
			"path": "pages/launch/policy",
			"style": {
				"navigationBarTitleText": "",
				"app-plus": {
					"titleNView": false,
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/launch/guide",
			"style": {
				"navigationBarTitleText": "",
				"app-plus": {
					"titleNView": false,
					"bounce": "none"
				}
			}
		},
		// #endif
		{
			"path": "pages/login/index",
			"style": {
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"bounce": "none"
				}
			}
		},
		{
			"path": "pages/login/sso-redirect",
			"style": {
				"navigationStyle": "custom", // 隐藏系统导航栏
				"navigationBarTextStyle": "black" // 状态栏字体为白色
			}
		},
		{
			"path": "pages/login/scanLogin",
			"style": {
				"navigationBarTitleText": "扫码登录"
			}
		},
		{
			"path": "pages/login/otherLogin",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTextStyle": "black",
				"app-plus": {
					"bounce": "none" // 取消APP端iOS回弹,避免与下拉刷新冲突 (可统一配在 'globalStyle')
				}
			}
		},
		{
			"path": "pages/index/news",
			"style": {
				"navigationBarTitleText": "健康宣教"
			}
		},
		{
			"path": "pages/index/message",
			"style": {
				"navigationBarTitleText": "消息"
			}
		},
		{
			"path": "pages/index/my",
			"style": {
				"navigationBarTitleText": "我的",
				"app-plus": {
					"bounce": "none"
				}
			}
		}
	],
	"subPackages": [
		{
			"root": "pages/message",
			"pages": [
				{
					"path": "contacts/index",
					"style": {
						"navigationBarTitleText": "通讯录"
					}
				},
				{
					"path": "userDetail/index",
					"style": {
						"navigationBarTitleText": "",
						"navigationBarTextStyle": "black",
						"navigationBarBackgroundColor": "#f0f2f6"
					}
				},
				{
					"path": "message/index",
					"style": {
						"navigationBarTitleText": "站内消息",
						"navigationStyle": "custom", // 隐藏系统导航栏
						"navigationBarTextStyle": "white" // 状态栏字体为白色
					}
				},
				{
					"path": "messageDetail/index",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "im/index",
					"style": {
						"navigationBarTitleText": ""
					}
				}
			]
		},
		{
			"root": "pages/apply",
			"pages": [
				{
					"path": "location/index",
					"style": {
						"navigationBarTitleText": "选择位置",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/my",
			"pages": [
				{
					"path": "settings/index",
					"style": {
						"navigationBarTitleText": "设置",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "scanResult/index",
					"style": {
						"navigationBarTitleText": "扫描结果",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "modifyPsd/index",
					"style": {
						"navigationBarTitleText": "修改密码",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "contactUs/index",
					"style": {
						"navigationBarTitleText": "联系我们",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "abouts/index",
					"style": {
						"navigationBarTitleText": "关于平台"
					}
				},
				{
					"path": "accountSecurity/index",
					"style": {
						"navigationBarTitleText": "账号安全",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "cancellation/index",
					"style": {
						"navigationBarTitleText": "注销账号",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "personalData/index",
					"style": {
						"navigationBarTitleText": "个人信息",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "business/index",
					"style": {
						"navigationBarTitleText": "企业组织",
						"app-plus": {
							"bounce": "none"
						}
					}
				},
				{
					"path": "subordinate/index",
					"style": {
						"navigationBarTitleText": "我的下属",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "pages/flow-up",
			"pages": [
				{
					"path": "patient/form",
					"style": {
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "patient/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "physician/myPatient",
					"style": {
						"navigationBarTitleText": "我的患者"
					}
				},
				{
					"path": "physician/index",
					"style": {
						"navigationBarTitleText": "随访医师"
					}
				},
				{
					"path": "physician/bindPhysician",
					"style": {
						"navigationBarTitleText": "绑定医师"
					}
				},
				{
					"path": "physician/detail",
					"style": {
						"navigationBarTitleText": "详情"
					}
				},
				{
					"path": "physician/form",
					"style": {
						"navigationBarTitleText": "修改"
					}
				},
				{
					"path": "doVisitRecord",
					"style": {
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "questionnaireRecord/myQuestionnaireRecord",
					"style": {
						"navigationBarTitleText": "我的问卷",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "questionnaireRecord/doQuestionnaireRecord",
					"style": {
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "myPhysician",
					"style": {
						"navigationBarTitleText": "我的医生",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "myVisitRecord",
					"style": {
						"navigationBarTitleText": "我的随访",
						"navigationBarTextStyle": "black"
					}
				},
				{
					"path": "myQrCode",
					"style": {
						"navigationBarTitleText": "我的就诊码",
						"navigationBarTextStyle": "black"
					}
				}
			]
		},
		{
			"root": "pages/news",
			"pages": [
				{
					"path": "detail",
					"style": {
						"navigationBarTitleText": "新闻详情"
					}
				},
				{
					"path": "video",
					"style": {
						"navigationBarTitleText": "视频播放"
					}
				}
			]
		}
	],
	"preloadRule": {
		"pages/index/message": {
			"network": "all",
			"packages": [
				"pages/message"
			]
		},
		"pages/index/my": {
			"network": "all",
			"packages": [
				"pages/my"
			]
		}
	},
	"tabBar": {
		"color": "#303133",
		"selectedColor": "#2979FF",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/image/tabbar/home.png",
				"selectedIconPath": "static/image/tabbar/homeHL.png"
			},
			{
				"pagePath": "pages/index/news",
				"text": "资讯",
				"iconPath": "static/image/tabbar/news.png",
				"selectedIconPath": "static/image/tabbar/newsHL.png"
			},
			{
				"pagePath": "pages/index/message",
				"text": "消息",
				"iconPath": "static/image/tabbar/message.png",
				"selectedIconPath": "static/image/tabbar/messageHL.png"
			},
			{
				"pagePath": "pages/index/my",
				"text": "我的",
				"iconPath": "static/image/tabbar/my.png",
				"selectedIconPath": "static/image/tabbar/myHL.png"
			}
		]
	},
	"globalStyle": {
		"navigationBarTitleText": "医疗随访",
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#ffffff",
		"backgroundColor": "#f0f2f6",
		"usingComponents": {
			"ly-tree-node": "/components/ly-tree/ly-tree-node",
			"m-video": "plugin://m-video/m-video"
		}
	},
	"uniIdRouter": {}
}