<template>
	<view class="personalData-v">
		<view class="notice-warp">
			<u-tabs :list="tabBars" :is-scroll="false" v-model="current" @change="tabChange" height="100">
			</u-tabs>
		</view>
		<view class="content">
			<accountData ref="accountData" v-if="current == 0" :accountData="baseInfo"></accountData>
			<PatienData ref="PatienData" v-if="current == 1 && baseInfo.roleId === '患者'" :personalData="baseInfo"></PatienData>
			<PhysicianData ref="PhysicianData" v-if="current == 1 && baseInfo.roleId === '医师'" :personalData="baseInfo"></PhysicianData>
		</view>
	</view>
</template>
<script>
import accountData from './components/accountInformation.vue'; 
import PatienData from './components/PatienData.vue';
import PhysicianData from './components/PhysicianData.vue';

export default {
	components: {
		accountData,
		PatienData,
		PhysicianData
	},
	data() {
		return {
			tabBars: [{
				name: '账户信息'
			}],
			current: 0,
			baseInfo: {}
		};
	},
	onLoad(e) {
		this.baseInfo = JSON.parse(decodeURIComponent(e.baseInfo))
		if (this.baseInfo.roleId === '患者') {
			this.tabBars.push({
				name: '患者资料'
			})
		}
		if (this.baseInfo.roleId === '医师') {
			this.tabBars.push({
				name: '医师资料'
			})
		}
	},
	methods: {
		tabChange(index) {
			this.current = index;
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f0f2f6;
	height: 100%;
}

.notice-warp {
	height: 100rpx;

	.search-box {
		padding: 20rpx;
	}
}

.content {
	margin-top: 120rpx;
}

.personalData-v {
	display: flex;
	flex-direction: column;
	padding-bottom: 100rpx;

	::v-deep .buttom-btn {
		width: 100% !important;
	}
}
</style>