

import request from "@/utils/request";

/**
 * 获取患者列表
 * @param {*} query 
 * @returns 
 */
export const getList = function (query) {
    return request({
        url: "/api/flowUp/questionnaireRecord/getList",
        method: "post",
        data: query,
    })
}


export const createQuestionnaireRecord = function (data) {
    return request({
        url: "/api/flowUp/questionnaireRecord",
        method: "post",
        data: data,
    })
}



export const updateQuestionnaireRecord = function (data) {
    return request({
        url: "/api/flowUp/questionnaireRecord/" + data.id,
        method: "put",
        data: data,
    })
}

export const doQuestionnaireRecord = function (data) {
    return request({
        url: "/api/flowUp/questionnaireRecord/doQuestionnaireRecord",
        method: "post",
        data: data,
    })
}



export const getForEdit = function (id) {
    return request({
        url: "/api/flowUp/questionnaireRecord/" + id,
        method: "get",
    })
}

/**
 * 获取患者详情
 * @param {*} query
 * @returns
 */
export const getDetail = function (id) {
    return request({
        url: "/api/flowUp/questionnaireRecord/" + "detail/" + id,
        method: "get",
    })
}

/**
 * 删除患者
 * @param {*} id 
 */
export const deleteQuestionnaireRecord = function (id) {
    return request({
        url: "/api/flowUp/questionnaireRecord/" + id,
        method: "delete",
    })
}