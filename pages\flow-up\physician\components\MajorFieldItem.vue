<template>
    <view class="container">
        <view v-if="items && items.length > 0">
            <view class="row" v-for="(item, index) in items" :key="index">
                <view class="left">
                    {{ titleName(index) }}
                </view>
                <view class="right">
                    <XundaSelect v-if="isEdit" v-model="item.name" :fieldKey="'majorField'" clearable placeholder="请选择"
                        :disabled="!isEdit" :props="majorFieldProps" :options="majorFieldOptions">
                    </XundaSelect>
                    <view v-else class="xunda-detail-text">
                        {{ xunda.dictionaryText(item.name, majorFieldOptions) }}
                    </view>
                </view>
                <view class="closeBox u-flex-col" @click.stop="handleDelete(index)" v-if="isEdit">
                    <text class="icon-ym icon-ym-nav-close closeTxt u-flex"></text>
                </view>
            </view>
        </view>
        <view v-else>
            <view v-if="!isEdit" class="empty">
                暂无专业
            </view>
        </view>
        <view v-if="isEdit">
            <u-button class="buttom-btn" type="primary" @click.stop="handelAdd()">添加专业</u-button>
        </view>
    </view>
</template>
<script>
import { buildUUID } from '@/utils/uuid';
import {
    getDictionaryDataSelector
} from '@/api/common';

export default {
    name: 'MajorFieldItem',
    props: {
        items: {
            type: Array,
            default: () => {
                return []
            }
        }, isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            data: {},
            majorFieldOptions: [],
            majorFieldProps: { "label": "fullName", "value": "enCode", "multiple": false, "children": "" },

        }
    },
    mounted() {
        this.data = this.items;
        getDictionaryDataSelector('majorField').then(res => {
            this.majorFieldOptions = res.data.list;
        })
    },
    methods: {
        titleName(index) {
            return `第${this.xunda.getNumberChhinese(index + 1)}专业`
        },
        click() {
            this.$emit('click', this.data)
        }, handelSelect(item) {
            console.log(item.id)
        },
        handelAdd() {
            let items = this.items ? this.items : []
            items.push({
                id: buildUUID(),
                name: "0"
            })
            this.$emit('update:items', items)
        },
        handleDelete(index) {
            uni.showModal({
                title: '提示',
                content: '确定要删除吗？',
                success: (res) => {
                    if (res.confirm) {
                        let items = this.items ? this.items : []
                        items.splice(index, 1)
                        this.$emit('update:items', items)
                    }
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.container {
    margin: 0
}


.empty {
    text-align: center;
    padding: 20px;
    font-size: 16px;
    color: #999;
}


.row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    /* 根据需要调整 */
}

.left,
.right {
    width: 48%;
    border: 10px solid #fff;
    /* 根据实际情况调整宽度 */
}

.left {
    align-self: center;
    text-align: left;
}

.right {
    text-align: right;
}

.arrow {
    margin-left: 5px;
    font-weight: bold;
}


.closeBox {
    height: 60rpx;
    align-items: flex-end;
    justify-content: space-evenly;
    flex: 0.2;

    .closeTxt {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #fa3534;
        color: #FFFFFF;
        font-size: 20rpx;
        align-items: center;
        justify-content: center;
        line-height: 36rpx;
    }
}
</style>