{
    "name": "医疗随访",
    "appid": "__UNI__407BCC6",
    "description": "专注信息化平台、软件开发、app开发",
    "versionName": "1.0.0",
    "versionCode": 100,
    "transformPx": false,
    /* 5+App特有相关 */
    "app-plus": {
        "usingComponents": true,
        "nvueStyleCompiler": "uni-app",
        "compilerVersion": 3,
        "splashscreen": {
            "alwaysShowBeforeRender": false,
            "waiting": true,
            "autoclose": true,
            "delay": 0
        },
        /* 模块配置 */
        "modules": {
            "Barcode": {},
            "Camera": {},
            "Maps": {},
            "OAuth": {},
            "Record": {},
            "VideoPlayer": {},
            "Push": {}
        },
        /* 应用发布信息 */
        "distribute": {
            /* android打包配置 */
            "android": {
                "permissions": [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "schemes": "xunda",
                "minSdkVersion": 22,
                "targetSdkVersion": 26,
                "abiFilters": [
                    "armeabi-v7a",
                    "arm64-v8a"
                ]
            },
            /* ios打包配置 */
            "ios": {
                "privacyDescription": {
                    "NSPhotoLibraryUsageDescription": "以便于修改头像等功能图片上传",
                    "NSPhotoLibraryAddUsageDescription": "以便于修改头像等功能图片上传",
                    "NSCameraUsageDescription": "以便于修改头像等功能图片上传",
                    "NSMicrophoneUsageDescription": "以便于使用语音、录制音频等功能",
                    "NSLocationWhenInUseUsageDescription": "以便于定位当前位置等功能",
                    "NSLocationAlwaysUsageDescription": "以便于定位当前位置等功能",
                    "NSLocationAlwaysAndWhenInUseUsageDescription": "以便于定位当前位置等功能",
                    "NSCalendarsUsageDescription": "以便于更好的办公",
                    "NSContactsUsageDescription": "以便于更好的沟通",
                    "NSAppleMusicUsageDescription": "以便于修改头像等功能媒体资料上传",
                    "NSFaceIDUsageDescription": "以便于使用快捷登录等功能"
                },
                "urltypes": "xunda",
                "capabilities": {
                    "entitlements": {
                        "com.apple.developer.associated-domains": [
                            "applinks:unlink.java.gzbtrj.com"
                        ]
                    }
                },
                "idfa": false,
                "dSYMs": false
            },
            /* SDK配置 */
            "sdkConfigs": {
                "maps": {
                    "amap": {
                        "name": "amapOdpt2hGy",
                        "appkey_ios": "26a65601349a5ec88318721884ef81b5",
                        "appkey_android": "26a65601349a5ec88318721884ef81b5"
                    }
                },
                "oauth": {
                    "weixin": {
                        "appid": "wxd5408a0b1b320373",
                        "UniversalLinks": ""
                    }
                },
                "push": {
                    "unipush": {}
                },
                "ad": {}
            },
            "splashscreen": {
                "androidStyle": "default",
                "android": {
                    "hdpi": "unpackage/res/startup/480x762.png",
                    "xhdpi": "unpackage/res/startup/720x1242.png",
                    "xxhdpi": "unpackage/res/startup/1080x1882.png"
                },
                "iosStyle": "default",
                "ios": {
                    "iphone": {
                        "portrait-896h@3x": "unpackage/res/startup/1242x2688.png",
                        "landscape-896h@3x": "unpackage/res/startup/1242x2688.png",
                        "portrait-896h@2x": "unpackage/res/startup/1242x2688.png",
                        "landscape-896h@2x": "unpackage/res/startup/1242x2688.png",
                        "iphonex": "unpackage/res/startup/startup.png",
                        "iphonexl": "unpackage/res/startup/1242x2688.png",
                        "retina55": "unpackage/res/startup/1242x2688.png",
                        "retina55l": "unpackage/res/startup/1242x2688.png",
                        "retina47": "unpackage/res/startup/1242x2688.png",
                        "retina47l": "unpackage/res/startup/1242x2688.png",
                        "retina40": "unpackage/res/startup/1242x2688.png",
                        "retina40l": "unpackage/res/startup/1242x2688.png",
                        "retina35": "unpackage/res/startup/1242x2688.png"
                    },
                    "ipad": {
                        "portrait-1366h@2x": "",
                        "landscape-1366h@2x": ""
                    }
                }
            },
            "icons": {
                "android": {
                    "hdpi": "unpackage/res/icons/72x72.png",
                    "xhdpi": "unpackage/res/icons/96x96.png",
                    "xxhdpi": "unpackage/res/icons/144x144.png",
                    "xxxhdpi": "unpackage/res/icons/192x192.png"
                },
                "ios": {
                    "appstore": "unpackage/res/icons/1024x1024.png",
                    "ipad": {
                        "app": "unpackage/res/icons/76x76.png",
                        "app@2x": "unpackage/res/icons/152x152.png",
                        "notification": "unpackage/res/icons/20x20.png",
                        "notification@2x": "unpackage/res/icons/40x40.png",
                        "proapp@2x": "unpackage/res/icons/167x167.png",
                        "settings": "unpackage/res/icons/29x29.png",
                        "settings@2x": "unpackage/res/icons/58x58.png",
                        "spotlight": "unpackage/res/icons/40x40.png",
                        "spotlight@2x": "unpackage/res/icons/80x80.png"
                    },
                    "iphone": {
                        "app@2x": "unpackage/res/icons/120x120.png",
                        "app@3x": "unpackage/res/icons/180x180.png",
                        "notification@2x": "unpackage/res/icons/40x40.png",
                        "notification@3x": "unpackage/res/icons/60x60.png",
                        "settings@2x": "unpackage/res/icons/58x58.png",
                        "settings@3x": "unpackage/res/icons/87x87.png",
                        "spotlight@2x": "unpackage/res/icons/80x80.png",
                        "spotlight@3x": "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp": {},
    /* 小程序特有相关 */
    "mp-weixin": {
        "appid": "wx1bbe55baa674d016",
        "setting": {
            "urlCheck": true,
            "es6": true,
            "postcss": true,
            "minified": true
        },
        "plugins": {
            "m-video": {
                "version": "latest",
                "provider": "wxd4709e80d42cb833"
            }
        },
        "usingComponents": true,
        "permission": {
            "scope.userLocation": {
                "desc": "你的位置信息将用于小程序位置接口的效果展示"
            }
        },
        "requiredPrivateInfos": [
            "getLocation",
            "chooseLocation"
        ]
    },
    "mp-alipay": {
        "usingComponents": true
    },
    "mp-baidu": {
        "usingComponents": true
    },
    "mp-toutiao": {
        "usingComponents": true
    },
    "uniStatistics": {
        "enable": false
    },
    "vueVersion": "3",
    "h5": {
        "devServer": {
            "port": 3800
        },
        "title": "xunda java vue3版",
        "router": {
            "mode": "history"
        },
        "optimization": {
            "treeShaking": {
                "enable": true
            }
        },
        "sdkConfigs": {
            "maps": {
                "amap": {
                    "key": "26a65601349a5ec88318721884ef81b5",
                    "securityJsCode": "06c3991ebe0b2994ca4c68c8207be61d",
                    "serviceHost": ""
                }
            }
        }
    }
}