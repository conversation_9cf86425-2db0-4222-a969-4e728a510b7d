<template>
  <view class="index-v">
    <view class="welcome-container" v-if="false">
      <view class="content">
        <view class="content-title">欢迎使用</view>
        <view class="content-desc">医疗随访管理小程序</view>
      </view>
    </view>
    <view v-if="userRole === '患者'">
      <patient ref="patientRef" />
    </view>
    <view v-if="userRole === '医师'">
      <physician />
    </view>
  </view>
</template>
<script>
var wv; //计划创建的webview
import logoImg from "@/static/logo.png";
import { getMyPhysicianAsync, scanBindAsync } from "@/api/flow-up/physician";
// #ifndef MP
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
// #endif
import patient from "@/pages/index/patient.vue";
import physician from "@/pages/index/physician.vue";
export default {
  // #ifndef MP
  mixins: [MescrollMixin, IndexMixin],
  // #endif
  components: {
    patient,
    physician,
  },
  data() {
    return {
      logoImg,
      gridItems: [],
      myPhysician: null,
      avatar: "",
      userRole: "",
    };
  },
  methods: {
    navigateTo(pagePath) {
      uni.navigateTo({
        url: pagePath,
      });
    },
    // 调用子组件方法示例
    refreshPatientInfo() {
      // 先判断组件是否存在
      if (this.$refs.patientRef) {
        this.$refs.patientRef.loadMyInfo();
      }
    },
  },
  onShow() {
    const userInfo = uni.getStorageSync("userInfo") || {};
    const roleName = userInfo.roleName || "";
    this.userRole = roleName;
    // 如果是患者角色，刷新患者信息
    if (this.userRole === "患者") {
      this.$nextTick(() => {
        this.refreshPatientInfo();
      });
    }
  },
  onReady() {},
  onLoad(e) {},
  computed: {},
};
</script>

<style lang="scss">
.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30vh;
  background-color: #f8f9fa;
  margin-top: 20px;
}
</style>
