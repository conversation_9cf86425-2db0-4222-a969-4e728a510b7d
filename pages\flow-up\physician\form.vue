<template>
  <view class="xunda-wrap xunda-wrap-form" v-if="!loading">
    <u-form :model="dataForm" :rules="rules" ref="dataFormRef" :errorType="['toast']" label-position="left"
      label-align="left" :label-width="labelwidth" class="xunda-form">
      <view>
        <u-tabs :is-scroll="false" :list="activetabData" name="title" v-model="activetab" @change="clickActivetab" />
        <view>
          <view v-show="0 == activetab">
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item required label="姓名" prop="name">
                <XundaInput v-model="dataForm.name" :fieldKey="'name'" input-align='right' clearable placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item required label="性别" prop="sex">
                <XundaSelect v-model="dataForm.sex" :fieldKey="'sex'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.sexOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="照片" prop="picture">
                <XundaInput disabled>
                </XundaInput>
                <u-avatar size="127" @click="chooseAvatar" :src="avatarSrc"></u-avatar>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item required label="出生日期" prop="birthdate">
                <XundaDatePicker v-model="dataForm.birthdate" :fieldKey="'birthdate'" format="yyyy-MM-dd" clearable
                  placeholder="请选择" type="date">
                </XundaDatePicker>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="民族" prop="nation">
                <XundaSelect v-model="dataForm.nation" :fieldKey="'nation'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.nationOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="党派" prop="politicalGroup">
                <XundaInput v-model="dataForm.politicalGroup" :fieldKey="'politicalGroup'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="籍贯" prop="nativePlace">
                <XundaInput v-model="dataForm.nativePlace" :fieldKey="'nativePlace'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="手机号" prop="phone">
                <XundaInput v-model="dataForm.phone" :fieldKey="'phone'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="身份证号" prop="idCard">
                <XundaInput v-model="dataForm.idCard" :fieldKey="'idCard'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="家庭住址" prop="address">
                <XundaInput v-model="dataForm.address" :fieldKey="'address'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="单位任职（职务）" prop="position">
                <XundaInput v-model="dataForm.position" :fieldKey="'position'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>

            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="医师级别" prop="medicalLevel">
                <XundaInput v-model="dataForm.medicalLevel" :fieldKey="'medicalLevel'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="职业状况" prop="vocationalStatus">
                <XundaInput v-model="dataForm.vocationalStatus" :fieldKey="'vocationalStatus'" input-align='right'
                  clearable placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="执业证书编号" prop="certificateNo">
                <XundaInput v-model="dataForm.certificateNo" :fieldKey="'certificateNo'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="执业证书" prop="certificate">
                <XundaUploadFile v-model="dataForm.certificate" :fieldKey="'certificate'" :list="dataForm.certificate"
                  sizeUnit="MB" :fileSize="10" :limit="9" pathType="defaultPath" timeFormat="YYYY">
                </XundaUploadFile>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="资格证书编号" prop="qualificationCertificateNo">
                <XundaInput v-model="dataForm.qualificationCertificateNo" :fieldKey="'qualificationCertificateNo'"
                  input-align='right' clearable placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="资格证书" prop="qualificationCertificate">
                <XundaUploadFile v-model="dataForm.qualificationCertificate" :fieldKey="'qualificationCertificate'"
                  :list="dataForm.qualificationCertificate" sizeUnit="MB" :fileSize="10" :limit="9"
                  pathType="defaultPath" timeFormat="YYYY">
                </XundaUploadFile>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="工作证书编号" prop="workCertificateNo">
                <XundaInput v-model="dataForm.workCertificateNo" :fieldKey="'workCertificateNo'" input-align='right'
                  clearable placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="工作证书" prop="workCertificate">
                <XundaUploadFile v-model="dataForm.workCertificate" :fieldKey="'workCertificate'"
                  :list="dataForm.workCertificate" sizeUnit="MB" :fileSize="10" :limit="9" pathType="defaultPath"
                  timeFormat="YYYY">
                </XundaUploadFile>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="技术职称" prop="technicalTitle">
              </u-form-item>
              <view class="xunda-card">
                <TechnicalTitleItem v-model:items="dataForm.technicalTitle" :isEdit="true"></TechnicalTitleItem>
              </view>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="备注说明" prop="remarks">
                <XundaTextarea v-model="dataForm.remarks" :fieldKey="'remarks'" input-align='right' clearable
                  placeholder="请输入" type="textarea">
                </XundaTextarea>
              </u-form-item>
            </view>
          </view>
          <view v-show="1 == activetab">
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="毕业学校" prop="graduationSchool">
                <XundaInput v-model="dataForm.graduationSchool" :fieldKey="'graduationSchool'" input-align='right'
                  clearable placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="毕业时间" prop="graduationTime">
                <XundaDatePicker v-model="dataForm.graduationTime" :fieldKey="'graduationTime'" format="yyyy-MM-dd"
                  clearable placeholder="请选择" type="date">
                </XundaDatePicker>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="学历" prop="educationLevel">
                <XundaSelect v-model="dataForm.educationLevel" :fieldKey="'educationLevel'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.educationLevelOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="学位" prop="degree">
                <XundaSelect v-model="dataForm.degree" :fieldKey="'degree'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.degreeOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="专业" prop="major">
                <XundaSelect v-model="dataForm.major" :fieldKey="'major'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.majorOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="从业时间" prop="workExperienceYears">
                <XundaDatePicker v-model="dataForm.workExperienceYears" :fieldKey="'workExperienceYears'"
                  format="yyyy-MM-dd" clearable placeholder="请选择" type="date">
                </XundaDatePicker>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="从业类型" prop="workType">
                <XundaSelect v-model="dataForm.workType" :fieldKey="'workType'" clearable placeholder="请选择"
                  :props="optionsObj.defaultProps" :options="optionsObj.workTypeOptions">
                </XundaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="专业方向" prop="majorField">
              </u-form-item>
              <view class="xunda-card">
                <MajorFieldItem v-model:items="dataForm.majorField" :isEdit="true"></MajorFieldItem>
              </view>
            </view>
          </view>
          <view v-show="2 == activetab">
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="省份" prop="provinceAndCity">
                <XundaAreaSelect v-model="dataForm.provinceAndCity" :fieldKey="'provinceAndCity'" clearable
                  placeholder="请选择" :level="1">
                </XundaAreaSelect>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="医院名称" prop="hospitalName">
                <XundaInput v-model="dataForm.hospitalName" :fieldKey="'hospitalName'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="科室" prop="department">
                <XundaInput v-model="dataForm.department" :fieldKey="'department'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item label="科室名称" prop="departmentName">
                <XundaInput v-model="dataForm.departmentName" :fieldKey="'departmentName'" input-align='right' clearable
                  placeholder="请输入">
                </XundaInput>
              </u-form-item>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <WorkHistoryItem v-model:items="dataForm.workHistory" :isEdit="true"></WorkHistoryItem>
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <AcademicPositionItem v-model:items="dataForm.academicPosition" :isEdit="true"></AcademicPositionItem>
            </view>
          </view>
        </view>
      </view>
    </u-form>
    <view class="buttom-actions">
      <u-button class="buttom-btn" type="primary" @click="handleSubmit" :loading="btnLoading">确定</u-button>
      <u-button class="buttom-btn" @click="resetForm">取消</u-button>
    </view>
  </view>
  <u-modal v-model="show" :content="content" width="70%" border-radius="16" :content-style="{
    fontSize: '28rpx',
    padding: '20rpx',
    lineHeight: '44rpx',
    textAlign: 'left',
  }" :titleStyle="{ padding: '20rpx' }" :confirm-style="{ height: '80rpx', lineHeight: '80rpx' }" :title="title"
    confirm-text="确定">
  </u-modal>

</template>

<script>

import { getForEdit, createPhysician, updatePhysician } from '@/pages/flow-up/physician/api'
import { getPhysicianAllDictionaryData } from "@/pages/flow-up/physician/index.js";
import TechnicalTitleItem from '@/pages/flow-up/physician/components/TechnicalTitleItem.vue'
import MajorFieldItem from '@/pages/flow-up/physician/components/MajorFieldItem.vue'
import AcademicPositionItem from '@/pages/flow-up/physician/components/AcademicPositionItem.vue'
import WorkHistoryItem from '@/pages/flow-up/physician/components/WorkHistoryItem.vue'
export default {
  components: {
    TechnicalTitleItem,
    MajorFieldItem,
    AcademicPositionItem,
    WorkHistoryItem,
  },
  data() {
    return {
      idList: [],
      index: 0,
      actionList: [],
      actionListLength: false,
      showAction: false,
      btnLoading: false,
      loading: false,
      text: '提示：测试文本',
      tableKey: '',
      timeKey: +new Date(),
      dataForm: {
        id: "",
        name: undefined,
        sex: '',
        picture: undefined,
        phone: undefined,
        nativePlace: undefined,
        idCard: undefined,
        birthdate: undefined,
        nation: '',
        politicalGroup: undefined,
        address: undefined,
        position: undefined,
        technicalTitle: undefined,
        vocationalStatus: undefined,
        certificate: [],
        qualificationCertificate: [],
        workCertificate: [],
        graduationSchool: undefined,
        graduationTime: undefined,
        major: undefined,
        degree: undefined,
        educationLevel: undefined,
        workType: undefined,
        workExperienceYears: undefined,
        majorField: undefined,
        remarks: undefined,
        province: undefined,
        provinceAndCity: undefined,
        hospitalName: undefined,
        department: undefined,
        academicPosition: undefined,
        workHistory: undefined,
      },
      rules: {
        name: [
          {
            required: true,
            message: '姓名请输入',
          },
        ],
        sex: [
          {
            required: true,
            message: '性别请选择',
          },
        ],
        phone: [
          {
            pattern: /^1[3456789]\d{9}$|^0\d{2,3}-?\d{7,8}$/,
            message: '手机号请输入正确的联系方式',
          },
        ],
        idCard: [
          {
            pattern: /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            message: '身份证号请输入正确的身份证号码',
          },
        ],
        birthdate: [
          {
            required: true,
            message: '出生日期请选择',
          },
        ],
      },
      activetab: 0,
      activetabData: [
        {
          title: "个人信息"
        },
        {
          title: "专业方向"
        },
        {
          title: "工作经历"
        },
      ],
      labelwidth: 100 * 2.5,
      menuId: '',
      jurisdictionType: '',
      ruleList: {
      },
      ableRelation: {
      },
      regList: {
      },
      ableAll: {
      },
      childIndex: -1,
      dataValue: {},
      isEdit: false,
      userInfo: {},
      content: '',
      title: '',
      show: false,
      optionsObj: {
        defaultProps: { "label": "fullName", "value": "enCode", "multiple": false, "children": "" },
        sexProps: { "label": "fullName", "value": "enCode", "multiple": false, "children": "" },
        nationProps: { "label": "fullName", "value": "enCode", "multiple": false, "children": "" },
        sexOptions: [],
        nationOptions: [],
      },
      pictureList: [],
      avatarSrc: '',
    }
  },
  computed: {
    baseURL() {
      return this.define.comUploadUrl;
    },
    baseURL2() {
      return this.define.baseURL;
    },
  },
  created() {
    uni.$on('linkPageConfirm', (subVal) => {
      if (this.tableKey) {
        for (let i = 0; i < subVal.length; i++) {
          let t = subVal[i]
          if (this['get' + this.tableKey]) {
            this['get' + this.tableKey](t)
          }
        }
        this.childIndex = -1
        this.collapse()
      }
    })
    uni.$on('initCollapse', () => {
      //初始化折叠面板高度高度
      this.collapse()
    })
  },
  onLoad(option) {
    this.jurisdictionType = option.jurisdictionType
    this.menuId = option.menuId
    this.userInfo = uni.getStorageSync('userInfo') || {}
    this.dataForm.id = option.id || ''
    this.setTitle(option)
    this.initDefaultData()
    this.dataAll()
    this.initData()
    this.dataValue = JSON.parse(JSON.stringify(this.dataForm))
    this.idList = option.idList ? option.idList.split(",") : []
    for (let i = 0; i < this.idList.length; i++) {
      if (this.idList[i] == option.id) {
        this.index = i;
      }
    }
    setTimeout(() => {
      uni.$emit('initCollapse')
    }, 50)
    uni.$on('initCollapse', () => {
      //初始化折叠面板高度高度
      this.collapse()
    })

  },
  onReady() {
    setTimeout(() => {
      this.$refs.dataFormRef.setRules(this.rules);
    }, 1000)

  },
  watch: {
    dataForm: {
      handler(val, oldVal) {
      },
      deep: true
    }
  },
  methods: {

    setTitle(option) {
      let _title = ""
      if (option.jurisdictionType == 'btn_edit') {
        _title = "编辑"
      }
      if (option.jurisdictionType == 'btn_detail') {
        _title = "详情"
      }
      if (option.jurisdictionType == 'btn_add') {
        _title = "新增"
      }
      if (_title) {
        uni.setNavigationBarTitle({
          title: _title
        })
      }
    },
    dataAll() {
      this.collapse()
    },
    clickActivetab(index) {
      this.activetab = index;
      this.timeKey = +new Date()
      this.collapse()
      setTimeout(() => {
        uni.$emit('initCollapse')
      }, 50)
    },
    initData() {
      this.$nextTick(function () {
        if (this.dataForm.id) {
          this.loading = true
          getForEdit(this.dataForm.id).then(res => {
            this.dataInfo(res.data)
            this.loading = false
          })
        } else {
          this.initDefaultData()
        }
      })
    },
    initDefaultData() {
      getPhysicianAllDictionaryData(this.optionsObj);
    },
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ["original", "compressed"],
        success: (res) => {
          // #ifdef H5
          let isAccept = new RegExp("image/*").test(res.tempFiles[0].type);
          if (!isAccept) return this.$u.toast(`请上传图片`);
          // #endif
          let tempFilePaths = res.tempFilePaths[0];
          uni.uploadFile({
            url: this.baseURL + "annexpic",
            filePath: tempFilePaths,
            name: "file",
            header: {
              Authorization: this.token,
            },
            success: (uploadFileRes) => {
              let data = JSON.parse(uploadFileRes.data);
              if (data.code === 200) {
                this.dataForm.picture = data.data.url;
                this.avatarSrc = this.baseURL2 + data.data.url;
              } else {
                this.$u.toast(data.msg);
              }
            },
            fail: (err) => {
              this.$u.toast("图片上传失败");
            },
          });
        },
      });
    },
    resetForm() {
      uni.navigateBack()
    },
    handleSubmit(type) {
      uni.showModal({
        title: '提示',
        content: '确认提交吗？',
        success: (res) => {
          if (res.confirm) {
            this.submitForm(type)
          }
        }
      })
    },
    submitForm(type) {
      var _data = this.dataList()
      this.$refs.dataFormRef.validate(valid => {
        if (!valid) return
        this.btnLoading = true
        if (this.dataForm.id) {
          updatePhysician(_data).then(res => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  this.btnLoading = false
                  if (type != 1) {
                    uni.$emit('refresh')
                    uni.navigateBack()
                  }
                }, 1500)
              }
            })
          }).catch(() => {
            this.btnLoading = false
          })
        } else {
          createPhysician(_data).then(res => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  if (type == 1) {
                    this.dataForm = JSON.parse(JSON.stringify(this.dataValue))
                    this.initDefaultData()
                  } else {
                    uni.$emit('refresh')
                    uni.navigateBack()
                  }
                  this.btnLoading = false
                }, 1500)
              }
            })
          }).catch(() => {
            this.btnLoading = false
          })
        }
      });
    },
    dataList() {
      var _data = this.dataForm;
      return _data;
    },
    dataInfo(dataAll) {
      let dataList = JSON.parse(JSON.stringify(dataAll))
      this.dataForm = dataList
      this.avatarSrc = dataList.picture && this.baseURL2 + dataList.picture;
      dataList.certificate = dataList.certificate || []
      dataList.qualificationCertificate = dataList.qualificationCertificate || []
      dataList.workCertificate = dataList.workCertificate || []
      this.isEdit = true
      this.dataAll()
      this.isEdit = false
      this.childIndex = -1
      this.collapse()
      setTimeout(() => {
        uni.$emit('initCollapse')
      }, 50)
    },
    collapse() {

    },
  }
}

</script>
<style>
page {
  background-color: #f0f2f6;
}
</style>