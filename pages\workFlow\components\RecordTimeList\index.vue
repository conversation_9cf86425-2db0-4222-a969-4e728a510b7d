<template>
	<view class="flowStep u-m-t-20">
		<view class="tabsContent">
			<u-tabs :list="getList" :current="current" @change="change"></u-tabs>
			<view class="u-p-l-20 u-p-r-20 u-p-t-20 time-line-box">
				<TimeLine :progressList="progressList" :taskInfo="taskInfo" v-if="current == 0"></TimeLine>
				<ProcessComments ref="ProcessComments" v-if="current == 1" :taskId="taskId" @handleReply="handleReply">
				</ProcessComments>
			</view>
		</view>
	</view>
</template>

<script>
	import TimeLine from './TimeLine.vue'
	import ProcessComments from './ProcessComments.vue'

	export default {
		components: {
			ProcessComments,
			TimeLine
		},
		props: {
			progressList: {
				type: Array,
				default: () => []
			},
			commentList: {
				type: Array,
				default: () => []
			},
			taskInfo: {
				type: Object,
				default: () => {}
			},
			taskId: {
				type: [String, Number],
				default: ''
			},
			hasComment: {
				default: false
			}
		},
		data() {
			return {

				current: 0,
				value: '',
				popupTitle: '',
				nodeId: ''
			}
		},
		computed: {
			baseURL() {
				return this.define.baseURL
			},
			getList() {
				let list = [{
					name: '流转'
				}]
				if (this.hasComment) list.push({
					name: '评论'
				})
				return list
			}
		},
		methods: {
			handleReply(id) {
				this.$emit('handleReply', id)
			},
			popupClose() {
				this.$refs.flowStepPopup.close()
			},
			change(e) {
				this.current = e
				if (this.current === 1) {
					this.$nextTick(() => {
						this.$refs.ProcessComments.getCommentList()
					})
				}
			}
		}
	}
</script>

<style lang="scss">
	.flowStep {
		width: 100%;

		.tabsContent {
			.time-line-box {
				background-color: #fff;
			}

			.bottom-btn {
				background-color: #fff;
				width: 100%;
				height: 88rpx;
				padding: 0 20rpx;
				color: #7f7f7f;

				.btn {
					background-color: #f2f2f2;
					border-radius: 8rpx;
					height: 60rpx;
					padding-left: 10rpx;
					width: 100%;
					line-height: 60rpx;
				}
			}
		}
	}

	.timeLine {
		width: 100%;
		height: 100%;
		padding: 20rpx;

		.u-time-axis-item {
			.u-time-axis-node {
				top: 0 !important;
			}
		}

		.timeLine-right {
			padding-left: 0;
			padding-right: 40rpx !important;

			&::before {
				left: 670rpx !important;
			}

			.u-time-axis-item {
				.u-time-axis-node {
					left: 670rpx !important;
				}
			}
		}

		.timeLine-dot {
			width: 28rpx;
			height: 28rpx;
			border: 4rpx solid #FFFFFF;
			box-shadow: 0 6rpx 12rpx rgba(2, 7, 28, 0.16);
			border-radius: 50%;
		}

		.timeLine-content {
			padding: 0 10rpx;

			.timeLine-title {
				font-size: 30rpx;
				line-height: 36rpx;

				.name {
					margin-bottom: 6rpx;
				}
			}

			.timeLine-title2 {
				margin-top: 6rpx;
				background: rgba(255, 255, 255, 0.39);
				box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.1);
				padding: 10rpx 20rpx;
				border-radius: 4px;
			}
		}


		.timeLine-desc {
			margin-top: 10rpx;
			font-size: 26rpx;
			line-height: 36rpx;
			color: #909399;
			margin-bottom: 10rpx;
		}
	}
</style>