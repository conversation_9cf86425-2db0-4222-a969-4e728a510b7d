<template>
  <u-popup
    ref="popRef"
    mode="bottom"
    bg-color="null"
    z-index="99"
    width="100%"
    height="200vh"
  >
    <view class="container">
      <view class="close">
        <icon type="clear" :size="20" color="#5677fc" @click="close"></icon>
      </view>
      <u-form
        ref="formRef"
        :model="searchFormState"
        label-position="left"
        labelWidth="150rpx"
        :labelStyle="{
          fontSize: '27rpx',
          color: '#606266',
        }"
      >
        <u-form-item label="学号" prop="no">
          <u-input
            v-model="searchFormState.no"
            placeholder="请输入学号"
          ></u-input>
        </u-form-item>
        <u-form-item label="姓名" prop="name">
          <u-input
            v-model="searchFormState.name"
            placeholder="请输入姓名"
          ></u-input>
        </u-form-item>
        <u-form-item label="联系方式" prop="phone">
          <u-input
            v-model="searchFormState.phone"
            placeholder="请输入联系方式"
          ></u-input>
        </u-form-item>
      </u-form>
      <u-row :gutter="10">
        <u-col :span="6">
          <tui-button height="90rpx" type="gray" @click="reset"
            >重置</tui-button
          >
        </u-col>
        <u-col :span="6">
          <tui-button height="90rpx" type="primary" @click="confirm"
            >确认</tui-button
          >
        </u-col>
      </u-row>
    </view>
  </u-popup>
</template>
<script>
import resources from "@/libs/resources.js";
import tuiButton from "@/components/tui-button.vue";
export default {
  components: {
    tuiButton,
  },
  props: {
    searchFormState: {
      type: Object,
      default: () => {
        return {
          no: "",
          name: "",
          phone: "",
        };
      },
    },
  },
  data: {},
  methods: {
    open() {
      this.$refs.popRef.open();
    },
    reset() {
      // 重置数据
      this.$emit("reset");
    },
    confirm() {
      this.$refs.popRef.close();
      this.$emit("confirm", this.searchFormState);
    },
    close() {
      console.log("关闭");
      this.$refs.popRef.close();
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  margin: 5rpx;
  border-radius: 10rpx;
  padding: 20rpx;
  background-color: white;
  .close {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
