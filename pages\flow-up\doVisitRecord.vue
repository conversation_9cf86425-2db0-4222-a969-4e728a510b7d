<template>
  <view class="visit-record-container" v-if="!loading">
    <!-- 顶部患者信息卡片 -->
    <view class="patient-info-card">
      <view class="patient-header">
        <view class="patient-avatar">
          <text class="avatar-text">{{ getPatientAvatarText() }}</text>
        </view>
        <view class="patient-details">
          <view class="patient-name">{{
            dataForm.patientName || "未知患者"
          }}</view>
          <view class="patient-meta">
            <text class="meta-label">住院号:</text>
            <text class="meta-value">{{
              dataForm.patientAdmissionNo || "暂无"
            }}</text>
          </view>
        </view>
        <view class="action-type-badge" :class="getActionTypeClass()">
          {{ getActionTypeText() }}
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-sections">
      <!-- 基本信息 -->
      <view class="form-section" v-if="actionType === 'detail'">
        <view class="section-header">
          <view class="section-icon">👨‍⚕️</view>
          <text class="section-title">随访信息</text>
        </view>
        <view class="section-content">
          <view class="form-row">
            <view class="form-item">
              <text class="form-label">随访医师</text>
              <view class="form-value-display">{{
                dataForm.physicianName || "未知医师"
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 随访内容 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">📝</view>
          <text class="section-title">随访内容</text>
        </view>
        <view class="section-content">
          <view class="form-row">
            <view class="form-item full-width">
              <text class="form-label">随访时间</text>
              <uni-datetime-picker v-if="actionType !== 'detail'" type="datetime" v-model="dataForm.visiteDate"
                :disabled="actionType === 'detail'" class="datetime-picker" />
              <view v-else class="form-value-display datetime-display">{{
                formatDateTime(dataForm.visiteDate)
              }}</view>
            </view>
          </view>
          <view class="form-row">
            <view class="form-item full-width">
              <text class="form-label">随访情况</text>
              <textarea v-if="actionType !== 'detail'" v-model="dataForm.situation" placeholder="请详细描述患者的随访情况..."
                class="form-textarea" :disabled="actionType === 'detail'" />
              <view v-else class="form-value-display textarea-display">{{
                dataForm.situation || "暂无记录"
              }}</view>
            </view>
          </view>
          <view class="form-row">
            <view class="form-item full-width">
              <text class="form-label">注意事项</text>
              <textarea v-if="actionType !== 'detail'" v-model="dataForm.attention" placeholder="请输入需要注意的事项..."
                class="form-textarea" :disabled="actionType === 'detail'" />
              <view v-else class="form-value-display textarea-display">{{
                dataForm.attention || "暂无记录"
              }}</view>
            </view>
          </view>
          <view class="form-row">
            <view class="form-item full-width">
              <text class="form-label">随访建议</text>
              <textarea v-if="actionType !== 'detail'" v-model="dataForm.advice" placeholder="请输入随访建议..."
                class="form-textarea" :disabled="actionType === 'detail'" />
              <view v-else class="form-value-display textarea-display">{{
                dataForm.advice || "暂无记录"
              }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 附件上传 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">📎</view>
          <text class="section-title">附件</text>
        </view>
        <view class="section-content">
          <view class="form-row">
            <view class="form-item full-width">
              <view v-if="actionType !== 'detail'">
                <view v-for="(attachment, index) in fileList" :key="attachment.fileId" class="file-card">
                  <u-icon name="file-text" size="32" color="#4a86e8" class="file-icon"></u-icon>
                  <u-icon name="download" size="32" color="#999" class="file-action-icon"
                    @click="downloadFile(attachment)"></u-icon>
                  <text class="file-title">{{ attachment.name }}</text>
                  <u-icon name="close" size="24" color="#999" class="file-action-icon"
                    @click="removeFile(attachment)"></u-icon>
                </view>
                <view class="upload-area">
                  <view class="upload-placeholder">
                    <u-upload :showUploadList="false" :action="define.baseURL + '/api/flowUp/basis/uploaderAttachment'
                      " :header="{
                        Authorization: uni.getStorageSync('token'),
                      }" :formData="{
                        folder: 'flowup/visitRecord',
                      }" @on-success="uploadSuccess" name="file" multiple :maxCount="10">
                      <view class="upload-button">
                        <u-icon name="plus" size="40" color="#999"></u-icon>
                        <text class="upload-text">添加附件</text>
                      </view>
                    </u-upload>
                  </view>
                </view>
              </view>
              <view v-else-if="fileList && fileList.length > 0" class="attachment-display">
                <view v-for="attachment in fileList" :key="attachment.fileId" class="file-card"
                  @click="downloadFile(attachment)">
                  <u-icon name="file-text" size="32" color="#4a86e8" class="file-icon"></u-icon>
                  <text class="file-title">{{ attachment.name }}</text>
                  <u-icon name="eye-fill" size="32" color="#999" class="file-action-icon"></u-icon>
                </view>
              </view>
              <view v-else class="form-value-display">暂无附件</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons" v-if="actionType !== 'detail'">
      <view class="action-row">
        <view class="action-btn secondary-btn" @click="resetForm">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn primary-btn" @click="handleSubmit" :class="{ 'btn-loading': btnLoading }">
          <view class="btn-icon" v-if="!btnLoading">✅</view>
          <view class="btn-loading-icon" v-if="btnLoading">⏳</view>
          <text class="btn-text">{{ btnLoading ? "提交中..." : "确定" }}</text>
        </view>
      </view>
    </view>

    <!-- 详情页面的返回按钮 -->
    <view class="action-buttons" v-else>
      <view class="action-row">
        <view class="action-btn primary-btn full-width-btn" @click="resetForm">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">返回</text>
        </view>
        <view class="action-btn primary-btn full-width-btn" @click="doEdit" v-if="showEdit">
          <view class="btn-icon">📝</view>
          <text class="btn-text">修改</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" v-if="loading">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
import {
  getForEdit,
  createVisitRecord,
  updateVisitRecord,
} from "@/api/flow-up/visitRecord";

export default {
  components: {},
  data() {
    return {
      dataForm: {},
      isEdit: false,
      option: {},
      actionType: "",
      loading: false,
      btnLoading: false,
      userInfo: {},
      dataValue: {},
      fileList: [],
    };
  },
  computed: {
    showEdit() {
      if (this.userInfo.roleName === "医师") return true;
      return false;
    },
  },
  created() {
    uni.$on("linkPageConfirm", (subVal) => {
      if (this.tableKey) {
        for (let i = 0; i < subVal.length; i++) {
          let t = subVal[i];
          if (this["get" + this.tableKey]) {
            this["get" + this.tableKey](t);
          }
        }
        this.collapse();
      }
    });
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onLoad(option) {
    this.option = option;
    this.actionType = option.actionType;
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.dataForm.id = option.id || "";
    this.initData();
    this.dataValue = JSON.parse(JSON.stringify(this.dataForm));

    setTimeout(() => {
      uni.$emit("initCollapse");
    }, 50);
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onReady() {
    setTimeout(() => { }, 1000);
  },
  watch: {
    dataForm: {
      handler() {
        // 监听数据变化
      },
      deep: true,
    },
    actionType: {
      handler() {
        // 监听数据变化
        let _title = "";
        if (this.actionType == "edit") {
          _title = "编辑";
        }
        if (this.actionType == "detail") {
          _title = "详情";
        }
        if (this.actionType == "add") {
          _title = "新增";
          this.initDefaultData();
        }
        if (_title) {
          uni.setNavigationBarTitle({
            title: _title,
          });
        }
      },
      deep: true,
    },
  },

  methods: {
    previewImage(e) {
      uni.previewImage({
        urls: [this.define.baseURL + e.url],
        current: 0,
      });
    },
    downloadFile(file) {
      // 显示加载提示
      uni.showLoading({
        title: "准备下载...",
        mask: true,
      });

      // 完整的文件URL
      const fileUrl = this.define.baseURL + file.url;

      // 使用uni.downloadFile下载文件
      uni.downloadFile({
        url: fileUrl,
        success: (res) => {
          if (res.statusCode === 200) {
            // 下载成功，打开文件
            uni.openDocument({
              filePath: res.tempFilePath,
              showMenu: true,
              success: () => {
                uni.hideLoading();
                uni.showToast({
                  title: "文件打开成功",
                  icon: "success",
                });
              },
              fail: () => {
                uni.hideLoading();
                uni.showToast({
                  title: "文件打开失败",
                  icon: "none",
                });
              },
            });
          }
        },
        fail: () => {
          uni.hideLoading();
          uni.showToast({
            title: "下载失败",
            icon: "none",
          });
        },
      });
    },

    doEdit() {
      this.actionType = "edit";
    },
    uploadSuccess(e) {
      this.fileList = this.fileList ?? [];
      if (e.code === 200 && e.data) this.fileList.push(e.data);
    },
    // 删除图片
    removeFile(event) {
      uni.showModal({
        title: "提示",
        content: "是否删除【" + event.name + "】？",
        success: (res) => {
          if (res.confirm) {
            this.fileList = this.fileList ?? [];
            this.fileList = this.fileList.filter(
              (item) => item.fileId !== event.fileId
            );
          }
        },
      });
    },
    // 获取患者头像文字
    getPatientAvatarText() {
      const name = this.dataForm.patientName;
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取操作类型文本
    getActionTypeText() {
      switch (this.actionType) {
        case "add":
          return "新增随访";
        case "edit":
          return "编辑随访";
        case "detail":
          return "随访详情";
        default:
          return "随访记录";
      }
    },

    // 获取操作类型样式类
    getActionTypeClass() {
      switch (this.actionType) {
        case "add":
          return "badge-add";
        case "edit":
          return "badge-edit";
        case "detail":
          return "badge-detail";
        default:
          return "badge-default";
      }
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return "暂无";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    initDefaultData() {
      this.dataForm = {
        patientId: this.option.patientId,
      };
      if (this.option.patientName) {
        this.dataForm.patientName = decodeURIComponent(this.option.patientName);
      }
    },
    resetForm() {
      if (this.actionType == "edit") {
        this.actionType = "detail";
        this.initData();
      } else uni.navigateBack();
    },

    initData() {
      this.$nextTick(() => {
        if (this.dataForm.id) {
          this.loading = true;
          getForEdit(this.dataForm.id)
            .then((res) => {
              this.dataInfo(res.data);
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
              uni.showToast({
                title: "加载失败",
                icon: "none",
              });
            });
        } else {
          this.initDefaultData();
        }
      });
    },

    valid() {
      if (!this.dataForm.visiteDate) {
        uni.showToast({
          title: "请选择随访日期",
          icon: "none",
        });
        return false;
      }
      if (!this.dataForm.situation) {
        uni.showToast({
          title: "请填写随访情况",
          icon: "none",
        });
        return false;
      }
      return true;
    },

    handleSubmit(type) {
      if (!this.valid()) return;
      uni.showModal({
        title: "提示",
        content: "确认提交吗？",
        success: (res) => {
          if (res.confirm) {
            this.submitForm(type);
          }
        },
      });
    },
    submitForm(type) {
      var _data = this.dataList();
      if (!this.valid()) return;
      this.btnLoading = true;
      _data.attachments = this.fileList;
      if (this.dataForm.id) {
        updateVisitRecord(_data)
          .then((res) => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  this.btnLoading = false;
                  this.resetForm();
                }, 1500);
              },
            });
          })
          .catch(() => {
            this.btnLoading = false;
          });
      } else {
        createVisitRecord(_data)
          .then((res) => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  uni.$emit("refresh");
                  uni.navigateBack();
                  this.btnLoading = false;
                }, 1500);
              },
            });
          })
          .catch(() => {
            this.btnLoading = false;
          });
      }
    },

    dataList() {
      var _data = this.dataForm;
      return _data;
    },
    dataInfo(dataAll) {
      this.dataForm = { ...dataAll };
      this.fileList = this.dataForm.attachments || [];
      this.isEdit = true;
    },
    collapse() {
      setTimeout(() => { }, 50);
    },
  },
};
</script>
<style lang="scss">
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.visit-record-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
}

// 顶部患者信息卡片
.patient-info-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .patient-header {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .patient-avatar {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3rpx solid rgba(255, 255, 255, 0.3);

      .avatar-text {
        color: #fff;
        font-size: 28rpx;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
    }

    .patient-details {
      flex: 1;
      min-width: 0;

      .patient-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 12rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .patient-meta {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .meta-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .meta-value {
          font-size: 26rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }

    .action-type-badge {
      padding: 12rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      border: 2rpx solid rgba(255, 255, 255, 0.3);

      &.badge-add {
        background: rgba(76, 175, 80, 0.9);
        color: #fff;
      }

      &.badge-edit {
        background: rgba(255, 193, 7, 0.9);
        color: #fff;
      }

      &.badge-detail {
        background: rgba(96, 125, 139, 0.9);
        color: #fff;
      }

      &.badge-default {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
      }
    }
  }
}

// 表单区域
.form-sections {
  .form-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }
    }

    .section-content {
      padding: 28rpx;

      .form-row {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .form-item {
          flex: 1;
          min-width: 0;

          &.full-width {
            flex: 100%;
          }

          .form-label {
            font-size: 28rpx;
            color: #1565c0;
            font-weight: 600;
            margin-bottom: 12rpx;
            display: block;
          }

          .form-value-display {
            padding: 20rpx;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
            border-radius: 16rpx;
            border: 1rpx solid rgba(25, 118, 210, 0.1);
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
            min-height: 44rpx;
            display: flex;
            align-items: center;

            &.textarea-display {
              min-height: 120rpx;
              align-items: flex-start;
              padding-top: 20rpx;
              word-break: break-all;
              line-height: 1.6;
            }

            &.datetime-display {
              color: #1565c0;
              font-weight: 600;
            }
          }

          .form-textarea {
            width: 100%;
            min-height: 120rpx;
            padding: 20rpx;
            border: 2rpx solid #e9ecef;
            border-radius: 16rpx;
            font-size: 28rpx;
            color: #333;
            background: #fff;
            transition: all 0.3s ease;
            box-sizing: border-box;
            resize: none;

            &:focus {
              border-color: #1976d2;
              box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
              outline: none;
            }

            &::placeholder {
              color: #adb5bd;
              font-size: 26rpx;
            }
          }

          .datetime-picker {
            width: 100%;

            ::v-deep .uni-datetime-picker {
              border: 2rpx solid #e9ecef;
              border-radius: 16rpx;
              background: #fff;
              transition: all 0.3s ease;

              .uni-datetime-picker-text {
                padding: 20rpx;
                font-size: 28rpx;
                color: #333;
                min-height: 44rpx;
                display: flex;
                align-items: center;
              }

              .uni-datetime-picker-text.uni-datetime-picker-placeholder {
                color: #adb5bd;
              }

              &:active {
                border-color: #1976d2;
                box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
              }
            }
          }

          .upload-area {
            border: 2rpx dashed #e9ecef;
            border-radius: 16rpx;
            padding: 20rpx;
            background: #fafbfc;
            transition: all 0.3s ease;

            &:active {
              border-color: #1976d2;
              background: #f8fbff;
            }

            .upload-placeholder {
              .upload-button {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 10rpx;
                padding: 30rpx 20rpx;
                border-radius: 12rpx;
                background: #fff;

                .upload-text {
                  font-size: 26rpx;
                  color: #6c757d;
                }
              }
            }
          }

          .attachment-display {
            .file-card {
              margin-bottom: 16rpx;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 文件卡片
.file-card {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
  border-radius: 16rpx;
  border: 1rpx solid rgba(25, 118, 210, 0.1);
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .file-icon {
    flex-shrink: 0;
  }

  .file-title {
    flex: 1;
    font-size: 28rpx;
    color: #1565c0;
    font-weight: 500;
    word-break: break-all;
  }

  .file-action-icon {
    flex-shrink: 0;
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      .btn-loading-icon {
        font-size: 28rpx;
        animation: spin 1s linear infinite;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }

        &.btn-loading {
          opacity: 0.8;
          pointer-events: none;
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.full-width-btn {
        flex: 100%;
      }
    }
  }
}

// 加载状态
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #e3f2fd;
      border-top: 4rpx solid #1976d2;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 28rpx;
      color: #1976d2;
      font-weight: 500;
    }
  }
}

// 动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .visit-record-container {
    padding: 16rpx;
    padding-bottom: 180rpx;
  }

  .patient-info-card {
    padding: 24rpx;

    .patient-header {
      gap: 16rpx;

      .patient-avatar {
        width: 64rpx;
        height: 64rpx;

        .avatar-text {
          font-size: 24rpx;
        }
      }

      .patient-details {
        .patient-name {
          font-size: 32rpx;
        }

        .patient-meta {
          .meta-label {
            font-size: 22rpx;
          }

          .meta-value {
            font-size: 24rpx;
          }
        }
      }

      .action-type-badge {
        padding: 8rpx 16rpx;
        font-size: 22rpx;
      }
    }
  }

  .form-sections {
    .form-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .form-row {
          gap: 16rpx;

          .form-item {
            .form-label {
              font-size: 26rpx;
            }

            .form-value-display {
              padding: 16rpx;
              font-size: 26rpx;

              &.textarea-display {
                min-height: 100rpx;
              }
            }

            .form-textarea {
              min-height: 100rpx;
              padding: 16rpx;
              font-size: 26rpx;
            }

            .datetime-picker {
              ::v-deep .uni-datetime-picker {
                .uni-datetime-picker-text {
                  padding: 16rpx;
                  font-size: 26rpx;
                }
              }
            }

            .upload-area {
              padding: 16rpx;

              .upload-placeholder {
                .upload-button {
                  padding: 20rpx 16rpx;
                  gap: 8rpx;

                  .upload-text {
                    font-size: 24rpx;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .file-card {
    gap: 12rpx;
    padding: 16rpx;

    .file-title {
      font-size: 26rpx;
    }
  }

  .action-buttons {
    padding: 20rpx 16rpx;

    .action-row {
      gap: 12rpx;

      .action-btn {
        height: 80rpx;

        .btn-icon,
        .btn-loading-icon {
          font-size: 24rpx;
        }

        .btn-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>
