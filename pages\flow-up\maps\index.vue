<template>
  <view class="xunda-location-map">
    <u-top-tips ref="uTips" />
    <view class="content">
      <!-- <u-button class="buttom-btn" type="success" @click="handleOpenForm(1)"
        >上访对象</u-button
      >
      <u-button class="buttom-btn" type="primary" @click="handleOpenForm(2)"
        >上访记录</u-button
      > -->
      <u-button class="buttom-btn" type="primary" @click="loadAddressList()"
        >刷新</u-button
      >
    </view>
    <view class="header">
      <view class="map-container">
        <map
          class="map"
          id="interviewMap"
          :latitude="location.latitude"
          :longitude="location.longitude"
          :scale="15"
          :show-action="false"
          :markers="markers"
          @markertap="handleMarkerTap"
        >
        </map>
      </view>
      <view class="around-contain">
        <scroll-view
          style="height: 50%"
          id="scroll-view-h"
          class="scroll-view2"
          :refresher-enabled="false"
          :refresher-threshold="50"
          :scroll-with-animation="true"
          @scrolltolower="handleScrollToLower"
          :scroll-y="true"
        >
          <radio-group
            class="around-contain-item"
            v-for="(item, index) in list"
            :key="index"
            v-if="list.length"
            @change="onSelectValueChange(item, index)"
          >
            <label class="u-radio-label">
              <radio
                class="u-radio"
                :value="item.id"
                :checked="item.id === selectId"
              />
              <view class="around-item-title-box">
                <view class="around-item-title u-line-1"> {{ item.name }}</view>
                <view class="around-item-sub-title u-line-1">
                  {{ item.address }}</view
                >
              </view>
            </label>
          </radio-group>
          <u-loading class="loading" mode="circle" size="44" v-if="loading" />
          <view v-if="!loading && !list.length" class="nodata u-flex-col">
            <image :src="noDataIcon" mode="widthFix" class="noDataIcon" />
            暂无数据
          </view>
        </scroll-view>
      </view>
      <view class="xunda-bottom-actions">
        <u-button class="buttom-btn" @click="close()">取消</u-button>
        <u-button
          class="buttom-btn"
          type="primary"
          @click.stop="handleConfirm()"
          >确定</u-button
        >
      </view>
    </view>
  </view>
</template>

<script>
import resources from "@/libs/resources.js";
import { getAroundList, getTextList } from "@/api/common.js";
import { getAddressList } from "@/api/interview/map.js";
import { getMenuList, getUsualList, getChildList } from "@/api/apply/apply.js";
export default {
  data() {
    return {
      mapContext: null,
      loading: false,
      noDataIcon: resources.message.nodata,
      tabWidth: 150,
      tabIndex: 0,
      keyword: "",
      location: {
        longitude: 116.404, // 经度
        latitude: 39.915, // 纬度
      },
      circles: [],
      list: [],
      pagination: {
        currentPage: 1,
        pageSize: 50,
      },
      total: 0,
      currentLocation: {},
      selectId: "",
      selectItem: {},
      enableLocation: "",
      showPopup: false,
      locateLoading: false,
      polygons: [],
      enableLocationScope: false,
      adjustmentScope: 500,
      enableDesktopLocation: false,
      locationScope: [],
      emitKey: "",
      // #ifdef APP
      dragLoading: false,
      // #endif
      param: {
        currentPage: 1,
        pageSize: 200,
        menuId: "648490144889831429",
        modelId: "648488945071423493",
        sidx: "",
        keyword: "",
        queryJson: "",
      },
      selectedAddress: null,
    };
  },
  onLoad(e) {
    const data = e.data ? JSON.parse(e.data) : {};
    this.enableLocationScope = data.enableLocationScope || false;
    this.adjustmentScope = data.adjustmentScope || 500;
    this.enableDesktopLocation = data.enableDesktopLocation || false;
    this.locationScope = data.locationScope || [];
    this.emitKey = data.emitKey;
    this.init();
  },
  methods: {
    init() {
      this.circles = [];
      this.polygons = [];
      this.selectId = "";
      this.list = [];
      this.locateLoading = false;
      // #ifdef APP
      this.dragLoading = false;
      // #endif
      // this.getLocation();
      this.loadAddressList();
    },
    getLocation() {
      this.loading = true;
      uni.getLocation({
        type: "gcj02",
        success: (res) => {
          this.location.longitude = res.longitude; // 经度
          this.location.latitude = res.latitude; // 纬度
        },
        fail: (err) => {},
      });
    },

    // 加载地址列表并设置标记点
    loadAddressList() {
      getAddressList(this.param).then((res) => {
        this.addressList = res.data.list;
        this.markers = [];
        console.log(this.markers);
        if (this.addressList.length > 0) {
          if (
            this.addressList[0].F_Latitude &&
            this.addressList[0].F_Longitude
          ) {
            this.location.latitude = this.addressList[0].F_Latitude;
            this.location.longitude = this.addressList[0].F_Longitude;
          }
        }

        this.handleSetMarker();
      });
    },
    handleSetMarker() {
      this.markers = this.addressList.map((item, index) => {
        const content = `${item.F_Name} ${
          item.F_Address ? "(" + item.F_Address + ")" : ""
        }上访次数：${item.F_Fcount ? item.F_Fcount : 0}次`;
        return {
          id: index,
          height: 40,
          width: 40,
          latitude: item.F_Latitude,
          longitude: item.F_Longitude,
          iconPath: "/static/image/mark.png",
          label: {
            content: content,
            color: "#000000",
            fontSize: 14,
            borderRadius: 3,
            bgColor: "#ffffff",
            padding: 5,
            display: "ALWAYS",
          },
        };
      });
      console.log(this.markers);
    },
    // 处理标记点点击
    handleMarkerTap(e) {
      const address = this.addressList.find(
        (item) =>
          item.F_Latitude === e.detail.latitude &&
          item.F_Longitude === e.detail.longitude
      );
      const _this = this;
      if (address) {
        this.selectedAddress = address;
        _this.goDetail(_this.selectedAddress);
      }
    },

    goDetail(item) {
      uni.navigateTo({
        url: "/pages/map/addressDetail?id=" + item.id,
        fail: (err) => {
          this.$u.toast("暂无此页面");
        },
      });
      return;
    },
    handleOpenForm(e) {
      if (e === 1) {
        const config = {
          id: "647452386423517509",
          hasChildren: false,
          parentId: "647440894479348037",
          fullName: "走访列表",
          icon: "icon-ym icon-ym-flow-align-let",
          isData: null,
          urlAddress: "/pages/apply/dynamicModel/index?id=648488945071423493",
          type: 3,
          propertyJson:
            '{"moduleId":"648488945071423493","iconBackgroundColor":"","isTree":0}',
          children: null,
          iconBackground: "",
          moduleId: "648488945071423493",
        };

        uni.navigateTo({
          url:
            "/pages/apply/dynamicModel/index?config=" +
            this.xunda.base64.encode(JSON.stringify(config)),
          fail: (err) => {
            this.$u.toast("暂无此页面");
          },
        });
      } else if (e === 2) {
        const config = {
          id: "648265730562765189",
          hasChildren: false,
          parentId: "647440894479348037",
          fullName: "上访记录",
          icon: "icon-ym icon-ym-tile-mode",
          isData: null,
          urlAddress: "/pages/apply/dynamicModel/index?id=648489558547103749",
          type: 3,
          propertyJson:
            '{"moduleId":"648489558547103749","iconBackgroundColor":"","isTree":0}',
          children: null,
          iconBackground: "",
          moduleId: "648489558547103749",
        };
        uni.navigateTo({
          url:
            "/pages/apply/dynamicModel/index?config=" +
            this.xunda.base64.encode(JSON.stringify(config)),
          fail: (err) => {
            this.$u.toast("暂无此页面");
          },
        });
      }
    },

    handleResult(res) {
      this.loading = false;
      if (res.data.status == "1") {
        this.list = [...this.list, ...(res.data.pois || [])];
        this.total = Number(res.data.count || 0);
      } else {
        this.$u.toast(res.data.info);
      }
    },
    onSelectValueChange(item, index) {
      // #ifdef APP
      this.dragLoading = true;
      // #endif
      this.selectStatus = true;
      this.selectId = item.id;
      this.selectItem = item;
      const [longitude, latitude] = (item.location || "").split(",");
      this.location = {
        longitude,
        latitude,
      };
      // #ifdef APP
      setTimeout(() => {
        this.dragLoading = false;
      }, 800);
      // #endif
    },
    handleConfirm() {
      if (!this.selectId) return this.$u.toast("请选择地址");
      const data = this.selectItem;
      const [lng, lat] = data.location.split(",");
      if (this.enableLocationScope) {
        const discount =
          this.xunda.getDistance(
            this.currentLocation.latitude,
            this.currentLocation.longitude,
            lat,
            lng
          ) || 0;
        if (discount > (this.adjustmentScope || 500))
          return this.$refs.uTips.show({
            title: "超出微调范围",
            type: "warning",
          });
      }
      //判断可选范围
      if (this.enableDesktopLocation && this.locationScope.length) {
        let list = [];
        for (let i = 0; i < this.locationScope.length; i++) {
          const o = this.locationScope[i];
          const discount = this.xunda.getDistance(o.lat, o.lng, lat, lng) || 0;
          list.push(discount > o.radius);
        }
        if (list.every((o) => o === true))
          return this.$refs.uTips.show({
            title: "超出规定范围",
            type: "warning",
          });
      }
      const address = data.address && data.address.length ? data.address : "";
      //台湾、北京、上海、重庆、深圳地址特殊处理
      let fullAddress =
        data.pname + data.cityname + data.adname + address + data.name;
      if (data.pname == data.cityname)
        fullAddress = data.pname + data.adname + address + data.name;
      if (data.pname == data.cityname && data.pname == data.adname)
        fullAddress = data.pname + address + data.name;
      this.innerValue = {
        pName: data.pname,
        cName: data.cityname,
        adName: data.adname,
        address,
        name: data.name,
        lng,
        lat,
        fullAddress,
      };
      uni.$emit(this.emitKey, JSON.stringify(this.innerValue));
      this.close();
    },
    close() {
      uni.navigateBack({
        delta: 1,
      });
    },
    getDistance(lat1, lon1, lat2, lon2) {
      const toRadians = (degrees) => {
        return degrees * (Math.PI / 180);
      };
      const R = 6371;
      const dLat = toRadians(lat2 - lat1);
      const dLon = toRadians(lon2 - lon1);
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRadians(lat1)) *
          Math.cos(toRadians(lat2)) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c;
      return distance * 1000;
    },
    search() {
      // 节流,避免输入过快多次请求
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.addressList = [];
        this.pagination.currentPage = 1;
        this.keyword ? this.handleSearch() : this.getList();
      }, 300);
    },
    handleSearch() {
      this.loading = true;
      const query = {
        key: this.define.aMapWebKey,
        keywords: this.keyword,
        radius: this.enableLocationScope ? this.adjustmentScope || 500 : -1,
        offset: this.pagination.pageSize,
        page: this.pagination.currentPage,
      };
      getTextList(query).then((res) => {
        this.handleResult(res);
      });
    },

    // 导航到位置
    navigateToLocation(address) {
      uni.openLocation({
        latitude: Number.parseFloat(address.F_Latitude),
        longitude: Number.parseFloat(address.F_Longitude),
        name: address.name,
        address: address.address,
        scale: 18,
      });
    },
    // 关闭地址卡片
    closeCard() {
      this.selectedAddress = null;
    },
  },
};
</script>

<style scoped lang="scss">
.xunda-location-map {
  /* #ifdef H5 */
  height: calc(100vh - 44px);
  /* #endif */
  /* #ifndef H5 */
  height: 100vh;
  /* #endif */

  display: flex;
  flex-direction: column;

  .header {
    .map-container {
      position: relative;
      padding: 0rpx 20rpx;

      .map {
        width: 100%;
        height: 100vh;
      }

      .map-marker {
        width: 38rpx;
        height: 64rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, calc(-50% - 20rpx));
        z-index: 9999;
      }

      .h5-map-marker {
        transform: translate(-50%, calc(-50% - 30rpx));
      }

      .map-locate {
        position: absolute;
        bottom: 10px;
        right: 10px;
        height: 24px;
        width: 24px;
        padding: 4px;
        background-color: #fff;
        border-radius: 50%;
        box-shadow: 0 0 5px silver;
        z-index: 999;

        .map-locate-img {
          -webkit-animation: rotate 2s linear infinite;
        }

        @keyframes rotate {
          0% {
            -webkit-transform: rotate(0deg);
          }

          25% {
            -webkit-transform: rotate(90deg);
          }

          50% {
            -webkit-transform: rotate(180deg);
          }

          75% {
            -webkit-transform: rotate(270deg);
          }

          100% {
            -webkit-transform: rotate(1turn);
          }
        }
      }
    }
  }

  .content {
    width: 100%;

    .user-select {
      .user-select-search {
        padding: 0rpx 20rpx;
        margin: 20rpx 0;
      }
    }
  }

  .around-contain {
    flex: 1;
    width: 100%;
    overflow: hidden;

    .loading {
      display: flex;
      justify-content: center;
      margin: 250rpx auto 0;
    }

    .around-contain-item {
      display: flex;
      align-items: center;
      padding: 10rpx 0;
      height: 60px;
      line-height: 22px;
      border-bottom: 1px solid #f2f2f6;

      .u-radio-label {
        width: 100%;
        display: flex;
        align-items: center;

        //  #ifdef MP
        :deep(.u-radio) {
          margin: 0 16rpx 0 20rpx;
        }

        // #endif
        //  #ifndef MP
        :deep(.uni-radio-input) {
          margin: 0 16rpx 0 20rpx;
        }

        // #endif
        .around-item-title-box {
          flex: 1;
          min-width: 0;
          padding-right: 16rpx;

          .around-item-title {
            font-size: 30rpx;
            color: #171a1d;
          }

          .around-item-sub-title {
            font-size: 28rpx;
            color: #b9babb;
            padding-top: 8rpx;
          }
        }
      }
    }
  }
}
</style>
