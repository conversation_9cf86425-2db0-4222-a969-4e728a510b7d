<template>
  <view class="workFlow-v">
    <view class="notice-warp">
      <view class="search-box">
        <u-search
          placeholder="请输入关键词搜索"
          v-model="keyword"
          height="72"
          :show-action="false"
          @change="search"
          bg-color="#f0f2f6"
          shape="square"
        />
      </view>
      <u-tabs
        :list="categoryList"
        :current="current"
        @change="change"
        :is-scroll="true"
        name="fullName"
        ref="tabs"
      />
    </view>
    <mescroll-body
      ref="mescrollRef"
      @down="downCallback"
      :down="downOption"
      :sticky="false"
      @up="upCallback"
      :up="upOption"
      :bottombar="false"
      style="min-height: 100%"
      @init="mescrollInit"
      top="226"
    >
      <view class="workFlow-list">
        <view class="part">
          <view class="caption u-line-1" v-if="list.length >= 1">{{
            current === 0 ? "全部流程" : fullName
          }}</view>
          <view class="u-flex u-flex-wrap">
            <view
              class="item u-flex-col u-col-center"
              v-for="(child, ii) in list"
              :key="ii"
              @click="handelClick(child)"
            >
              <text
                class="u-font-40 item-icon"
                :class="child.icon"
                :style="{ background: child.iconBackground || '#008cff' }"
              />
              <text class="u-font-24 u-line-1 item-text">{{
                child.fullName
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </mescroll-body>
  </view>
</template>
<script>
import { getFlowSelector } from "@/api/workFlow/flowEngine";
import { getUsualList } from "@/api/apply/apply.js";
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { useBaseStore } from "@/store/modules/base";

const baseStore = useBaseStore();

export default {
  mixins: [MescrollMixin],
  data() {
    return {
      fullName: "全部流程",
      selector: [],
      show: false,
      downOption: {
        use: true,
        auto: true,
      },
      upOption: {
        page: {
          num: 0,
          size: 50,
          time: null,
        },
        empty: {
          use: true,
          icon: resources.message.nodata,
          tip: "暂无数据",
          fixed: false,
          top: "560rpx",
        },
        textNoMore: "没有更多数据",
      },
      keyword: "",
      category: "",
      current: 0,
      categoryList: [],
      list: [],
      loading: false,
      delegateUser: "",
    };
  },
  onLoad(e) {
    this.delegateUser = e.delegateUser;
    uni.$on("refresh", () => {
      this.list = [];
      this.current = 0;
      this.mescroll.resetUpScroll();
    });
    this.getPaymentMethodOptions();
  },
  methods: {
    openPage(path) {
      if (!path) return;
      uni.navigateTo({
        url: path,
      });
    },
    upCallback(page) {
      let query = {
        currentPage: page.num,
        pageSize: page.size,
        keyword: this.keyword,
        category: this.category == 0 ? "" : this.category,
        delegateUser: this.delegateUser,
      };
      this.loading = false;
      getFlowSelector(query)
        .then((res) => {
          let resData = res.data.list || [];
          this.mescroll.endSuccess(resData.length);
          if (page.num == 1) this.list = [];
          const list = resData.map((o) => ({
            show: false,
            ...o,
          }));
          this.list = this.list.concat(list);
          this.loading = true;
        })
        .catch(() => {
          this.mescroll.endErr();
        });
    },
    search() {
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      }, 300);
    },
    change(index) {
      this.current = index;
      this.category = !this.categoryList[index].id
        ? ""
        : this.categoryList[index].id;
      this.list = [];
      this.fullName = this.categoryList[index].fullName;
      this.keyword = "";
      this.mescroll.resetUpScroll();
    },
    getPaymentMethodOptions() {
      baseStore
        .getDictionaryData({
          sort: "businessType",
        })
        .then((res) => {
          this.categoryList = [
            {
              fullName: "全部流程",
            },
            ...res,
          ];
        });
    },
    handelClick(item) {
      const config = {
        id: "",
        flowId: item.id,
        opType: "-1",
        taskNodeId: "",
        fullName: item.fullName,
        delegateUser: this.delegateUser,
      };
      uni.navigateTo({
        url:
          "/pages/workFlow/flowBefore/index?config=" +
          this.xunda.base64.encode(JSON.stringify(config)),
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
}

.workFlow-v {
  .search-box_sticky {
    margin-bottom: 20rpx;

    .search-box {
      padding: 20rpx;
    }
  }

  .head-tabs {
    width: 100%;
    padding: 0 32rpx;
    height: 132rpx;

    .head-tabs-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #303133;
      line-height: 40rpx;

      .icon-style {
        font-size: 48rpx;
        color: #303133;
        margin-bottom: 24rpx;
      }
    }
  }
}
</style>
