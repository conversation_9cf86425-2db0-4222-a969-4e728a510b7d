#xunda-app-vue3

## 一 环境要求

### 1.1 开发环境

- 操作系统：`Windows 10/11`，`MacOS`；
- `Node.js v12/v4/v16`(v16 版本某些情况下可能需要安装 `Python3` 环境)；
- `HBuilder X` (最新版)

### 1.2 运行环境

`Nginx 1.18.0` 及以上版本或 `TongHttpServer 6.0` (信创环境)

## 二 关联项目

> 需要使用下表中的对应分支

| 项目						| 分支			| 说明										|
| -------------------------	| -------------	| ------------------------------------------|
| **后端**(任一后端服务)	|				|											|
| xunda-java-boot			| v5.0.x-stable	| Java 单体项目源码							|
| xunda-java-cloud			| v5.0.x-stable	| Java 微服务项目源码						|
| xunda-dotnet				| v5.0.x-stable	| .NET 单体项目源码							|
| xunda-dotnet-cloud			| v5.0.x-stable	| .NET 微服务项目源码						|

## 三 使用说明

### 3.1 高德地图配置

打开 `/utils/define.ts` 配置文件，修改aMapWebKey值

打开 `manifest.json` 文件，点击app模块配置，修改高德地图相关配置信息。点击web配置，修改高德地图相关配置信息。
