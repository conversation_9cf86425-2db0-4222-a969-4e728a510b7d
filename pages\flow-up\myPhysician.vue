<template>
  <view class="doctor-container">
    <!-- 医师信息卡片 -->
    <view class="flowup-card">
      <view class="flowup-card-header">
        <view class="card-header-content">
          <view class="flowup-avatar">
            <text class="avatar-text">{{ getAvatarText() }}</text>
          </view>
          <view class="info-text">
            <view class="header-main">
              <text class="header-title">{{ doctorInfo?.name || "未绑定医师" }}</text>
              <uni-icons v-if="doctorInfo" type="person" size="16" color="#4CAF50"></uni-icons>
            </view>
            <view class="user-meta" v-if="doctorInfo?.title || doctorInfo?.department">
              <text class="meta-item" v-if="doctorInfo?.title">
                <uni-icons type="medal" size="14" color="#2196F3"></uni-icons>
                {{ doctorInfo.title }}
              </text>
              <text class="meta-item" v-if="doctorInfo?.department">
                <uni-icons type="home" size="14" color="#FF9800"></uni-icons>
                {{ doctorInfo.department }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <view class="flowup-card-content">
        <view class="doctor-contact" v-if="doctorInfo?.phone">
          <view class="contact-item" @click="makePhoneCall">
            <uni-icons type="phone" size="20" color="#4CAF50"></uni-icons>
            <text class="contact-text">电话咨询：{{ doctorInfo?.phone }}</text>
          </view>
          <view class="contact-item" @click="sendMessage">
            <uni-icons type="chat" size="20" color="#2196F3"></uni-icons>
            <text class="contact-text">发送消息</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 医师详情 -->
    <view class="flowup-card">
      <view class="flowup-section-header">
        <uni-icons type="info" size="18" color="#1976D2"></uni-icons>
        <text class="section-title">医师简介</text>
      </view>
      <view class="flowup-card-content">
        <view class="flowup-info-item">
          <text class="info-value textarea-value">{{ doctorInfo?.remarks || "暂无医师简介" }}</text>
        </view>
      </view>
    </view>

    <!-- 出诊信息 -->
    <view class="flowup-card" v-if="scheduleList && scheduleList.length > 0">
      <view class="flowup-section-header">
        <uni-icons type="calendar" size="18" color="#1976D2"></uni-icons>
        <text class="section-title">出诊信息</text>
      </view>
      <view class="flowup-card-content">
        <view class="schedule-list">
          <view class="schedule-item" v-for="(item, index) in scheduleList" :key="index">
            <view class="schedule-day">
              <uni-icons type="clock" size="16" color="#FF9800"></uni-icons>
              {{ item.day }}
            </view>
            <view class="schedule-time">{{ item.time }}</view>
            <view class="schedule-location">
              <uni-icons type="location" size="16" color="#F44336"></uni-icons>
              {{ item.location }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 患者评价 -->
    <view class="flowup-card" v-if="reviewList && reviewList.length > 0">
      <view class="flowup-section-header">
        <uni-icons type="star" size="18" color="#1976D2"></uni-icons>
        <text class="section-title">患者评价</text>
      </view>
      <view class="flowup-card-content">
        <view class="review-list">
          <view class="review-item" v-for="(review, index) in reviewList" :key="index">
            <view class="review-header">
              <image class="review-avatar" :src="review.avatar || '/static/image/default-avatar.png'"></image>
              <view class="review-info">
                <view class="review-name">{{ review.name }}</view>
                <view class="review-date">{{ review.date }}</view>
              </view>
              <view class="review-rating">
                <image class="star-icon" src="/static/image/star-full.png" v-for="i in review.rating" :key="i"></image>
                <image class="star-icon" src="/static/image/star-empty.png" v-for="i in 5 - review.rating"
                  :key="'empty' + i"></image>
              </view>
            </view>
            <view class="review-content">{{ review.content }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getMyPhysicianAsync } from "@/api/flow-up/physician";
import { useChatStore } from "@/store/modules/chat";
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue';

export default {
  components: {
    uniIcons
  },
  data() {
    return {
      doctorInfo: {

      },
      scheduleList: [

      ],
      reviewList: [

      ],
      avatar: "/static/image/doctor-avatar.png",
      // 其他数据...
    };
  },
  onLoad(options) {
    console.log(options);
    // 可以根据传入的医生ID获取医生详细信息
    this.doctorInfo = null;
    this.getMyPhysician();
  },
  methods: {
    // 获取医生详细信息
    getDoctorDetail(doctorId) {
      // 这里可以调用API获取医生详细信息
      console.log("获取医生信息，ID:", doctorId);
    },
    getMyPhysician() {
      getMyPhysicianAsync()
        .then((res) => {
          this.doctorInfo = res.data;
          if (res.data.picture) {
            this.avatar = this.define.baseURL + res.data.picture;
          }
          // 初始化scheduleList和reviewList
          this.scheduleList = res.data.scheduleList || [];
          this.reviewList = res.data.reviewList || [];
        })
        .catch((err) => {
          console.error(err);
        });
    },
    // 拨打电话
    makePhoneCall() {
      uni.makePhoneCall({
        phoneNumber: this.doctorInfo.phone,
        fail: () => {
          uni.showToast({
            title: "拨打电话失败",
            icon: "none",
          });
        },
      });
    },
    // 发送消息
    sendMessage() {
      const item = this.doctorInfo;
      const name = item.name;
      if (!item.userId) {
        uni.showToast({
          title: "暂无医生联系方式",
          icon: "none",
        });
      }
      uni.navigateTo({
        url:
          "/pages/message/im/index?name=" +
          name +
          "&formUserId=" +
          item.userId +
          "&headIcon=" +
          item.picture,
      });
    },
    // 获取头像文字
    getAvatarText() {
      const name = this.doctorInfo?.name;
      if (!name) return "医生";
      return name.length > 1 ? name.slice(-2) : name;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/flowup-design.scss";

.doctor-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 20rpx;
  padding-bottom: 40rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 20rpx;
  border-radius: $border-radius-base;
  background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
  transition: all 0.3s ease;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    transform: translateY(2rpx);
    box-shadow: $box-shadow-sm;
  }
}

.contact-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.contact-text {
  font-size: $font-size-base;
  color: $primary-dark-color;
  font-weight: $font-weight-medium;
}

.schedule-list {
  margin-top: 20rpx;
}

.schedule-item {
  display: flex;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

  &:last-child {
    border-bottom: none;
  }
}

.schedule-day {
  width: 150rpx;
  font-size: $font-size-base;
  color: $primary-dark-color;
  font-weight: $font-weight-semibold;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.schedule-time {
  flex: 1;
  font-size: $font-size-base;
  color: $gray-700;
  padding-left: 20rpx;
}

.schedule-location {
  font-size: $font-size-base;
  color: $gray-600;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.review-list {
  margin-top: 20rpx;
}

.review-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

  &:last-child {
    border-bottom: none;
  }
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
}

.review-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 15rpx;
  background-color: #f0f0f0;
}

.review-info {
  flex: 1;
}

.review-name {
  font-size: $font-size-base;
  color: $gray-800;
  font-weight: $font-weight-semibold;
}

.review-date {
  font-size: $font-size-xs;
  color: $gray-600;
  margin-top: 5rpx;
}

.review-rating {
  display: flex;
}

.star-icon {
  width: 30rpx;
  height: 30rpx;
  margin-left: 5rpx;
}

.review-content {
  font-size: $font-size-base;
  color: $gray-700;
  line-height: 1.5;
  padding-left: 75rpx;
}

.user-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-top: 12rpx;

  .meta-item {
    font-size: $font-size-xs;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.2);
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;
  }
}

.header-main {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.flowup-section-header {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .doctor-container {
    padding: 16rpx;
    padding-bottom: 30rpx;
  }

  .user-meta {
    gap: 12rpx;
    margin-top: 8rpx;

    .meta-item {
      font-size: 20rpx;
      padding: 4rpx 12rpx;
    }
  }

  .contact-icon {
    width: 50rpx;
    height: 50rpx;
  }

  .contact-text {
    font-size: $font-size-sm;
  }

  .schedule-day {
    width: 120rpx;
    font-size: $font-size-sm;
  }

  .schedule-time,
  .schedule-location {
    font-size: $font-size-sm;
    padding-left: 15rpx;
  }

  .review-name {
    font-size: $font-size-sm;
  }

  .review-date {
    font-size: 20rpx;
  }

  .review-content {
    font-size: $font-size-sm;
    padding-left: 60rpx;
  }

  .star-icon {
    width: 24rpx;
    height: 24rpx;
  }
  
  .schedule-day,
  .schedule-location {
    gap: 6rpx;
  }
}
</style>