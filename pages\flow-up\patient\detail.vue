<template>
  <view class="patient-detail-container" v-if="!loading">
    <!-- 顶部患者信息卡片 -->
    <view class="patient-header-card">
      <view class="patient-avatar-section">
        <view class="patient-avatar">
          <text class="avatar-text">{{ getAvatarText(dataForm.name) }}</text>
        </view>
        <view class="patient-basic-info">
          <view class="patient-name">{{ dataForm.name || "未知患者" }}</view>
          <view class="patient-meta">
            <view class="meta-item">
              <text class="meta-label">年龄:</text>
              <text class="meta-value">{{ dataForm.age || "未知" }}岁</text>
            </view>
            <view class="meta-item">
              <text class="meta-label">性别:</text>
              <text class="meta-value">{{ getSexText() }}</text>
            </view>
          </view>
        </view>
        <view class="patient-status" v-if="false">
          <view class="status-badge" :class="getStatusClass()">
            {{ getStatusText() }}
          </view>
        </view>
      </view>
    </view>

    <!-- 详细信息区域 -->
    <view class="detail-sections">
      <!-- 基本信息 -->
      <view class="detail-section">
        <view class="section-header">
          <view class="section-icon">👤</view>
          <text class="section-title">基本信息</text>
        </view>
        <view class="section-content">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">身份证号</text>
              <text class="info-value">{{
                formatIdCard(dataForm.idCard)
              }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">详细地址</text>
              <XundaLocation v-model:modelValue="dataForm.addressDetail" :disabled="true" :detailed="true"
                :clearable="false" :enableLocationScope="true" :adjustmentScope="500" :enableDesktopLocation="true"
                :locationScope="locationScope" @change="handleLocation"></XundaLocation>
            </view>
          </view>
        </view>
      </view>

      <!-- 住院信息 -->
      <view class="detail-section">
        <view class="section-header">
          <view class="section-icon">🏥</view>
          <text class="section-title">住院信息</text>
        </view>
        <view class="section-content">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">住院号</text>
              <text class="info-value">{{
                dataForm.admissionNo || "暂无"
              }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">入院日期</text>
              <text class="info-value">{{
                formatDate(dataForm.admissionDate)
              }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">出院日期</text>
              <text class="info-value">{{
                formatDate(dataForm.dischargeDate)
              }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="info-label">入院诊断</text>
              <text class="info-value diagnosis-text">{{
                dataForm.admittingDiagnosis || "暂无诊断信息"
              }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 随访信息 -->
      <view class="detail-section">
        <view class="section-header">
          <view class="section-icon">📋</view>
          <text class="section-title">随访信息</text>
        </view>
        <view class="section-content">
          <view class="follow-stats">
            <view class="stat-card">
              <view class="stat-number">{{ dataForm.fcount || 0 }}</view>
              <view class="stat-label">随访次数</view>
            </view>
            <view class="stat-card" v-if="false">
              <view class="stat-number">{{ dataForm.rate || 0 }}%</view>
              <view class="stat-label">随访率</view>
            </view>
            <view class="stat-card" v-if="false">
              <view class="stat-number">{{ dataForm.tnumber || 0 }}</view>
              <view class="stat-label">总人数</view>
            </view>
            <view class="stat-card" v-if="false">
              <view class="stat-number">{{ dataForm.dnumber || 0 }}</view>
              <view class="stat-label">应完成</view>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">随访类型</text>
              <text class="info-value">{{ getFollowTypeText() }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item full-width">
              <text class="info-label">联系地址</text>
              <text class="info-value address-text">{{
                dataForm.address || "暂无联系地址"
              }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <view class="action-row">
        <view class="action-btn secondary-btn" @click="closeDetail">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">返回</text>
        </view>
        <view class="action-btn primary-btn" @click.stop="toEdit" v-if="btnList.includes('btn_edit')">
          <view class="btn-icon">✏️</view>
          <text class="btn-text">编辑</text>
        </view>
        <view class="action-btn danger-btn" @click="removeBind">
          <view class="btn-icon">🔗</view>
          <text class="btn-text">撤销绑定</text>
        </view>
      </view>
      <view class="action-row">
        <view class="action-btn follow-btn" @click="goToVisitRecord">
          <view class="btn-icon">📋</view>
          <text class="btn-text">随访</text>
        </view>
        <view class="action-btn chat-btn" @click="sendMessage">
          <view class="btn-icon">💬</view>
          <text class="btn-text">聊天</text>
        </view>
      </view>
    </view>
    <u-modal v-model="show" :content="content" width="70%" border-radius="16" :content-style="{
      fontSize: '28rpx',
      padding: '20rpx',
      lineHeight: '44rpx',
      textAlign: 'left',
    }" :titleStyle="{ padding: '20rpx' }" :confirm-style="{ height: '80rpx', lineHeight: '80rpx' }" :title="title"
      confirm-text="确定">
    </u-modal>
  </view>
</template>
<script>
import { getDictionaryDataSelector } from "@/api/common";
import { getDetailInfo } from "@/api/flow-up/patient";
import { removeBindAsync } from "@/api/flow-up/physician";

export default {
  data() {
    return {
      btnLoading: false,
      loading: false,
      text: "提示：测试文本",
      tableKey: "",
      timeKey: +new Date(),
      dataForm: {
        id: "",
      },
      rules: {},
      labelwidth: 100 * 1.5,
      menuId: "",
      btnList: [],
      idList: [],
      ruleList: {},
      childIndex: -1,
      content: "",
      title: "",
      show: false,
      optionsObj: {
        //选项配置
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        }, // 默认下拉选择键值
      },
    };
  },
  onLoad(option) {
    this.menuId = option.menuId;
    this.btnList = option.btnList.split(",");
    uni.setNavigationBarTitle({
      title: "详情",
    });
    this.dataForm.id = option.id || 0;
    this.idList = option.idList ? option.idList.split(",") : [];
    this.intSelectOption();
    this.initData();
    uni.$on("refresh", () => {
      //执行接口更新数据
      this.initData();
    });
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  beforeDestroy() {
    uni.$off("refresh");
  },
  onReady() {
  },
  watch: {
    dataForm: {
      handler() {
        // 监听数据变化
      },
      deep: true,
    },
  },
  methods: {
    intSelectOption() {
      getDictionaryDataSelector("Sex").then((res) => {
        this.optionsObj.SexOptions = res.data.list;
      });
      getDictionaryDataSelector("FlowType").then((res) => {
        this.optionsObj.FlowTypeOptions = res.data.list;
      });
    },

    // 获取头像文字
    getAvatarText(name) {
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取性别文本
    getSexText() {
      if (!this.dataForm.sex || !this.optionsObj.SexOptions) return "未知";
      const sexOption = this.optionsObj.SexOptions.find(
        (item) => item.enCode === this.dataForm.sex
      );
      return sexOption ? sexOption.fullName : "未知";
    },

    // 获取随访类型文本
    getFollowTypeText() {
      if (!this.dataForm.type || !this.optionsObj.FlowTypeOptions)
        return "暂无";
      const typeOption = this.optionsObj.FlowTypeOptions.find(
        (item) => item.enCode === this.dataForm.type
      );
      return typeOption ? typeOption.fullName : "暂无";
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return "暂无";
      return idCard;
      if (idCard.length <= 8) return idCard;
      return idCard.slice(0, 4) + "****" + idCard.slice(-4);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "暂无";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 获取状态文本
    getStatusText() {
      if (this.dataForm.fcount > 0) {
        return "已随访";
      }
      return "待随访";
    },

    // 获取状态样式类
    getStatusClass() {
      if (this.dataForm.fcount > 0) {
        return "status-completed";
      }
      return "status-pending";
    },
    onCollapseChange() {
      uni.$emit("initCollapse");
    },
    // 关闭详情页
    closeDetail() {
      uni.navigateBack();
    },

    // 跳转到随访记录页面
    goToVisitRecord() {
      if (!this.dataForm.id) {
        uni.showToast({
          title: "患者信息不完整",
          icon: "none",
        });
        return;
      }
      uni.navigateTo({
        url: `/pages/flow-up/doVisitRecord?patientId=${this.dataForm.id
          }&patientName=${encodeURIComponent(
            this.dataForm.name || "未知患者"
          )}&actionType=add`,
        fail: (err) => {
          console.error("跳转随访页面失败:", err);
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        },
      });
    },
    dataAll() { },
    // 获取详情信息
    initData() {
      this.$nextTick(function () {
        if (this.dataForm.id) {
          this.loading = true;
          getDetailInfo(this.dataForm.id).then((res) => {
            this.dataInfo(res.data);
            this.loading = false;
          });
        }
      });
    },
    // 跳转编辑页
    toEdit() {
      uni.navigateTo({
        url:
          "./form?menuId=" +
          this.menuId +
          "&jurisdictionType=btn_edit&id=" +
          this.dataForm.id +
          "&idList=" +
          this.idList,
      });
    },
    selfInit() {
      this.$store.commit("base/UPDATE_RELATION_DATA", {});
    },
    dataInfo(dataAll) {
      let _dataAll = dataAll;
      this.dataForm = _dataAll;
      this.collapse();
    },
    collapse() {
      setTimeout(() => { }, 1000);
    },
    sendMessage() {
      const item = this.dataForm;
      const name = item.name;
      if (!item.userId) {
        uni.showToast({
          title: "患者暂未绑定用户",
          icon: "none",
        });
      }
      uni.navigateTo({
        url:
          "/pages/message/im/index?name=" +
          name +
          "&formUserId=" +
          item.userId +
          "&headIcon=" +
          item.picture,
      });
    },
    removeBind() {
      const item = this.dataForm;
      uni.showModal({
        title: "提示",
        content: `确定要撤销与患者 ${item.name || "未知"} 的绑定吗？`,
        success: (res) => {
          if (res.confirm) {
            removeBindAsync({
              patientId: item.id,
            }).then((res) => {
              if (res.code === 200) {
                this.closeDetail();
                uni.showToast({
                  title: "撤销绑定成功",
                  icon: "none",
                });
              }
            });
          }
        },
      });
    },
  },
};
</script>
<style lang="scss">
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.patient-detail-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
}

// 顶部患者信息卡片
.patient-header-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .patient-avatar-section {
    display: flex;
    align-items: flex-start;
    gap: 24rpx;

    .patient-avatar {
      width: 96rpx;
      height: 96rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 4rpx solid rgba(255, 255, 255, 0.3);

      .avatar-text {
        color: #fff;
        font-size: 32rpx;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
    }

    .patient-basic-info {
      flex: 1;
      min-width: 0;

      .patient-name {
        font-size: 40rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 16rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .patient-meta {
        display: flex;
        gap: 32rpx;

        .meta-item {
          display: flex;
          align-items: center;
          gap: 8rpx;

          .meta-label {
            font-size: 24rpx;
            color: rgba(255, 255, 255, 0.8);
          }

          .meta-value {
            font-size: 26rpx;
            color: #fff;
            font-weight: 600;
          }
        }
      }
    }

    .patient-status {
      .status-badge {
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 600;
        border: 2rpx solid rgba(255, 255, 255, 0.3);

        &.status-completed {
          background: rgba(76, 175, 80, 0.9);
          color: #fff;
        }

        &.status-pending {
          background: rgba(255, 193, 7, 0.9);
          color: #fff;
        }
      }
    }
  }
}

// 详细信息区域
.detail-sections {
  .detail-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }
    }

    .section-content {
      padding: 28rpx;

      .info-row {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .info-item {
          flex: 1;
          min-width: 0;
          padding: 20rpx;
          background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
          border-radius: 16rpx;
          border: 1rpx solid rgba(25, 118, 210, 0.1);

          &.full-width {
            flex: 100%;
          }

          .info-label {
            font-size: 24rpx;
            color: #1565c0;
            font-weight: 600;
            margin-bottom: 8rpx;
            display: block;
          }

          .info-value {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.4;

            &.address-text,
            &.diagnosis-text {
              word-break: break-all;
              line-height: 1.6;
            }
          }
        }
      }

      // 随访统计卡片
      .follow-stats {
        display: flex;
        gap: 16rpx;
        margin-bottom: 24rpx;

        .stat-card {
          flex: 1;
          background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
          border-radius: 16rpx;
          padding: 24rpx 16rpx;
          text-align: center;
          color: #fff;
          box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

          .stat-number {
            font-size: 36rpx;
            font-weight: 700;
            margin-bottom: 8rpx;
            text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
          }

          .stat-label {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
          }
        }
      }
    }
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.chat-btn {
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
        }
      }

      &.danger-btn {
        background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(244, 67, 54, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.3);
        }
      }

      &.follow-btn {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(255, 152, 0, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
        }
      }

      &.full-width-btn {
        flex: 100%;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .patient-detail-container {
    padding: 16rpx;
    padding-bottom: 180rpx;
  }

  .patient-header-card {
    padding: 24rpx;

    .patient-avatar-section {
      gap: 16rpx;

      .patient-avatar {
        width: 80rpx;
        height: 80rpx;

        .avatar-text {
          font-size: 28rpx;
        }
      }

      .patient-basic-info {
        .patient-name {
          font-size: 36rpx;
        }

        .patient-meta {
          gap: 24rpx;

          .meta-item {
            .meta-label {
              font-size: 22rpx;
            }

            .meta-value {
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }

  .detail-sections {
    .detail-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .info-row {
          gap: 16rpx;

          .info-item {
            padding: 16rpx;

            .info-label {
              font-size: 22rpx;
            }

            .info-value {
              font-size: 26rpx;
            }
          }
        }

        .follow-stats {
          gap: 12rpx;

          .stat-card {
            padding: 20rpx 12rpx;

            .stat-number {
              font-size: 32rpx;
            }

            .stat-label {
              font-size: 20rpx;
            }
          }
        }
      }
    }
  }

  .action-buttons {
    padding: 20rpx 16rpx;

    .action-row {
      gap: 12rpx;

      .action-btn {
        height: 80rpx;

        .btn-icon {
          font-size: 24rpx;
        }

        .btn-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>
