<template>
  <view class="dynamicModel-list-v">
    <view class="head-warp com-dropdown">
      <u-dropdown class="u-dropdown" ref="uDropdown" @open="openData" hidden>
        <u-dropdown-item title="排序" :options="sortOptions">
          <view class="screen-box">
            <view class="screen-list" v-if="sortOptions.length">
              <view class="u-p-l-20 u-p-r-20 list">
                <scroll-view scroll-y="true" style="height: 100%">
                  <u-cell-group :border="false">
                    <u-cell-item
                      @click="cellClick(item)"
                      :arrow="false"
                      :title="item.label"
                      v-for="(item, index) in sortOptions"
                      :key="index"
                      :title-style="{
                        color: sortValue.includes(item.value)
                          ? '#2979ff'
                          : '#606266',
                      }"
                    >
                      <u-icon
                        v-if="sortValue.includes(item.value)"
                        name="checkbox-mark"
                        color="#2979ff"
                        size="32"
                      />
                    </u-cell-item>
                  </u-cell-group>
                </scroll-view>
              </view>
            </view>
            <view v-else class="notData-box u-flex-col">
              <view class="u-flex-col notData-inner">
                <image :src="icon" class="iconImg"></image>
                <text class="notData-inner-text">暂无数据</text>
              </view>
            </view>
            <view
              class="buttom-actions"
              v-if="sortOptions.length"
              style="z-index: 1"
            >
              <u-button class="buttom-btn" @click="handleSortReset"
                >清空</u-button
              >
              <u-button
                class="buttom-btn"
                type="primary"
                @click="handleSortSearch"
                >确定</u-button
              >
            </view>
          </view>
        </u-dropdown-item>
        <u-dropdown-item title="筛选">
          <view class="screen-box">
            <view class="screen-list">
              <view class="u-p-l-20 u-p-r-20 list">
                <scroll-view scroll-y="true" style="height: 100%">
                  <u-form :label-width="150">
                    <u-form-item label="姓名">
                      <XundaInput
                        v-model="searchForm.name"
                        placeholder="请输入姓名"
                        :key="'searchForm.name'"
                        input-align="right"
                        clearable
                        :templateJson="interfaceRes.name"
                        selectType="all"
                      />
                    </u-form-item>
                  </u-form>
                </scroll-view>
              </view>
              <view class="buttom-actions">
                <u-button class="buttom-btn" @click="reset">重置</u-button>
                <u-button
                  class="buttom-btn"
                  type="primary"
                  @click="closeDropdown"
                  >检索</u-button
                >
              </view>
              <view
                class="dropdown-slot-bg"
                @click="$refs.uDropdown.close()"
              ></view>
            </view>
          </view>
        </u-dropdown-item>
      </u-dropdown>
    </view>
    <view class="search-box_sticky">
      <view class="search-box">
        <u-search
          placeholder="请输入关键词搜索"
          v-model="listQuery.keyword"
          height="72"
          :show-action="false"
          @change="search"
          bg-color="#f0f2f6"
          shape="square"
        >
        </u-search>
      </view>
    </view>

    <view class="list-warp">
      <mescroll-uni
        ref="mescrollRef"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        :up="upOption"
        :fixed="false"
      >
        <view class="list u-p-b-20 u-p-l-20 u-p-r-20">
          <view class="list-box">
            <uni-swipe-action ref="swipeAction">
              <uni-swipe-action-item
                v-for="(item, index) in list"
                :key="index"
                :threshold="0"
                :right-options="options"
              >
                <view class="item">
                  <u-avatar
                    size="200"
                    :src="pictureUrl(item.picture)"
                    @click="doPreviewImage(item.picture)"
                  ></u-avatar>
                  <view
                    class="u-line-1 item-cell"
                    v-for="(column, i) in columnList"
                    :key="i"
                    @click="goDetail(item)"
                  >
                    <template v-if="column.xundaKey != 'table'">
                      <text class="item-cell-label">{{ column.label }}:</text>
                      <text
                        class="item-cell-content"
                        v-if="
                          ['calculate', 'inputNumber'].includes(
                            column.xundaKey
                          ) && column.thousands
                        "
                        >{{ toThousands(item[column.prop], column) }}</text
                      >
                      <text
                        class="item-cell-content text-primary"
                        v-else-if="column.xundaKey == 'relationForm'"
                        @click.stop="relationFormClick(item, column)"
                        >{{ item[column.prop] }}</text
                      >
                      <text
                        class="item-cell-content"
                        v-else-if="column.key == 'sex'"
                        >{{
                          xunda.dictionaryText(
                            item[column.prop],
                            optionsObj.sexOptions
                          )
                        }}</text
                      >
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'sign'"
                      >
                        <XundaSign
                          v-model="item[column.prop]"
                          align="left"
                          detailed
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'signature'"
                      >
                        <XundaSignature
                          v-model="item[column.prop]"
                          align="left"
                          detailed
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'uploadImg'"
                        @click.stop
                      >
                        <XundaUploadImg
                          v-model="item[column.prop]"
                          detailed
                          simple
                          v-if="item[column.prop] && item[column.prop].length"
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'uploadFile'"
                        @click.stop
                      >
                        <XundaUploadFile
                          v-model="item[column.prop]"
                          detailed
                          v-if="item[column.prop] && item[column.prop].length"
                          align="left"
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'rate'"
                      >
                        <XundaRate
                          v-model="item[column.prop]"
                          :max="column.count"
                          :allowHalf="column.allowHalf"
                          disabled
                        />
                      </view>
                      <view
                        class="item-cell-content item-cell-slider"
                        v-else-if="column.xundaKey == 'slider'"
                      >
                        <XundaSlider
                          v-model="item[column.prop]"
                          :min="column.min"
                          :max="column.max"
                          :step="column.step"
                          disabled
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'input'"
                      >
                        <XundaInput
                          v-model="item[column.prop]"
                          detailed
                          showOverflow
                          :useMask="column.useMask"
                          :maskConfig="column.maskConfig"
                          align="left"
                        />
                      </view>
                      <text class="item-cell-content" v-else>{{
                        item[column.prop]
                      }}</text>
                    </template>
                  </view>
                </view>
                <template v-slot:right>
                  <view class="right-option-box">
                    <view
                      class="right-option"
                      v-for="(it, i) in options"
                      @click="handleClick(index)"
                      :key="i"
                    >
                      <text>{{ it.text }}</text>
                    </view>
                  </view>
                </template>
              </uni-swipe-action-item>
            </uni-swipe-action>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <view class="com-addBtn" @click="addPage()">
      <u-icon name="plus" size="60" color="#fff" />
    </view>
  </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
import request from "@/utils/request";
import { columnList as appColumnList } from "@/pages/flow-up/physician/index.js";
import { getPhysicianAllDictionaryData } from "@/pages/flow-up/physician/index.js";
import { getList } from "@/pages/flow-up/physician/api.js";
export default {
  mixins: [MescrollMixin],
  components: {},
  data() {
    return {
      isAuthority: true,
      icon: resources.message.nodata,
      sortValue: [],
      searchForm: {
        name: undefined,
      },
      downOption: {
        use: true,
        auto: false,
      },
      dataOptions: {},
      upOption: {
        page: {
          num: 0,
          size: 20,
          time: null,
        },
        empty: {
          use: true,
          icon: resources.message.nodata,
          tip: "暂无数据",
          fixed: true,
          zIndex: 5,
        },
        textNoMore: "没有更多数据",
        toTop: {
          bottom: 250,
        },
      },
      list: [],
      appColumnList: appColumnList,
      listQuery: {
        moduleId: "661876504619124229",
        sidx: "",
        keyword: "",
        json: "",
      },
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#dd524d",
          },
        },
      ],
      sortOptions: [],
      ableAll: {},
      interfaceRes: {
        name: [],
      },
      menuId: "",
      columnList: [],
      key: new Date(),
      dataValue: {},
      userInfo: {},
      firstInitSearchData: false,
      tabList: [],
      tabKey: 0,
      optionsObj: {
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
      },
    };
  },
  computed: {},
  onLoad(e) {
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.menuId = e.menuId;
    this.setDefaultQuery();
    this.dataAll();
    this.getColumnList();
  },
  onShow() {
    this.$nextTick(() => {
      this.mescroll.resetUpScroll();
    });
  },
  onUnload() {
    uni.$off("refresh");
  },
  methods: {
    pictureUrl(picture) {
      return picture
        ? this.define.baseURL + picture
        : this.define.baseURL + "/api/file/Image/userAvatar/001.png";
    },
    toThousands(val, column) {
      if (val) {
        let valList = val.toString().split(".");
        let num = Number(valList[0]);
        let newVal = column.thousands ? num.toLocaleString() : num;
        return valList[1] ? newVal + "." + valList[1] : newVal;
      } else {
        return val;
      }
    },
    dataAll() {
      getPhysicianAllDictionaryData(this.optionsObj);
    },
    openData(e) {},
    setDefaultQuery() {
      const defaultSortConfig = [];
      const sortField = defaultSortConfig.map(
        (o) => (o.sort === "desc" ? "-" : "") + o.field
      );
      this.listQuery.sidx = sortField.join(",");
    },
    //初始化查询的默认数据
    async initSearchData() {
      this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
    },
    relationFormClick(item, column) {
      let vModel = column.__vModel__ + "_id";
      let id = item[vModel];
      let modelId = column.modelId;
      if (!id || !modelId) return;
      let config = {
        modelId: modelId,
        id: id,
        formTitle: "详情",
        noShowBtn: 1,
      };
      this.$nextTick(() => {
        const url =
          "/pages/apply/dynamicModel/detail?config=" +
          this.xunda.base64.encode(JSON.stringify(config), "UTF-8");
        uni.navigateTo({
          url: url,
        });
      });
    },
    async upCallback(page) {
      if (!this.firstInitSearchData) {
        await this.initSearchData();
        this.firstInitSearchData = true;
      }
      const query = {
        currentPage: page.num,
        pageSize: page.size,
        menuId: this.menuId,
        ...this.listQuery,
        ...this.searchForm,
        dataType: 0,
      };
      getList(query)
        .then((res) => {
          let _list = res.data.list;
          this.mescroll.endSuccess(_list.length);
          if (page.num == 1) this.list = [];
          const list = _list.map((o) => ({
            show: false,
            ...o,
          }));
          this.list = this.list.concat(_list);
        })
        .catch(() => {
          this.mescroll.endSuccess(this.list.length);
        });
    },
    handleClick(index, index1) {
      const item = this.list[index];
      //提示是否确认删除弹窗
      uni.showModal({
        title: "提示",
        content: "确定删除?",
        success: (res) => {
          if (res.confirm) {
            this.delete(item);
          }
        },
      });
    },
    delete(item) {
      request({
        url: "/api/flowUp/physician/" + item.id,
        method: "delete",
      }).then((res) => {
        uni.showToast({
          title: res.msg,
          complete: () => {
            this.$u.toast(res.msg);
            this.mescroll.resetUpScroll();
          },
        });
      });
    },
    open(index) {
      this.list[index].show = true;
      this.list.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    search() {
      if (this.isPreview == "1") return;
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      }, 300);
    },
    goDetail(item) {
      let id = item.id;
      let btnType = "";
      let btnList = [];
      btnList.push("btn_edit");
      btnList.push("btn_detail");
      if (btnList.length == 0) return;
      this.jumPage(id, btnList);
    },
    addPage() {
      this.jumPage();
    },
    jumPage(id, btnList) {
      let idVal = id ? "&id=" + id : "";
      let idList = [];
      for (let i = 0; i < this.list.length; i++) {
        idList.push(this.list[i].id);
      }
      let idListVal = "&idList=" + idList;
      if (!id) {
        uni.navigateTo({
          url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_add",
        });
      } else if (btnList.includes("btn_detail")) {
        uni.navigateTo({
          url:
            "./detail?menuId=" +
            this.menuId +
            "&btnList=" +
            btnList +
            idVal +
            idListVal,
        });
      } else if (btnList.includes("btn_edit")) {
        uni.navigateTo({
          url:
            "./form?menuId=" +
            this.menuId +
            "&jurisdictionType=btn_edit&btnList=" +
            btnList +
            idVal +
            idListVal,
        });
      }
    },
    getColumnList() {
      let columnPermissionList = [];
      let _appColumnList = this.appColumnList;
      for (let i = 0; i < _appColumnList.length; i++) {
        columnPermissionList.push(_appColumnList[i]);
      }
      this.columnList = columnPermissionList;
    },
    cellClick(item) {
      const findIndex = this.sortValue.findIndex((o) => o === item.value);
      if (findIndex < 0) {
        const findLikeIndex = this.sortValue.findIndex(
          (o) => o.indexOf(item.sidx) > -1
        );
        if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
        this.sortValue.push(item.value);
      } else {
        this.sortValue.splice(findIndex, 1);
      }
    },
    handleSortReset() {
      this.sortValue = [];
    },
    handleSortSearch() {
      if (this.sortValue.length) {
        this.listQuery.sidx = this.sortValue.join(",");
      } else {
        this.setDefaultQuery();
      }
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    reset() {
      this.searchForm = JSON.parse(JSON.stringify(this.dataValue));
      this.key = new Date();
    },
    closeDropdown() {
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    dataList(data) {
      let _list = data.list;
      return _list;
    },
    doPreviewImage(current) {
      const image = this.pictureUrl(current);
      uni.previewImage({
        urls: [image],
        current: image,
        success: () => {},
        fail: () => {
          uni.showToast({
            title: "预览图片失败",
            icon: "none",
          });
        },
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
  height: 100%;
  /* #ifdef MP-ALIPAY */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  /* #endif */
}

:deep(.u-cell) {
  padding: 0rpx;
  height: 112rpx;
}

.screen-box {
  background-color: #fff;
  height: 100%;

  .screen-list {
    width: 100%;
    height: 100%;

    .list {
      height: calc(100% - 88rpx);
      overflow-y: scroll;
    }
  }
}

.item {
  padding: 0 !important;
}

.notData-box {
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding-bottom: 200rpx;

  .notData-inner {
    width: 280rpx;
    height: 308rpx;
    align-items: center;

    .iconImg {
      width: 100%;
      height: 100%;
    }

    .notData-inner-text {
      padding: 30rpx 0;
      color: #909399;
    }
  }
}

.right-option-box {
  display: flex;
  width: max-content;

  .right-option {
    width: 144rpx;
    height: 100%;
    font-size: 16px;
    color: #fff;
    background-color: #dd524d;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .more-option {
    background-color: #1890ff;
  }
}
</style>
