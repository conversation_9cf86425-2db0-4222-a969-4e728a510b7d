<template>
  <u-form
    :model="formData"
    ref="dataForm"
    :errorType="['toast']"
    label-position="left"
    label-width="150"
  >
    <u-form-item
      :label="item.label"
      :prop="item.id"
      v-for="(item, i) in formConfCopy"
      :key="i"
    >
      <XundaInput
        v-if="useInputList.includes(item.__config__.xundaKey)"
        input-align="right"
        v-model="formData[item.id]"
        :placeholder="
          webType != 4
            ? '请输入' + item.label
            : '请输入' + item.__config__.label
        "
        clearable
      />
      <template
        v-if="['inputNumber', 'calculate'].includes(item.__config__.xundaKey)"
      >
        <XundaInputNumber
          v-model="formData[item.id]"
          :precision="item.precision"
          :placeholder="
            webType != 4 ? item.placeholder : '请输入' + item.__config__.label
          "
          v-if="item.__config__.isFromParam"
        />
        <XundaNumberRange
          v-model="formData[item.id]"
          :precision="
            !item.precision && item.__config__.xundaKey == 'calculate'
              ? 0
              : item.precision
          "
          v-else
        />
      </template>
      <template v-if="['rate', 'slider'].includes(item.__config__.xundaKey)">
        <XundaNumberRange
          v-model="formData[item.id]"
          :precision="item.allowHalf ? 1 : 0"
        />
      </template>
      <XundaSelect
        v-if="useSelectList.includes(item.__config__.xundaKey)"
        v-model="formData[item.id]"
        :placeholder="item.placeholder"
        :options="item.options"
        :props="item.props"
        :multiple="item.searchMultiple"
        :key="key"
        filterable
      />
      <XundaCascader
        v-if="item.__config__.xundaKey === 'cascader'"
        v-model="formData[item.id]"
        :placeholder="item.placeholder"
        :options="item.options"
        :props="item.props"
        filterable
        :showAllLevels="item.showAllLevels"
        :multiple="item.searchMultiple"
      />
      <XundaAutoComplete
        v-if="item.__config__.xundaKey === 'autoComplete'"
        v-model="formData[item.id]"
        :interfaceName="item.interfaceName"
        :placeholder="item.placeholder"
        :interfaceId="item.interfaceId"
        :total="item.total"
        :templateJson="item.templateJson"
        :formData="formData"
        :relationField="item.relationField"
        :propsValue="item.propsValue"
        :clearable="item.clearable"
      />
      <XundaGroupSelect
        v-if="item.__config__.xundaKey === 'groupSelect'"
        v-model="formData[item.id]"
        :vModel="item.id"
        :multiple="item.searchMultiple"
        :disabled="item.disabled"
        :placeholder="item.placeholder"
        :ableIds="item.ableIds"
        :selectType="item.selectType"
      />
      <XundaRoleSelect
        v-if="item.__config__.xundaKey === 'roleSelect'"
        v-model="formData[item.id]"
        :multiple="item.searchMultiple"
        :disabled="item.disabled"
        :placeholder="item.placeholder"
        :ableIds="item.ableIds"
        :selectType="item.selectType"
      />
      <XundaOrganizeSelect
        v-if="
          ['organizeSelect', 'currOrganize'].includes(item.__config__.xundaKey)
        "
        v-model="formData[item.id]"
        :placeholder="'请选择' + item.label"
        :multiple="item.searchMultiple"
        :ableIds="item.ableIds"
        :selectType="item.selectType"
      />
      <XundaDepSelect
        v-if="['depSelect', 'currDept'].includes(item.__config__.xundaKey)"
        v-model="formData[item.id]"
        :placeholder="'请选择' + item.label"
        :ableIds="item.ableIds"
        :selectType="item.selectType"
        :multiple="item.searchMultiple"
      />
      <XundaPosSelect
        v-if="['posSelect', 'currPosition'].includes(item.__config__.xundaKey)"
        v-model="formData[item.id]"
        :placeholder="'请选择' + item.label"
        :ableIds="item.ableIds"
        :selectType="item.selectType"
        :multiple="item.searchMultiple"
      />
      <XundaUserSelect
        v-if="
          ['userSelect', 'createUser', 'modifyUser'].includes(
            item.__config__.xundaKey
          )
        "
        v-model="formData[item.id]"
        :placeholder="'请选择' + item.label"
        :ableDepIds="item.ableDepIds"
        :ableIds="item.ableIds"
        :selectType="item.selectType != 'custom' ? 'all' : 'custom'"
        :multiple="item.searchMultiple"
      />
      <XundaUsersSelect
        v-if="item.__config__.xundaKey === 'usersSelect'"
        v-model="formData[item.id]"
        :multiple="item.searchMultiple"
        :placeholder="item.placeholder"
        :selectType="item.selectType"
        :ableIds="item.ableIds"
        :clearable="item.clearable"
      />
      <XundaTreeSelect
        v-if="item.__config__.xundaKey === 'treeSelect'"
        v-model="formData[item.id]"
        :options="item.options"
        :props="item.props"
        :placeholder="item.placeholder"
        filterable
        :multiple="item.searchMultiple"
      />
      <XundaAreaSelect
        v-if="item.__config__.xundaKey === 'areaSelect'"
        v-model="formData[item.id]"
        :placeholder="item.placeholder"
        :level="item.level"
        :multiple="item.searchMultiple"
      />
      <template
        v-if="
          useDateList.includes(item.__config__.xundaKey) ||
          item.__config__.xundaKey === 'datePicker'
        "
      >
        <XundaDatePicker
          v-model="formData[item.id]"
          :format="item.format"
          v-if="item.__config__.isFromParam"
        />
        <XundaDateRange
          v-model="formData[item.id]"
          :format="item.format"
          v-else
        />
      </template>
      <XundaTimeRange
        v-if="item.__config__.xundaKey === 'timePicker'"
        v-model="formData[item.id]"
        :format="item.format"
      />
    </u-form-item>
  </u-form>
</template>
<script>
import { getDictionaryDataSelector, getDataInterfaceRes } from "@/api/common";
const dyOptionsList = ["radio", "checkbox", "select", "cascader", "treeSelect"];
const useSelectList = ["radio", "checkbox", "select"];
const useInputList = [
  "input",
  "textarea",
  "text",
  "link",
  "billRule",
  "location",
];
const useDateList = ["createTime", "modifyTime"];
const useArrList = [
  "cascader",
  "address",
  "numInput",
  "calculate",
  ...useDateList,
];
export default {
  props: ["formConf", "webType", "searchFormData"],
  data() {
    const data = {
      useInputList,
      useDateList,
      useSelectList,
      formConfCopy: this.$u.deepClone(this.formConf),
      formData: this.$u.deepClone(this.searchFormData),
      key: +new Date(),
    };
    this.initRelationForm(data.formConfCopy);
    this.initFormData(data.formConfCopy, data.formData);
    return data;
  },
  watch: {
    searchFormData(val) {
      this.formData = val;
    },
  },
  methods: {
    initFormData(componentList, formData) {
      componentList.forEach((cur) => {
        const config = cur.__config__;
        if (dyOptionsList.indexOf(config.xundaKey) > -1) {
          if (config.dataType === "dictionary" && config.dictionaryType) {
            getDictionaryDataSelector(config.dictionaryType).then((res) => {
              cur.options = res.data.list || [];

              this.resetForm();
            });
          }
          if (config.dataType === "dynamic" && config.propsUrl) {
            const query = {
              paramList: this.xunda.getParamList(config.templateJson) || [],
            };
            getDataInterfaceRes(config.propsUrl, query).then((res) => {
              let list = res.data || [];
              cur.options = Array.isArray(list) ? list : [];
              this.key = +new Date();
              this.resetForm();
            });
          }
        }
      });
    },
    initRelationForm(componentList) {
      componentList.forEach((cur) => {
        const config = cur.__config__;
        if (
          config.xundaKey == "relationFormAttr" ||
          config.xundaKey == "popupAttr"
        ) {
          const relationKey = cur.relationField.split("_xundaTable_")[0];
          componentList.forEach((item) => {
            const noVisibility =
              Array.isArray(item.__config__.visibility) &&
              !item.__config__.visibility.includes("app");
            if (
              relationKey == item.id &&
              (noVisibility || !!item.__config__.noShow)
            ) {
              cur.__config__.noShow = true;
            }
          });
        }
        if (cur.__config__.children && cur.__config__.children.length)
          this.initRelationForm(cur.__config__.children);
      });
    },
    allCondition() {
      for (let key in this.formData) {
        if (!this.formData[key]) this.formData[key] = undefined;
        if (
          this.formData[key] &&
          Array.isArray(this.formData[key]) &&
          !this.formData[key].length
        ) {
          this.formData[key] = undefined;
        }
      }
      return this.formData;
    },
    submitForm() {
      this.$refs.dataForm.validate((valid) => {
        if (!valid) return;
        for (let key in this.formData) {
          if (!this.formData[key]) this.formData[key] = undefined;
          if (
            this.formData[key] &&
            Array.isArray(this.formData[key]) &&
            !this.formData[key].length
          ) {
            this.formData[key] = undefined;
          }
        }
        this.$emit("submit", this.formData);
      });
    },
    resetForm() {
      this.$refs.dataForm.resetFields();
    },
  },
};
</script>
