<template>
  <view class="dynamicModel-list-v">
    <view class="head-warp com-dropdown">
      <u-dropdown class="u-dropdown" ref="uDropdown">
        <u-dropdown-item title="排序" :options="sortOptions">
          <view class="screen-box">
            <view class="screen-list" v-if="sortOptions.length">
              <view class="u-p-l-20 u-p-r-20 list">
                <scroll-view scroll-y="true" style="height: 100%">
                  <u-cell-group :border="false">
                    <u-cell-item
                      @click="cellClick(item)"
                      :arrow="false"
                      :title="item.label"
                      v-for="(item, index) in sortOptions"
                      :key="index"
                      :title-style="{
                        color: sortValue.includes(item.value)
                          ? '#2979ff'
                          : '#606266',
                      }"
                    >
                      <u-icon
                        v-if="sortValue.includes(item.value)"
                        name="checkbox-mark"
                        color="#2979ff"
                        size="32"
                      />
                    </u-cell-item>
                  </u-cell-group>
                </scroll-view>
              </view>
            </view>
            <view v-else class="notData-box u-flex-col">
              <view class="u-flex-col notData-inner">
                <image :src="icon" class="iconImg"></image>
                <text class="notData-inner-text">暂无数据</text>
              </view>
            </view>
            <view class="buttom-actions" v-if="sortOptions.length">
              <u-button class="buttom-btn" @click="handleSortReset"
                >清空</u-button
              >
              <u-button
                class="buttom-btn"
                type="primary"
                @click="handleSortSearch"
                >确定</u-button
              >
            </view>
          </view>
        </u-dropdown-item>
        <u-dropdown-item title="筛选">
          <view class="screen-box u-flex-col">
            <view
              class="screen-list"
              v-if="showParser && searchFormConf.length"
            >
              <view class="u-p-l-20 u-p-r-20 list">
                <scroll-view scroll-y="true" style="height: 100%">
                  <Parser
                    :formConf="searchFormConf"
                    :searchFormData="searchFormData"
                    :webType="config.webType"
                    ref="searchForm"
                    @submit="sumbitSearchForm"
                  />
                </scroll-view>
              </view>
              <view
                class="u-flex screen-btn"
                v-if="showParser && searchFormConf.length"
              >
                <text @click="handleReset" class="btn btn1">重置</text>
                <text @click="handleSearch" class="btn btn2">检索</text>
              </view>
            </view>
            <view v-else class="notData-box u-flex-col">
              <view class="u-flex-col notData-inner">
                <image :src="icon" class="iconImg"></image>
                <text class="notData-inner-text">暂无数据</text>
              </view>
            </view>
          </view>
        </u-dropdown-item>
      </u-dropdown>
    </view>
    <u-tabs
      :list="tabList"
      v-model="tabActiveKey"
      font-size="28"
      @change="onTabChange"
      height="80"
      name="fullName"
      v-show="showTabs"
    >
    </u-tabs>
    <view class="list-warp">
      <mescroll-uni
        ref="mescrollRef"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        :down="downOption"
        :up="upOption"
        :bottombar="false"
        :top="
          columnData.tabConfig && columnData.tabConfig.on && tabList.length
            ? 170
            : 80
        "
      >
        <view class="list u-p-b-20 u-p-l-20 u-p-r-20" ref="tableRef">
          <view class="list-box">
            <uni-swipe-action ref="swipeAction">
              <uni-swipe-action-item
                v-for="(item, index) in list"
                :key="item.id"
                :threshold="0"
              >
                <view class="item" @click="goDetail(item)">
                  <view
                    class="item-cell"
                    v-for="(column, i) in columnList"
                    :key="i"
                  >
                    <template v-if="column.xundaKey != 'table'">
                      <text class="item-cell-label">{{ column.label }}:</text>
                      <text
                        class="item-cell-content"
                        v-if="
                          ['calculate', 'inputNumber'].includes(column.xundaKey)
                        "
                      >
                        {{ toThousands(item[column.prop], column) }}
                      </text>
                      <text
                        class="item-cell-content text-primary"
                        v-else-if="column.xundaKey == 'relationForm'"
                        @click.stop="relationFormClick(item, column)"
                      >
                        {{ item[column.prop] }}
                      </text>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'sign'"
                      >
                        <XundaSign
                          v-model="item[column.prop]"
                          align="left"
                          detailed
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'signature'"
                      >
                        <XundaSignature
                          v-model="item[column.prop]"
                          align="left"
                          detailed
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'uploadImg'"
                        @click.stop
                      >
                        <XundaUploadImg
                          v-model="item[column.prop]"
                          detailed
                          simple
                          v-if="item[column.prop] && item[column.prop].length"
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'uploadFile'"
                        @click.stop
                      >
                        <XundaUploadFile
                          v-model="item[column.prop]"
                          detailed
                          v-if="item[column.prop] && item[column.prop].length"
                          align="left"
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'rate'"
                      >
                        <XundaRate
                          v-model="item[column.prop]"
                          :max="column.count"
                          :allowHalf="column.allowHalf"
                          disabled
                        />
                      </view>
                      <view
                        class="item-cell-content item-cell-slider"
                        v-else-if="column.xundaKey == 'slider'"
                      >
                        <XundaSlider
                          v-model="item[column.prop]"
                          :min="column.min"
                          :max="column.max"
                          :step="column.step"
                          disabled
                        />
                      </view>
                      <view
                        class="item-cell-content"
                        v-else-if="column.xundaKey == 'input'"
                      >
                        <XundaInput
                          v-model="item[column.prop]"
                          detailed
                          showOverflow
                          :useMask="column.useMask"
                          :maskConfig="column.maskConfig"
                          align="left"
                        />
                      </view>
                      <text class="item-cell-content" v-else>{{
                        item[column.prop]
                      }}</text>
                    </template>
                    <tableCell
                      v-else
                      @click.stop
                      class="tableCell"
                      ref="tableCell"
                      :label="column.label"
                      :childList="item[column.prop]"
                      :children="column.children"
                      :pageLen="3"
                      @cRelationForm="relationFormClick"
                    >
                    </tableCell>
                  </view>
                  <view class="item-cell" v-if="config.enableFlow == 1">
                    <text class="item-cell-label">审批状态:</text>
                    <text
                      :style="{
                        color: useDefine.getFlowStatusColor(item.flowState),
                      }"
                    >
                      {{ useDefine.getFlowStatusContent(item.flowState) }}
                    </text>
                  </view>
                </view>
                <template v-slot:right>
                  <view class="right-option-box">
                    <view
                      class="right-option more-option"
                      v-if="
                        columnData.customBtnsList &&
                        columnData.customBtnsList.length
                      "
                      @click="handleMoreClick(index)"
                    >
                      <text>更多</text>
                      <uni-icons type="down" color="#fff" size="16" />
                    </view>
                    <view
                      class="right-option"
                      v-for="(it, i) in actionOptions"
                      @click="handleClick(index)"
                      :key="i"
                      v-if="config.webType != 4"
                    >
                      <text>{{ it.text }}</text>
                    </view>
                  </view>
                </template>
              </uni-swipe-action-item>
            </uni-swipe-action>
          </view>
        </view>
      </mescroll-uni>
    </view>
    <view v-if="config.webType != 4">
      <view
        class="com-addBtn"
        v-if="
          isPreview ||
          (permission.btnPermission &&
            permission.btnPermission.includes('btn_add'))
        "
        @click="addPage()"
      >
        <u-icon name="plus" size="60" color="#fff" />
      </view>
    </view>
    <u-select
      :list="columnData.customBtnsList"
      v-model="showMoreBtn"
      @confirm="selectBtnconfirm"
    />
  </view>
</template>

<script>
import { useDefineSetting } from "@/utils/useDefineSetting";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
import tableCell from "../tableCell.vue";
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import Parser from "../parser/index.vue";
import { getModelList, deteleModel, getModelInfo } from "@/api/apply/visualDev";
import { getDataInterfaceRes } from "@/api/common";
export default {
  mixins: [MescrollMixin],
  props: ["config", "modelId", "isPreview", "title", "menuId"],
  components: {
    Parser,
    tableCell,
  },
  data() {
    return {
      useDefine: useDefineSetting(),
      tabActiveKey: 0,
      tabList: [],
      tabQueryJson: {},
      sortValue: [],
      icon: resources.message.nodata,
      downOption: {
        use: true,
        auto: false,
      },
      upOption: {
        page: {
          num: 0,
          size: 10,
          time: null,
        },
        empty: {
          use: true,
          icon: resources.message.nodata,
          tip: "暂无数据",
          fixed: true,
        },
        textNoMore: "没有更多数据",
      },
      list: [],
      listQuery: {
        sidx: "",
        keyword: "",
        queryJson: "",
      },
      actionOptions: [],
      showParser: false,
      columnData: {},
      columnList: [],
      sortList: [],
      sortOptions: [],
      searchList: [],
      searchFormConf: [],
      permission: {},
      selectListIndex: 0,
      showMoreBtn: false,
      properties: {},
      flowId: "",
      key: +new Date(),
      userInfo: {},
      searchFormData: {},
      enableFunc: {},
    };
  },
  created() {
    this.init();
  },
  computed: {
    showTabs() {
      return this.columnData?.tabConfig?.on && this.tabList.length;
    },
  },
  methods: {
    init() {
      this.userInfo = uni.getStorageSync("userInfo") || {};
      this.properties = this.config.flowTemplateJson
        ? JSON.parse(this.config.flowTemplateJson).properties
        : {};
      let columnData = this.config.appColumnData
        ? this.config.appColumnData
        : this.config.columnData
        ? this.config.columnData
        : [];
      this.columnData = JSON.parse(columnData);
      this.permission = this.$permission.getPermission(
        this.columnData,
        this.menuId,
        this.xunda.getScriptFunc
      );
      this.enableFunc = this.permission.enableFunc;
      this.getTabList();
      this.upOption.page.size = this.columnData.hasPage
        ? this.columnData.pageSize
        : 1000000;
      this.setDefaultQuery();
      this.columnList = this.permission.columnPermission || [];
      this.columnData.customBtnsList =
        this.permission.customBtnsPermission || [];
      this.columnList = this.transformColumnList(this.columnList);
      this.columnList.map((o) => {
        if (o.xundaKey != "table" && o.label.length > 4)
          o.label = o.label.substring(0, 4);
      });
      this.sortList = this.columnList.filter((o) => o.sortable);
      this.handleSearchList();
      this.handleSortList();
      this.handleDeleteBtn();
      this.key = +new Date();
    },
    //获取标签面板数据、设置标签面板默认值
    async getTabList() {
      this.tabList = [];
      if (!this.columnData.tabConfig) return;
      const list =
        this.columnData.columnOptions.filter(
          (o) => o.__vModel__ == this.columnData.tabConfig.relationField
        ) || [];
      if (list?.length) {
        this.columnData.tabConfig?.hasAllTab &&
          this.tabList.push({
            fullName: "全部",
            id: undefined,
          });
        if (
          list[0].__config__.dataType == "dictionary" &&
          list[0].__config__.dictionaryType
        ) {
          const data =
            (await baseStore.getDicDataSelector(
              list[0].__config__.dictionaryType
            )) || [];
          const options =
            list[0].props.value == "enCode"
              ? data.map((o) => ({
                  ...o,
                  id: o.enCode,
                }))
              : data;
          this.tabList = [...this.tabList, ...options];
        } else {
          this.tabList = [...this.tabList, ...list[0].options];
        }
      }
      this.tabActiveKey = 0;
      this.onTabChange(this.tabActiveKey);
    },
    onTabChange(val) {
      this.tabActiveKey = val;
      this.tabQueryJson = {};
      if (this.columnData.tabConfig.hasAllTab) {
        if (val != 0) {
          this.tabQueryJson = {
            [this.columnData.tabConfig.relationField]: this.tabList[val].id,
          };
        }
      } else {
        this.tabQueryJson = {
          [this.columnData.tabConfig.relationField]: this.tabList[val].id,
        };
      }
      let search =
        this.$refs.searchForm && this.$refs.searchForm.allCondition();
      this.listQuery.queryJson = JSON.stringify({
        ...search,
        ...this.tabQueryJson,
      });
      this.initData();
    },
    handleSearchForm(data) {
      let newData = {};
      for (let key in data) {
        if (data.hasOwnProperty(key)) {
          if (typeof data[key] === "object" && data[key] !== null) {
            for (let innerKey in data[key]) {
              if (data[key].hasOwnProperty(innerKey)) {
                let newKey = `${key}-${innerKey}`;
                newData[newKey] = data[key][innerKey];
              }
            }
          } else {
            newData[key] = data[key];
          }
        }
      }
      return newData;
    },
    sumbitSearchForm(data) {
      let queryJson = data || {};
      this.searchFormData = data;
      // 标签面板查询
      if (this.columnData.tabConfig && this.columnData.tabConfig.on) {
        this.tabQueryJson = {
          [this.columnData.tabConfig.relationField]:
            this.tabList[this.tabActiveKey]?.id,
        };
        queryJson = {
          ...queryJson,
          ...this.tabQueryJson,
        };
      }
      this.listQuery.queryJson =
        JSON.stringify(queryJson) !== "{}" ? JSON.stringify(queryJson) : "";
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    // 处理启用规则
    customEnableRule(data, funcName) {
      // #ifdef MP-WEIXIN
      return true;
      // #endif
      // #ifndef MP-WEIXIN
      let func = this.enableFunc[funcName];
      if (!func) return false;
      let res = func.call(this, {
        row: data,
        rowIndex: data.index,
        onlineUtils: this.xunda.onlineUtils,
      });
      return res;
      // #endif
    },
    handleDeleteBtn() {
      const actionOptions = this.columnData.columnBtnsList.filter(
        (o) => o.value == "remove" && o.show
      );
      this.actionOptions = actionOptions.map((o) => ({
        text: o.label,
        style: {
          backgroundColor: "#dd524d",
        },
      }));
    },
    handleSearchList() {
      this.searchList = this.columnData.searchList || [];
      for (let i = 0; i < this.searchList.length; i++) {
        const item = this.searchList[i];
        const config = item.__config__;
        if (item.value != null && item.value != "" && item.value != []) {
          this.searchFormData[item.id] = item.value;
        }
        if (this.config.webType == 4) config.label = item.label;
      }
      if (Object.keys(this.searchFormData).length)
        this.listQuery.queryJson = JSON.stringify(this.searchFormData);
      if (this.searchList.some((o) => o.isKeyword)) {
        const keywordItem = {
          id: "xundaKeyword",
          fullName: "关键词",
          prop: "xundaKeyword",
          label: "关键词",
          xundaKey: "input",
          clearable: true,
          placeholder: "请输入",
          value: undefined,
          __config__: {
            xundaKey: "input",
          },
        };
        this.searchList.unshift(keywordItem);
      }
      this.searchFormConf = this.$u.deepClone(this.searchList);
    },
    handleSortList() {
      this.sortOptions = [];
      const sortList = this.sortList;
      for (let i = 0; i < sortList.length; i++) {
        let ascItem = {
          label: sortList[i].label + "升序",
          value: sortList[i].prop,
          sidx: sortList[i].prop,
          sort: "asc",
        };
        let descItem = {
          label: sortList[i].label + "降序",
          value: "-" + sortList[i].prop,
          sidx: sortList[i].prop,
          sort: "desc",
        };
        this.sortOptions.push(ascItem, descItem);
      }
    },
    transformColumnList(columnList) {
      let list = [];
      for (let i = 0; i < columnList.length; i++) {
        const e = columnList[i];
        if (!e.prop.includes("-")) {
          e.option = null;
          list.push(e);
        } else {
          let prop = e.prop.split("-")[0];
          let vModel = e.prop.split("-")[1];
          let label = e.label.split("-")[0];
          let childLabel = e.label.replace(label + "-", "");
          let newItem = {
            align: "center",
            xundaKey: "table",
            prop,
            label,
            children: [],
          };
          e.vModel = vModel;
          e.childLabel = childLabel;
          if (!list.some((o) => o.prop === prop)) list.push(newItem);
          for (let i = 0; i < list.length; i++) {
            if (list[i].prop === prop) {
              e.option = null;
              list[i].children.push(e);
              break;
            }
          }
        }
      }
      return list;
    },
    setDefaultQuery() {
      const defaultSortConfig = (this.columnData.defaultSortConfig || []).map(
        (o) => (o.sort === "desc" ? "-" : "") + o.field
      );
      this.listQuery.sidx = defaultSortConfig.join(",");
    },
    upCallback(page) {
      if (this.isPreview == "1") return this.mescroll.endSuccess(0, false);
      if (this.listQuery.queryJson === "{}") this.listQuery.queryJson = "";
      const query = {
        currentPage: page.num,
        pageSize: page.size,
        menuId: this.menuId,
        ...this.listQuery,
      };
      getModelList(this.modelId, query, {
        load: page.num == 1,
      })
        .then((res) => {
          this.showParser = true;
          if (page.num == 1) this.list = [];
          this.mescroll.endSuccess(res.data.list.length);
          const list = res.data.list.map((o, i) => ({
            show: false,
            index: i,
            ...o,
          }));
          this.list = this.list.concat(list);
          this.$nextTick(() => {
            if (this.columnData.funcs && this.columnData.funcs.afterOnload)
              this.setTableLoadFunc();
          });
        })
        .catch((err) => {
          this.mescroll.endByPage(0, 0);
          this.mescroll.endErr();
        });
    },
    setTableLoadFunc() {
      const XUNDATable = this.$refs.tableRef;
      const parameter = {
        data: this.list,
        tableRef: XUNDATable,
        onlineUtils: this.xunda.onlineUtils,
      };
      const func = this.xunda.getScriptFunc.call(
        this,
        this.columnData.funcs.afterOnload
      );
      if (!func) return;
      func.call(this, parameter);
    },
    handleClick(index) {
      const item = this.list[index];
      if (!this.permission.btnPermission.includes("btn_remove"))
        return this.$u.toast("未开启删除权限");
      if (!this.customEnableRule(item, "remove"))
        return this.$u.toast("没有删除权限");
      let txt = "流程处于暂停状态,不可操作";
      if ([1, 2, 3, 4, 6, 7, 8].includes(item.flowState))
        txt = "流程已受理,无法删除";
      uni.showModal({
        title: "提示",
        content: "删除后数据无法恢复",
        success: (res) => {
          if (res.confirm) {
            if (
              this.config.enableFlow == 1 &&
              ![0, 9].includes(item.flowState)
            ) {
              this.$u.toast(txt);
              this.list[index].show = false;
              return;
            }
            let data = {
              flowId: this.config.flowId,
              ids: [item.id],
            };
            deteleModel(data, this.modelId).then((res) => {
              this.$u.toast(res.msg);
              this.list.splice(index, 1);
              this.mescroll.resetUpScroll();
            });
          }
        },
      });
    },
    handleMoreClick(index) {
      this.selectListIndex = index;
      this.showMoreBtn = true;
    },
    selectBtnconfirm(e) {
      var i = this.columnData.customBtnsList.findIndex((item) => {
        return item.value == e[0].value;
      });
      const item = this.columnData.customBtnsList[i];
      const row = this.list[this.selectListIndex];
      const index = this.selectListIndex;
      // 自定义启用规则判断
      if (!this.customEnableRule(row, item.value))
        return this.$u.toast("没有" + item.label + "权限");
      if (item.event.btnType == 1) this.handlePopup(item.event, row, index);
      if (item.event.btnType == 2)
        this.handleScriptFunc(item.event, row, index);
      if (item.event.btnType == 3) this.handleInterface(item.event, row, index);
    },
    handlePopup(item, row, index) {
      this.handleListen();
      let data = {
        config: item,
        modelId: this.modelId,
        id: this.config.webType == 4 ? "" : row.id,
        isPreview: this.isPreview,
        row: this.config.webType == 4 ? row : "",
      };
      data = encodeURIComponent(JSON.stringify(data));
      uni.navigateTo({
        url: "/pages/apply/customBtn/index?data=" + data,
      });
    },
    handleScriptFunc(item, row, index) {
      const parameter = {
        data: row,
        index,
        refresh: this.initData,
        onlineUtils: this.xunda.onlineUtils,
      };
      const func = this.xunda.getScriptFunc.call(this, item.func);
      if (!func) return;
      func.call(this, parameter);
    },
    handleInterface(item, row, index) {
      const handlerData = () => {
        getModelInfo(this.modelId, row.id).then((res) => {
          const dataForm = res.data || {};
          if (!dataForm.data) return;
          const data = {
            ...JSON.parse(dataForm.data),
            id: row.id,
          };
          handlerInterface(data);
        });
      };
      const handlerInterface = (data) => {
        let query = {
          paramList: this.xunda.getParamList(item.templateJson, data) || [],
        };
        getDataInterfaceRes(item.interfaceId, query).then((res) => {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
          if (item.isRefresh) this.initData();
        });
      };
      const handleFun = () => {
        this.config.webType == "4" ? handlerInterface(row) : handlerData();
      };
      if (!item.useConfirm) return handleFun();
      uni.showModal({
        title: "提示",
        content: item.confirmTitle || "确认执行此操作",
        success: (res) => {
          if (!res.cancel) handleFun();
        },
      });
    },
    initData() {
      this.list = [];
      this.$nextTick(() => {
        this.mescroll.resetUpScroll();
      });
    },
    open(index) {
      this.list[index].show = true;
      this.list.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    search() {
      if (this.isPreview == "1") return;
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      }, 300);
    },
    handleListen() {
      uni.$off("refresh");
      uni.$on("refresh", () => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    addPage() {
      this.handleListen();
      this.jumPage({}, "");
    },
    jumPage(item, btnType) {
      if (!item.id && !item.flowState) btnType = "btn_add";
      if (this.config.enableFlow == 1) {
        if (item.id) {
          if (
            !this.permission.btnPermission.includes("btn_edit") &&
            item.flowState == 3
          )
            return;
          if (
            !this.permission.btnPermission.includes("btn_detail") &&
            ![0, 8, 9].includes(item.flowState)
          )
            return;
        }
        let opType = "-1";
        if (![0, 8, 9].includes(item.flowState) && btnType != "btn_add")
          opType = 0;
        const config = {
          id: item.flowTaskId || item.id || "",
          flowId: this.config.flowId,
          opType,
          status: item.flowState || "",
          isPreview: this.isPreview,
          taskId: item.flowTaskId || item.id,
          isFlow: 0,
        };
        uni.navigateTo({
          url:
            "/pages/workFlow/flowBefore/index?config=" +
            this.xunda.base64.encode(JSON.stringify(config)),
        });
      } else {
        const type = btnType == "btn_detail" ? "detail" : "form";
        const currentMenu = encodeURIComponent(
          JSON.stringify(this.permission.formPermission)
        );
        let btnType_ = this.permission.btnPermission.includes("btn_edit")
          ? "btn_edit"
          : "btn_add";
        let enableEdit = this.customEnableRule(item, "edit");
        let labelS = {};
        for (let i = 0; i < this.columnData.columnBtnsList.length; i++) {
          const item = this.columnData.columnBtnsList[i];
          if (item.value == "edit") labelS[btnType_] = item.label;
        }
        const config = {
          currentMenu,
          btnType: btnType_,
          list: this.list,
          modelId: this.modelId,
          isPreview: this.isPreview,
          id: item.id || "",
          index: item.index,
          enableEdit,
          labelS,
        };
        const url =
          "/pages/apply/dynamicModel/" +
          type +
          "?config=" +
          this.xunda.base64.encode(JSON.stringify(config));
        uni.navigateTo({
          url: url,
        });
      }
    },

    goDetail(item) {
      if (this.config.webType == 4) return;
      this.handleListen();
      let hasDetail = this.permission.btnPermission.includes("btn_detail");
      let hasEdit = this.permission.btnPermission.includes("btn_edit");
      if (!hasDetail && !hasEdit) return;
      if (hasDetail) {
        if (this.customEnableRule(item, "detail")) {
          return this.jumPage(item, "btn_detail");
        }
        if (this.customEnableRule(item, "edit")) {
          return this.jumPage(item, "btn_edit");
        }
      } else {
        if (this.customEnableRule(item, "edit")) {
          return this.jumPage(item, "btn_edit");
        }
      }
    },
    cellClick(item) {
      if (this.isPreview == "1") return this.$u.toast("功能预览不支持排序");
      const findIndex = this.sortValue.findIndex((o) => o === item.value);
      if (findIndex < 0) {
        const findLikeIndex = this.sortValue.findIndex(
          (o) => o.indexOf(item.sidx) > -1
        );
        if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
        this.sortValue.push(item.value);
      } else {
        this.sortValue.splice(findIndex, 1);
      }
    },
    handleReset() {
      this.searchFormData = {};
      const list = [
        "datePicker",
        "timePicker",
        "inputNumber",
        "calculate",
        "cascader",
        "organizeSelect",
      ];
      for (let i = 0; i < this.searchList.length; i++) {
        const item = this.searchList[i];
        const config = item.__config__;
        let defaultValue =
          item.searchMultiple || list.includes(config.xundaKey)
            ? []
            : undefined;
        if (config.isFromParam) defaultValue = undefined;
        config.defaultValue = defaultValue;
        this.searchFormData[item.id] = item.value || defaultValue;
      }
      this.searchFormConf = JSON.parse(JSON.stringify(this.searchList));
    },
    handleSearch() {
      if (this.isPreview == "1") return this.$u.toast("功能预览不支持检索");
      this.$refs.searchForm && this.$refs.searchForm.submitForm();
    },
    toThousands(val, column) {
      if (val) {
        let valList = val.toString().split(".");
        let num = Number(valList[0]);
        let newVal = column.thousands ? num.toLocaleString() : num;
        return valList[1] ? newVal + "." + valList[1] : newVal;
      } else {
        return val;
      }
    },
    relationFormClick(item, column) {
      let vModel = column.vModel ? column.vModel : column.__vModel__;
      let model_id = column.modelId;
      let config = {
        modelId: model_id,
        isPreview: true,
        id: item[vModel + "_id"],
        isRelationForm: 1,
      };
      const url =
        "/pages/apply/dynamicModel/detail?config=" +
        this.xunda.base64.encode(JSON.stringify(config));
      uni.navigateTo({
        url: url,
      });
    },
    handleSortReset() {
      this.sortValue = [];
    },
    handleSortSearch() {
      if (this.sortValue.length) {
        this.listQuery.sidx = this.sortValue.join(",");
      } else {
        this.setDefaultQuery();
      }
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
  height: 100%;
  /* #ifdef MP-ALIPAY */
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  /* #endif */
}

:deep(.u-cell) {
  padding: 0rpx;
  height: 112rpx;
}

.buttom-actions {
  z-index: 1;
}

.screen-box {
  background-color: #fff;
  height: 100%;

  .screen-btn {
    width: 100%;
    height: 2.75rem;

    .btn {
      width: 50%;
      height: 2.75rem;
      text-align: center;
      line-height: 2.75rem;
      box-shadow: 0px -4rpx 20rpx #f8f8f8;
    }

    .btn1 {
      color: #606266;
    }

    .btn2 {
      background-color: #2979ff;
      color: #fff;
    }
  }

  .screen-list {
    width: 100%;
    height: 100%;

    .list {
      height: calc(100% - 88rpx);
      overflow-y: scroll;
    }
  }
}

.item {
  padding: 0 !important;
}

.notData-box {
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  padding-bottom: 200rpx;

  .notData-inner {
    width: 280rpx;
    height: 308rpx;
    align-items: center;

    .iconImg {
      width: 100%;
      height: 100%;
    }

    .notData-inner-text {
      padding: 30rpx 0;
      color: #909399;
    }
  }
}

.right-option-box {
  display: flex;
  width: max-content;

  .right-option {
    width: 144rpx;
    height: 100%;
    font-size: 16px;
    color: #fff;
    background-color: #dd524d;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .more-option {
    background-color: #1890ff;
  }
}
</style>
