{"id": "vk-uview-ui", "name": "vk-uview-ui", "displayName": "【开箱即用】uView Vue3 横空出世，继承uView1意志，再战江湖，风云再起！", "version": "1.6.4", "description": "同时支持 Vue3.0 和 Vue2.0，你没看错，现在 uView 支持 Vue3.0 了！（不支持nvue，此版本为uView1.0的分支）", "keywords": ["vk-uview-ui", "vk云开发", "vue3.0", "鸿蒙", "uview"], "repository": "https://gitee.com/vk-uni/vk-uview-ui.git", "engines": {"HBuilderX": "^3.1.0", "uni-app": "^4.36", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://vkuviewdoc.fsq.pub", "type": "component-vue", "darkmode": "x", "i18n": "√", "widescreen": "x"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "x", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "-", "jd": "-", "harmony": "√", "qq": "√", "lark": "-"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}