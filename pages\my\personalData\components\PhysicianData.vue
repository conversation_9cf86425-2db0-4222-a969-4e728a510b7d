<template>
  <view class="xunda-wrap personalData">
    <view style="background-color: #fff" class="u-p-l-20 u-p-r-20">
      <u-form
        :model="dataForm"
        :errorType="['toast']"
        label-position="left"
        label-width="150"
        label-align="right"
        ref="dataForm"
      >
        <u-form-item label="姓名" prop="realName" required>
          <u-input
            input-align="right"
            v-model="dataForm.realName"
            placeholder="请输入"
          ></u-input>
        </u-form-item>
        <u-form-item label="性别">
          <XundaSelect
            v-model="dataForm.gender"
            placeholder="请选择"
            :options="genderOptions"
            :props="props"
          />
        </u-form-item>
        <u-form-item label="出生日期">
          <XundaDatePicker v-model="dataForm.birthday" placeholder="请选择" />
        </u-form-item>
        <u-form-item label="身份证号">
          <u-input
            input-align="right"
            v-model="dataForm.certificatesNumber"
            placeholder="请输入"
          >
          </u-input>
        </u-form-item>
        <u-form-item label="联系电话">
          <u-input
            input-align="right"
            v-model="dataForm.mobilePhone"
            placeholder="请输入"
          >
          </u-input>
        </u-form-item>
        <u-form-item label="科室">
          <u-input
            input-align="right"
            v-model="dataForm.department"
            placeholder="请输入"
          >
          </u-input>
        </u-form-item>
        <u-form-item label="职称">
          <u-input
            input-align="right"
            v-model="dataForm.title"
            placeholder="请输入"
          >
          </u-input>
        </u-form-item>
        <u-form-item label="执业医师证书编号">
          <u-input
            input-align="right"
            v-model="dataForm.physicianCertificateNumber"
            placeholder="请输入"
          >
          </u-input>
        </u-form-item>
        <u-form-item label="专业领域">
          <u-input
            input-align="right"
            v-model="dataForm.specialty"
            placeholder="请输入"
            type="textarea"
          />
        </u-form-item>
        <u-form-item label="个人简介">
          <u-input
            input-align="right"
            v-model="dataForm.biography"
            placeholder="请输入"
            type="textarea"
          />
        </u-form-item>
      </u-form>
    </view>
    <view class="flowBefore-actions">
      <u-button class="buttom-btn" type="primary" @click="submit"
        >保存</u-button
      >
    </view>
  </view>
</template>

<script>
import { UpdateUser } from "@/api/common";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
export default {
  props: {
    personalData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    const data = {
      show: false,
      props: {
        label: "fullName",
        value: "enCode",
      },
      dataForm: {
        birthday: null,
        certificatesNumber: "",
        gender: "",
        mobilePhone: "",
        realName: "",
        department: "",
        title: "",
        physicianCertificateNumber: "",
        specialty: "",
        biography: "",
        id: null,
      },
      genderOptions: [],
      rules: {
        realName: [
          {
            required: true,
            message: "请输入姓名",
            trigger: ["change", "blur"],
          },
        ],
      },
    };
    return data;
  },
  computed: {
    baseURL() {
      return this.define.baseURL;
    },
  },
  watch: {
    personalData: {
      handler(val) {
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.$refs.dataForm.setRules(this.rules);
  },
  methods: {
    init() {
      let initData = JSON.parse(JSON.stringify(this.personalData));
      for (let key in initData) {
        for (let k in this.dataForm) {
          if (key === k) {
            this.dataForm[key] = initData[key];
          }
        }
      }
      this.getOptions();
    },
    getOptions() {
      baseStore
        .getDictionaryData({
          sort: "sex",
        })
        .then((res) => {
          this.genderOptions = JSON.parse(JSON.stringify(res));
        });
      this.show = true;
    },
    submit() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          UpdateUser(this.dataForm).then((res) => {
            uni.showToast({
              title: "保存成功",
              duration: 800,
              icon: "none",
            });
            setTimeout(() => {
              uni.navigateBack();
            }, 1000);
          });
        }
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
}

.slot-btn {
  width: 329rpx;
  height: 140rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgb(244, 245, 246);
  border-radius: 10rpx;
}

.slot-btn__hover {
  background-color: rgb(235, 236, 238);
}
</style>