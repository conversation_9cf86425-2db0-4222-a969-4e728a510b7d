<template>
  <view :class="'xunda-button xunda-button-' + align">
    <u-button
      :custom-style="customStyle"
      :type="realType"
      :disabled="disabled"
      @click="onClick"
      >{{ buttonText }}</u-button
    >
  </view>
</template>
<script>
export default {
  name: "xunda-button",
  props: {
    align: {
      default: "left",
    },
    buttonText: {
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    type: {
      default: "",
    },
  },
  computed: {
    realType() {
      return !this.type
        ? "default"
        : this.type === "danger"
        ? "error"
        : this.type;
    },
  },
  data() {
    return {
      customStyle: {
        display: "inline-block",
      },
    };
  },
  methods: {
    onClick(event) {
      this.$emit("click", event);
    },
  },
};
</script>
<style lang="scss" scoped>
.xunda-button {
  width: 100%;

  &.xunda-button-left {
    text-align: left;
  }

  &.xunda-button-center {
    text-align: center;
  }

  &.xunda-button-right {
    text-align: right;
  }
}
</style>
