<template>
    <view class="container">
        <u-form-item label="学术任职" prop="academicPostion">
            <!-- 展开或者收缩 -->
        </u-form-item>
        <view v-if="items && items.length > 0">
            <view class="item" v-for="(item, index) in items" :key="index">
                <view class="row">
                    <view class="left">
                        {{ titleName(index) }}
                    </view>
                    <view class="closeBox u-flex-col" @click.stop="handleDelete(index)" v-if="isEdit">
                        <text class="icon-ym icon-ym-nav-close closeTxt u-flex"></text>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        任职类别
                    </view>
                    <view class="right">
                        <XundaInput v-if="isEdit" v-model="item.jobCategory" :fieldKey="'jobCategory'"
                            input-align='right' clearable placeholder="请输入">
                        </XundaInput>
                        <view v-else class="xunda-detail-text">
                            {{ item.jobCategory }}
                        </view>
                    </view>
                </view>

                <view class="row">
                    <view class="left">
                        学会名称
                    </view>
                    <view class="right">
                        <XundaInput v-if="isEdit" v-model="item.societyName" :fieldKey="'societyName'"
                            input-align='right' clearable placeholder="请输入">
                        </XundaInput>
                        <view v-else class="xunda-detail-text">
                            {{ item.societyName }}
                        </view>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        类别
                    </view>
                    <view class="right">
                        <XundaInput v-if="isEdit" v-model="item.category" :fieldKey="'category'" input-align='right'
                            clearable placeholder="请输入">
                        </XundaInput>
                        <view v-else class="xunda-detail-text">
                            {{ item.category }}
                        </view>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        届数
                    </view>
                    <view class="right">
                        <XundaInput v-if="isEdit" v-model="item.session" :fieldKey="'session'" input-align='right'
                            clearable placeholder="请输入">
                        </XundaInput>
                        <view v-else class="xunda-detail-text">
                            {{ item.session }}
                        </view>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        职务
                    </view>
                    <view class="right">
                        <XundaInput v-if="isEdit" v-model="item.duty" :fieldKey="'duty'" input-align='right' clearable
                            placeholder="请输入">
                        </XundaInput>
                        <view v-else class="xunda-detail-text">
                            {{ item.duty }}
                        </view>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        开始时间
                    </view>
                    <view class="right">
                        <XundaDatePicker v-if="isEdit" v-model="item.startTime" :fieldKey="'startTime'"
                            format="yyyy-MM-dd" clearable placeholder="请选择" type="date">
                        </XundaDatePicker>
                        <view v-else class="xunda-detail-text">
                            {{ xunda.toDate(item.startTime, "yyyy-MM-dd") }}
                        </view>
                    </view>
                </view>
                <view class="row">
                    <view class="left">
                        结束时间
                    </view>
                    <view class="right">
                        <XundaDatePicker v-if="isEdit" v-model="item.endTime" :fieldKey="'endTime'" format="yyyy-MM-dd"
                            clearable placeholder="请选择" type="date">
                        </XundaDatePicker>
                        <view v-else class="xunda-detail-text">
                            {{ xunda.toDate(item.endTime, "yyyy-MM-dd") }}
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view v-else>
            <view v-if="!isEdit" class="empty">
                暂无学术任职
            </view>
        </view>
        <view v-if="isEdit">
            <u-button class="buttom-btn" type="primary" @click.stop="handelAdd()">添加学术任职</u-button>
        </view>
    </view>
</template>
<script>
import { buildUUID } from '@/utils/uuid';
export default {
    name: 'AcademicPositionItem',
    props: {
        items: {
            type: Array,
            default: () => {
                return []
            }
        }, isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            data: {},
        }
    },
    mounted() {
        this.data = this.items;
    },
    methods: {
        titleName(index) {
            return `学术任职：${this.xunda.getNumberChhinese(index + 1)}`
        },
        click() {
            this.$emit('click', this.data)
        }, handelSelect(item) {
            console.log(item.id)
        },
        handelAdd() {
            let items = this.items ? this.items : []
            items.push({
                id: buildUUID(),
            })
            this.$emit('update:items', items)
        },
        handleDelete(index) {
            uni.showModal({
                title: '提示',
                content: '确定要删除吗？',
                success: (res) => {
                    if (res.confirm) {
                        let items = this.items ? this.items : []
                        items.splice(index, 1)
                        this.$emit('update:items', items)
                    }
                }
            })
        },
    }
}
</script>
<style lang="scss" scoped>
.container {
    margin: 0;
}

.item {
    margin: 0;
    padding: 0;
    border: 1px solid #d8d8d8;
}

.empty {
    text-align: center;
    padding: 20px;
    font-size: 16px;
    color: #999;
}

.buttom-btn {
    margin: 10px;
}

.row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0px;
    /* 根据需要调整 */
}

.left,
.right {
    width: 48%;
    border: 10px solid #ffffff;
    /* 根据实际情况调整宽度 */
}

.left {
    align-self: center;
    text-align: left;
}

.right {
    text-align: right;
}

.arrow {
    margin-left: 5px;
    font-weight: bold;
}


.closeBox {
    height: 60rpx;
    align-items: flex-end;
    justify-content: space-evenly;
    flex: 0.2;

    .closeTxt {
        width: 36rpx;
        height: 36rpx;
        border-radius: 50%;
        background-color: #fa3534;
        color: #FFFFFF;
        font-size: 20rpx;
        align-items: center;
        justify-content: center;
        line-height: 36rpx;
    }
}
</style>