<template>
  <u-radio-group
    v-model="innerValue"
    :disabled="disabled"
    :wrap="direction == 'horizontal' ? false : true"
    @change="onChange"
    iconPlacement="right"
  >
    <u-radio
      v-for="(item, index) in options"
      :key="index"
      :name="item[props.value]"
      :custom-style="{
        marginBottom: '16rpx',
        padding: '20rpx',
        backgroundColor: '#fff',
        borderRadius: '12rpx',
        border: '2rpx solid #e0e0e0',
      }"
      icon-size="36"
      label-size="26"
      :shape="readonly ? 'circle' : 'square'"
    >
      {{ item[props.label] }}
    </u-radio>
  </u-radio-group>
</template>
<script>
export default {
  name: "xundaRadio",
  props: {
    modelValue: {
      type: [String, Number, Boolean],
    },
    direction: {
      type: String,
      default: "horizontal",
    },
    options: {
      type: Array,
      default: () => [],
    },
    props: {
      type: Object,
      default: () => ({
        label: "fullName",
        value: "id",
      }),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      innerValue: "",
    };
  },
  watch: {
    modelValue: {
      handler(val) {
        this.innerValue = val;
      },
      immediate: true,
    },
  },
  methods: {
    onChange(value, e) {
      const selectData =
        this.options.filter((o) => value == o[this.props.value]) || [];
      this.$emit("update:modelValue", value);
      this.$emit("change", value, selectData[0]);
    },
  },
};
</script>
