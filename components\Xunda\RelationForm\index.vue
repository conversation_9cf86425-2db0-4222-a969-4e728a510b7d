<template>
  <view class="xunda-relation-form">
    <u-input
      :class="{ 'link-style': disabled && innerValue }"
      input-align="right"
      type="select"
      v-model="innerValue"
      @click="openSelect"
      :placeholder="placeholder"
    ></u-input>
  </view>
</template>

<script>
import { getDataChange } from "@/api/common.js";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
export default {
  name: "xunda-relation-form",
  props: {
    modelValue: {
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    columnOptions: {
      type: Array,
      default: [],
    },
    relationField: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "relation",
    },
    propsValue: {
      type: String,
      default: "",
    },
    modelId: {
      type: String,
      default: "",
    },
    hasPage: {
      type: <PERSON>olean,
      default: false,
    },
    pageSize: {
      type: Number,
      default: 10000,
    },
    vModel: {
      type: String,
      default: "",
    },
    popupTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      selectShow: false,
      innerValue: "",
      defaultValue: "",
      current: null,
      defaultOptions: [],
      firstVal: "",
      firstId: 0,
    };
  },
  watch: {
    modelValue(val) {
      this.setDefault();
    },
  },
  created() {
    uni.$on("confirm1", (subVal, innerValue, list, selectData) => {
      this.confirm(subVal, innerValue, list, selectData);
    });
    this.setDefault();
  },
  methods: {
    setDefault() {
      if (this.modelValue) {
        if (!this.modelId) return;
        getDataChange(this.modelId, this.modelValue).then((res) => {
          if (!res.data || !res.data.data) return;
          let data = JSON.parse(res.data.data);
          this.innerValue = data[this.relationField]
            ? data[this.relationField]
            : "";
          if (!this.vModel) return;
          let relationData = baseStore.relationData;
          relationData[this.vModel] = data;
          baseStore.updateRelationData(relationData);
        });
      } else {
        this.innerValue = "";
        if (!this.vModel) return;
        let relationData = baseStore.relationData;
        relationData[this.vModel] = {};
        baseStore.updateRelationData(relationData);
      }
    },
    openSelect() {
      if (this.disabled) {
        if (!this.modelValue) return;
        let config = {
          modelId: this.modelId,
          id: this.modelValue,
          formTitle: "详情",
          noShowBtn: 1,
        };
        this.$nextTick(() => {
          const url =
            "/pages/apply/dynamicModel/detail?config=" +
            this.xunda.base64.encode(JSON.stringify(config));
          uni.navigateTo({
            url: url,
          });
        });
        return;
      }
      let data = {
        columnOptions: this.columnOptions,
        relationField: this.relationField,
        type: this.type,
        propsValue: this.propsValue,
        modelId: this.modelId,
        hasPage: this.hasPage,
        pageSize: this.pageSize,
        id: this.modelValue,
        vModel: this.vModel,
        popupTitle: this.popupTitle || "选择数据",
        innerValue: this.innerValue,
      };
      uni.navigateTo({
        url:
          "/pages/apply/popSelect/index?data=" +
          encodeURIComponent(JSON.stringify(data)),
      });
    },
    confirm(subVal, innerValue, vModel, selectData) {
      if (vModel === this.vModel) {
        this.firstVal = innerValue;
        this.firstId = subVal;
        this.innerValue = innerValue + "";
        this.$emit("update:modelValue", subVal);
        this.$emit("change", subVal, selectData);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.xunda-relation-form {
  width: 100%;
  height: 100%;
}
</style>
