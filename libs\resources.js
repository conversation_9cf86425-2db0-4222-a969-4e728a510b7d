const resources = {
	startup: {
		main: "https://app.cdn.gzbtrj.com/image/3.2/<EMAIL>",
	},
	login: {
		banner: "https://app.cdn.gzbtrj.com/image/3.0/login/top.png",
		logo: "https://app.cdn.gzbtrj.com/image/banner/logo.png",
	},
	banner: {
		home1Url: "https://app.cdn.gzbtrj.com/image/3.2/banner1.png",
		home2Url: "https://app.cdn.gzbtrj.com/image/3.2/banner2.png",
		home3Url: "https://app.cdn.gzbtrj.com/image/3.2/banner4.png",
		contactus: "https://app.cdn.gzbtrj.com/image/banner/contactus.png",
		loginlogo: "https://app.cdn.gzbtrj.com/image/banner/logo.png",
		accountSecurity: "https://app.cdn.gzbtrj.com/image/banner/accountSecurity.png"
	},
	message: {
		nocontent: "https://app.cdn.gzbtrj.com/image/message/nocontent.png",
		nodata: "https://app.cdn.gzbtrj.com/image/message/nodata.png",
	},
	//引导页
	guide: {
		guide1: "https://app.cdn.gzbtrj.com/image/v5.0/guide1.png",
		guide2: "https://app.cdn.gzbtrj.com/image/v5.0/guide2.png",
		guide3: "https://app.cdn.gzbtrj.com/image/v5.0/guide3.png",
	},
	extend: {
		againlocation: "https://app.cdn.gzbtrj.com/image/extend/againlocation.png",
		file: "https://app.cdn.gzbtrj.com/image/extend/file.png",
		location: "https://app.cdn.gzbtrj.com/image/extend/location.png",
		location3: "https://app.cdn.gzbtrj.com/image/extend/location%403x.png",
		pause: "https://app.cdn.gzbtrj.com/image/extend/pause.png",
		play: "https://app.cdn.gzbtrj.com/image/extend/play.png",
		pluse: "https://app.cdn.gzbtrj.com/image/extend/plus.png",
		record: "https://app.cdn.gzbtrj.com/image/extend/record.png",
		stop: "https://app.cdn.gzbtrj.com/image/extend/stop.png",
		trash: "https://app.cdn.gzbtrj.com/image/extend/trash.png"
	},
	common: {
		currentIcon: "https://app.cdn.gzbtrj.com/image/common/currentIcon.png",
		defaultIcon: "https://app.cdn.gzbtrj.com/image/common/defaultIcon.png",
		agreement: "https://app.cdn.gzbtrj.com/image/common/agreement.png",
		wechat_qrcode: "https://app.cdn.gzbtrj.com/image/common/wechat_qrcode.jpg",
		new_edition: "https://app.cdn.gzbtrj.com/image/common/new_edition.png"
	},
	jnapp: "https://app.cdn.gzbtrj.com/jnapp/20200415.apk",
	userAgreement: "https://m.gzbit.com/userAgreement.html", //用户协议
	privacyPolicy: "https://m.gzbit.com/privacyPolicy.html", //隐私政策
	appStore: "https://apps.apple.com/cn/app/jnapp/id1490797314",
	status: {
		//前加签
		frontSign: "https://app.cdn.gzbtrj.com/image/flow/frontSign.png",
		//加签
		addSign: "https://app.cdn.gzbtrj.com/image/flow/addSign.png",
		//指派
		assign: "https://app.cdn.gzbtrj.com/image/flow/assign.png",
		//拒绝
		refuse: "https://app.cdn.gzbtrj.com/image/flow/refuse.png",
		//等待提交
		submit: "https://app.cdn.gzbtrj.com/image/flow/submit.png",
		//等待审核
		review: "https://app.cdn.gzbtrj.com/image/flow/review.png",
		//已通过
		adopt: "https://app.cdn.gzbtrj.com/image/flow/adopt.png",
		//审核通过
		reviewAdopt: "https://app.cdn.gzbtrj.com/image/flow/reviewAdopt.png",
		//审核退回
		reviewRefuse: "https://app.cdn.gzbtrj.com/image/flow/reviewRefuse.png",
		//流程撤回
		reviewUndo: "https://app.cdn.gzbtrj.com/image/flow/reviewUndo.png",
		//审核终止
		reviewStop: "https://app.cdn.gzbtrj.com/image/flow/reviewStop.png",
		//委托中
		entrusting: "https://app.cdn.gzbtrj.com/image/flow/entrusting.png",
		//已失效
		expired: "https://app.cdn.gzbtrj.com/image/flow/expired.png",
		//未开始
		notStarted: "https://app.cdn.gzbtrj.com/image/flow/notStarted.png",
		//已被挂起
		suspend: "https://app.cdn.gzbtrj.com/image/flow/suspend.png",
		//转向
		trun: "https://app.cdn.gzbtrj.com/image/flow/trun.png",
		//已退回
		back: "https://app.cdn.gzbtrj.com/image/flow/back.png",
		//已终止
		cancel: "https://app.cdn.gzbtrj.com/image/flow/cancel.png",
		//进行中
		doing: "https://app.cdn.gzbtrj.com/image/flow/doing.png",
		//待提交
		draft: "https://app.cdn.gzbtrj.com/image/flow/draft.png",
		//已暂停
		pause: "https://app.cdn.gzbtrj.com/image/flow/pause.png",
		//已撤回
		recall: "https://app.cdn.gzbtrj.com/image/flow/recall.png",
		//已拒绝
		reject: "https://app.cdn.gzbtrj.com/image/flow/reject.png",
		//已撤销
		revoke: "https://app.cdn.gzbtrj.com/image/flow/revoke.png",
		//撤销中
		revoking: "https://app.cdn.gzbtrj.com/image/flow/revoking.png",
		//待签收
		signfor: "https://app.cdn.gzbtrj.com/image/flow/signfor.png",
		//流转中
		circulation: "https://app.cdn.gzbtrj.com/image/flow/circulation.png",
		//退回
		return: "https://app.cdn.gzbtrj.com/image/flow/return.png",
		//加签
		addSign: "https://app.cdn.gzbtrj.com/image/flow/addSign.png",
		//同意
		agree: "https://app.cdn.gzbtrj.com/image/flow/agree.png",
		//转审
		transfer: "https://app.cdn.gzbtrj.com/image/flow/transfer.png",
		//协办
		assist: "https://app.cdn.gzbtrj.com/image/flow/assist.png"
	},
	document: {
		wordImg: 'https://app.cdn.gzbtrj.com/image/document/word.png',
		excelImg: 'https://app.cdn.gzbtrj.com/image/document/excel.png',
		pptImg: 'https://app.cdn.gzbtrj.com/image/document/ppt.png',
		pdfImg: 'https://app.cdn.gzbtrj.com/image/document/pdf.png',
		rarImg: 'https://app.cdn.gzbtrj.com/image/document/rar.png',
		txtImg: 'https://app.cdn.gzbtrj.com/image/document/txt.png',
		codeImg: 'https://app.cdn.gzbtrj.com/image/document/code.png',
		imageImg: 'https://app.cdn.gzbtrj.com/image/document/image.png',
		audioImg: 'https://app.cdn.gzbtrj.com/image/document/audio.png',
		blankImg: 'https://app.cdn.gzbtrj.com/image/document/blank.png',
		folderImg: 'https://app.cdn.gzbtrj.com/image/document/folder.png',
	}
}

export default resources