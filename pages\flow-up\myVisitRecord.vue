<template>
    <view class="list-view">
        <view class="search-box_sticky">
            <view class="search-box">
                <u-search placeholder="请输入关键词搜索" v-model="listQuery.keyword" height="72" :show-action="false"
                    @change="search" bg-color="#f0f2f6" shape="square">
                </u-search>
            </view>
            <view class="month-picker">
                <scroll-view scroll-x="true" class="month-scroll" :scroll-left="scrollLeft">
                    <view v-for="item in monthList" :key="item.value"
                        :class="['month-item', { active: selectedMonth === item.value }]"
                        @click="selectMonth(item.value)">
                        {{ item.label }}
                    </view>
                </scroll-view>
            </view>
        </view>

        <view class="visit-list-section">
            <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
                :fixed="false">
                <view class="date-group" v-for="(group, date) in groupedList" :key="date">
                    <view class="date-header">
                        <text class="date-text">{{ formatDate(date) }}</text>
                        <text class="count-badge">{{ group.length }}条记录</text>
                    </view>

                    <view class="visit-card" v-for="(item, index) in group" :key="index" @click="goDetail(item)">
                        <view class="visit-header">
                            <view class="patient-info">
                                <text class="name">{{ item.patientName }}</text>
                                <text class="id">{{ item.patientAdmissionNo }}</text>
                            </view>
                            <view class="doctor-info">
                                <text class="label">随访医师:</text>
                                <text class="name">{{ item.physicianName }}</text>
                            </view>
                        </view>

                        <view class="visit-time">
                            <u-icon name="clock" size="28" color="#94a3b8"></u-icon>
                            <text>{{ formatTime(item.visiteDate) }}</text>
                        </view>
                    </view>
                </view>

                <view class="empty-state" v-if="list.length === 0">
                    <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png"></u-empty>
                </view>
            </mescroll-uni>
        </view>
    </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getAppList } from "@/api/flow-up/visitRecord";

export default {
    mixins: [MescrollMixin],
    components: {},
    data() {
        return {
            selectedMonth: "",
            scrollLeft: 0,
            monthList: [],
            groupedList: {},
            isAuthority: true,
            icon: resources.message.nodata,
            sortValue: [],
            searchForm: {
                name: undefined,
            },
            downOption: {
                use: true,
                auto: false,
            },
            dataOptions: {},
            upOption: {
                page: {
                    num: 0,
                    size: 20,
                    time: null,
                },
                empty: {
                    use: true,
                    icon: resources.message.nodata,
                    tip: "暂无数据",
                    fixed: true,
                    zIndex: 5,
                },
                textNoMore: "没有更多数据",
                toTop: {
                    bottom: 250,
                },
            },
            list: [],
            appColumnList: [
                {
                    prop: "patientAdmissionNo",
                    label: "住院号",
                },
                {
                    prop: "patientName",
                    label: "随访患者",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "physicianName",
                    label: "随访医师",
                    width: 100,
                    align: "center",
                    sort: true,
                },
                {
                    prop: "visiteDate",
                    label: "随访日期",
                    width: 100,
                    align: "center",
                    sort: true,
                },
            ],
            listQuery: {
                moduleId: "661876504619124229",
                sidx: "",
                keyword: "",
                json: "",
            },
            options: [],
            sortOptions: [],
            ableAll: {},
            interfaceRes: {
                name: [],
            },
            menuId: "",
            columnList: [],
            key: new Date(),
            dataValue: {},
            userInfo: {},
            firstInitSearchData: false,
            tabList: [],
            tabKey: 0,
            optionsObj: {
                defaultProps: {
                    label: "fullName",
                    value: "enCode",
                    multiple: false,
                    children: "",
                },
            },
            type: "",
        };
    },
    computed: {},
    onLoad(e) {
        this.type = e.type;
        this.userInfo = uni.getStorageSync("userInfo") || {};
        this.menuId = e.menuId;
        this.setDefaultQuery();
        this.generateMonthList();
        this.dataAll();
        this.getColumnList();
    },
    onShow() {
        this.$nextTick(() => {
            this.mescroll.resetUpScroll();
        });
    },
    onUnload() {
        uni.$off("refresh");
    },
    methods: {
        generateMonthList() {
            const now = new Date();
            const currentYear = now.getFullYear();
            const currentMonth = now.getMonth();

            // 生成最近12个月的列表
            this.monthList = Array.from({ length: 12 }, (_, i) => {
                const monthIndex = currentMonth - i;
                const year = currentYear + Math.floor(monthIndex / 12);
                const month = ((monthIndex % 12) + 12) % 12;
                const value = `${year}-${(month + 1).toString().padStart(2, "0")}`;
                const label = `${year}年${month + 1}月`;
                return { value, label };
            });

            // 反转列表，使日期从小到大排序
            // this.monthList = this.monthList.reverse();

            // 默认选择当前月份
            this.selectedMonth = this.monthList[0].value;

            // 延迟执行滚动，确保DOM已经渲染
            setTimeout(() => {
                this.scrollToMonth(this.selectedMonth);
            }, 100);
        },

        selectMonth(month) {
            if (this.selectedMonth === month) {
                this.selectedMonth = ""; // 再次点击取消选择
            } else {
                this.selectedMonth = month;
            }
            this.search();
        },

        onMonthInput(value) {
            // 验证输入的月份格式是否正确（YYYY-MM）
            const monthPattern = /^\d{4}-(?:0[1-9]|1[0-2])$/;
            if (monthPattern.test(value)) {
                this.selectedMonth = value;
                this.search();
            }
        },

        scrollToMonth(month) {
            // 获取选中月份的索引
            const index = this.monthList.findIndex((item) => item.value === month);
            if (index > -1) {
                // 计算需要滚动的距离
                const itemWidth = 140; // 每个月份项的宽度（包含间距）
                const scrollPosition = index * itemWidth;

                // 设置滚动位置
                this.$nextTick(() => {
                    this.scrollLeft = scrollPosition;
                });
            }
        },

        formatDate(dateStr) {
            const date = new Date(dateStr);
            return `${date.getMonth() + 1}月${date.getDate()}日`;
        },

        formatTime(dateStr) {
            const date = new Date(dateStr);
            const hours = date.getHours().toString().padStart(2, "0");
            const minutes = date.getMinutes().toString().padStart(2, "0");
            return `${hours}:${minutes}`;
        },

        groupListByDate(list) {
            const grouped = {};
            list.forEach((item) => {
                const date = item.visiteDate.split(" ")[0]; // 获取日期部分
                if (!grouped[date]) {
                    grouped[date] = [];
                }
                grouped[date].push(item);
            });

            // 按日期倒序排序
            this.groupedList = Object.keys(grouped)
                .sort((a, b) => new Date(b) - new Date(a))
                .reduce((acc, key) => {
                    acc[key] = grouped[key];
                    return acc;
                }, {});
        },

        toThousands(val, column) {
            if (val) {
                let valList = val.toString().split(".");
                let num = Number(valList[0]);
                let newVal = column.thousands ? num.toLocaleString() : num;
                return valList[1] ? newVal + "." + valList[1] : newVal;
            } else {
                return val;
            }
        },
        dataAll() {
            // getPhysicianAllDictionaryData(this.optionsObj);
        },
        openData(e) { },
        setDefaultQuery() {
            const defaultSortConfig = [];
            const sortField = defaultSortConfig.map(
                (o) => (o.sort === "desc" ? "-" : "") + o.field
            );
            this.listQuery.sidx = sortField.join(",");
        },
        //初始化查询的默认数据
        async initSearchData() {
            this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
        },
        relationFormClick(item, column) {
            let vModel = column.__vModel__ + "_id";
            let id = item[vModel];
            let modelId = column.modelId;
            if (!id || !modelId) return;
            let config = {
                modelId: modelId,
                id: id,
                formTitle: "详情",
                noShowBtn: 1,
            };
            this.$nextTick(() => {
                const url =
                    "/pages/apply/dynamicModel/detail?config=" +
                    this.xunda.base64.encode(JSON.stringify(config), "UTF-8");
                uni.navigateTo({
                    url: url,
                });
            });
        },
        async upCallback(page) {
            if (!this.firstInitSearchData) {
                await this.initSearchData();
                this.firstInitSearchData = true;
            }

            const query = {
                currentPage: page.num,
                pageSize: page.size,
                menuId: this.menuId,
                ...this.listQuery,
                ...this.searchForm,
                dataType: 0,
                patientFlag: this.type === "patient",
                physicianFlag: this.type === "physician",
            };

            // 添加月份筛选
            if (this.selectedMonth) {
                const [year, month] = this.selectedMonth.split("-");
                query.startDate = `${year}-${month}-01`;
                const nextMonth =
                    Number(month) === 12
                        ? `${Number(year) + 1}-01-01`
                        : `${year}-${(Number(month) + 1).toString().padStart(2, "0")}-01`;
                query.endDate = nextMonth;
            }
            getAppList(query)
                .then((res) => {
                    let _list = res.data.list;
                    this.mescroll.endSuccess(_list.length);
                    if (page.num == 1) this.list = [];
                    this.list = this.list.concat(_list);
                    // 按日期分组数据
                    this.groupListByDate(this.list);
                })
                .catch(() => {
                    this.mescroll.endSuccess(this.list.length);
                });
        },
        open(index) {
            this.list[index].show = true;
            this.list.map((val, idx) => {
                if (index != idx) this.list[idx].show = false;
            });
        },
        search() {
            if (this.isPreview == "1") return;
            this.searchTimer && clearTimeout(this.searchTimer);
            this.searchTimer = setTimeout(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            }, 300);
        },
        goDetail(item) {
            let id = item.id;
            uni.navigateTo({
                url:
                    "/pages/flow-up/doVisitRecord?actionType=" + "detail" + "&id=" + id,
            });
        },
        getColumnList() {
            let columnPermissionList = [];
            let _appColumnList = this.appColumnList;
            for (let i = 0; i < _appColumnList.length; i++) {
                columnPermissionList.push(_appColumnList[i]);
            }
            this.columnList = columnPermissionList;
        },
        cellClick(item) {
            const findIndex = this.sortValue.findIndex((o) => o === item.value);
            if (findIndex < 0) {
                const findLikeIndex = this.sortValue.findIndex(
                    (o) => o.indexOf(item.sidx) > -1
                );
                if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
                this.sortValue.push(item.value);
            } else {
                this.sortValue.splice(findIndex, 1);
            }
        },
        handleSortReset() {
            this.sortValue = [];
        },
        handleSortSearch() {
            if (this.sortValue.length) {
                this.listQuery.sidx = this.sortValue.join(",");
            } else {
                this.setDefaultQuery();
            }
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },
        reset() {
            this.searchForm = JSON.parse(JSON.stringify(this.dataValue));
            this.key = new Date();
        },
        closeDropdown() {
            this.$refs.uDropdown.close();
            this.$nextTick(() => {
                this.list = [];
                this.mescroll.resetUpScroll();
            });
        },

        dataList(data) {
            let _list = data.list;
            return _list;
        },
    },
};
</script>

<style lang="scss">
page {
    background-color: #f5f7fa;
    height: 100%;
}

.list-view {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.search-box_sticky {
    background-color: #fff;
    padding: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.month-picker {
    margin-top: 20rpx;
}

.month-picker {
    margin-top: 20rpx;

    .month-scroll {
        white-space: nowrap;
        width: 100%;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    .month-input-wrapper {
        display: inline-block;
        vertical-align: middle;
        width: 200rpx;
        margin-right: 16rpx;
        background: #f8fafc;
        border-radius: 32rpx;
        padding: 0 20rpx;
    }

    .month-item {
        display: inline-block;
        padding: 12rpx 32rpx;
        margin-right: 16rpx;
        background: #f8fafc;
        border-radius: 32rpx;
        font-size: 26rpx;
        color: #64748b;
        transition: all 0.3s ease;

        &.active {
            background: #4a86e8;
            color: #fff;
        }

        &:active {
            transform: scale(0.95);
        }
    }
}

.visit-list-section {
    flex: 1;
    overflow: hidden;
}

.date-group {
    margin: 0 20rpx 20rpx;
}

.date-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    padding: 0 10rpx;
}

.date-text {
    font-size: 28rpx;
    color: #64748b;
    font-weight: 500;
}

.count-badge {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #94a3b8;
    background: #f1f5f9;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
}

.visit-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.visit-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
}

.patient-info {
    .name {
        font-size: 32rpx;
        color: #1e293b;
        font-weight: 500;
    }

    .id {
        margin-left: 16rpx;
        font-size: 26rpx;
        color: #64748b;
    }
}

.doctor-info {
    .label {
        font-size: 26rpx;
        color: #94a3b8;
    }

    .name {
        margin-left: 8rpx;
        font-size: 26rpx;
        color: #1e293b;
    }
}

.visit-time {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #64748b;

    text {
        margin-left: 8rpx;
    }
}

.floating-button {
    position: fixed;
    right: 32rpx;
    bottom: 32rpx;
    width: 96rpx;
    height: 96rpx;
    background: linear-gradient(135deg, #4a86e8, #60a5fa);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(74, 134, 232, 0.3);
    transition: transform 0.3s ease;

    &:active {
        transform: scale(0.95);
    }
}

.empty-state {
    padding: 60rpx;
    text-align: center;
}
</style>
