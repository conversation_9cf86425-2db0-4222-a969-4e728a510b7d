<template>
  <view class="flow-popup-content">
    <u-form
      ref="dataForm"
      :model="dataForm"
      :label-width="150"
      :errorType="['toast']"
    >
      <view class="content">
        <u-form-item
          label="退回节点"
          v-if="config.type === 'back' && config.backType"
          prop="backNodeCode"
          required
        >
          <view class="u-flex-col" style="justify-content: end; width: 100%">
            <XundaSelect
              v-model="dataForm.backNodeCode"
              :options="config.backNodeList"
              :props="props"
              :disabled="config.backNodeCode != 2"
            />
            <view
              style="width: 100%; text-align: right"
              class="u-m-t-20"
              v-if="config.backType == 3"
            >
              <u-radio-group v-model="dataForm.backType">
                <u-radio
                  @change="radioChange(item)"
                  v-for="(item, index) in list"
                  :key="index"
                  :name="item.name"
                  :disabled="item.disabled"
                >
                  {{ item.fullName }}
                </u-radio>
              </u-radio-group>
            </view>
          </view>
        </u-form-item>
        <u-form-item
          label="转审给谁"
          prop="handleIds"
          v-if="['transfer'].includes(config.type)"
          required
        >
          <XundaUserSelect v-model="dataForm.handleIds" />
        </u-form-item>
        <u-form-item
          label="协办给谁"
          prop="handleVal"
          v-if="['assist'].includes(config.type)"
          required
        >
          <XundaUserSelect
            v-model="dataForm.handleVal"
            multiple
            @change="changeUserSelect"
          />
        </u-form-item>
        <view v-if="config.type === 'freeApprover'">
          <u-form-item label="加签人员" prop="addSignUserIdList" required>
            <XundaUserSelect v-model="dataForm.addSignUserIdList" multiple />
          </u-form-item>
          <u-form-item label="加签类型">
            <XundaSelect
              :options="typeList"
              v-model="dataForm.addSignType"
              @change="freeApproverChange"
            />
          </u-form-item>
          <u-form-item label="审批方式">
            <XundaRadio v-model="dataForm.counterSign" :options="options" />
          </u-form-item>
          <u-form-item label="会签比例" v-if="dataForm.counterSign == 1">
            <view class="u-flex-col free-box">
              <XundaSelect :options="ratioList" v-model="dataForm.auditRatio" />
              <text class="u-m-l-10 free-box-txt">达到会签比例则通过</text>
            </view>
          </u-form-item>
        </view>
        <u-form-item
          v-for="(item, index) in candidateList"
          :key="index"
          v-if="showCandidate"
          :label="item.nodeName + '审批人'"
          required
          :label-width="250"
        >
          <u-input
            v-if="item.hasCandidates"
            type="select"
            v-model="candidateValue[item.nodeCode]"
            @click="openSelect(item)"
            placeholder="请选择审批候选人"
            input-align="right"
          />
          <XundaUserSelect
            v-model="candidateValue[item.nodeCode]"
            v-else
            multiple
            @change="change($event, item.nodeCode)"
          />
        </u-form-item>
        <u-form-item v-if="showOpinion" :label="opinionTitle">
          <HandleOpinion
            :commonList="commonList"
            v-model="dataForm.handleOpinion"
            @addCommonWords="addCommonWords"
            :showCommon="false"
          ></HandleOpinion>
        </u-form-item>
        <u-form-item
          prop="handleOpinion"
          required
          label-position="top"
          label="审批意见"
          v-if="showApproval"
        >
          <HandleOpinion
            :commonList="commonList"
            v-model="dataForm.handleOpinion"
            @addCommonWords="addCommonWords"
          ></HandleOpinion>
        </u-form-item>
        <u-form-item
          :prop="signRule ? 'signImg' : ''"
          :required="signRule"
          v-if="config.hasSign"
        >
          <XundaSign v-model="dataForm.signImg" signType="ApprovalSign" />
        </u-form-item>
        <u-form-item v-if="config.hasFile">
          <view class="uploadFile">
            <XundaUploadFile
              v-model="dataForm.fileList"
              :limit="3"
              align="left"
            />
          </view>
        </u-form-item>
        <u-form-item label="抄送人员" v-if="showCustomCopy">
          <XundaUserSelect v-model="copyIds" multiple />
        </u-form-item>
      </view>
    </u-form>
    <view class="flowBefore-actions">
      <view
        class="u-flex-col buttom-btn-left-inner"
        @click.stop="xunda.goBack()"
      >
        <u-icon
          name="icon-ym"
          size="24"
          custom-prefix="icon-ym icon-ym-add-cancel"
        ></u-icon>
        <text>取消</text>
      </view>
      <u-button class="buttom-btn" type="primary" @click="confirm('confirm')"
        >确定
      </u-button>
    </view>
  </view>
</template>
<script>
import HandleOpinion from "./components/HandleOpinion.vue";
import { getSelector, Create } from "@/api/commonWords";
export default {
  components: {
    HandleOpinion,
  },
  data() {
    return {
      copyIds: [],
      selectList: [],
      candidateValue: {},
      candidateList: [],
      commonList: [],
      title: "",
      label: "",
      name: {
        reject: "拒绝",
        launchRecall: "撤回",
        auditRecall: "撤回",
        audit: "同意",
        back: "退回",
        freeApprover: "加签",
        transfer: "转审",
      },
      ratioList: [
        {
          fullName: "10%",
          id: 10,
        },
        {
          fullName: "20%",
          id: 20,
        },
        {
          fullName: "30%",
          id: 30,
        },
        {
          fullName: "40%",
          id: 40,
        },
        {
          fullName: "50%",
          id: 50,
        },
        {
          fullName: "60%",
          id: 60,
        },
        {
          fullName: "70%",
          id: 70,
        },
        {
          fullName: "80%",
          id: 80,
        },
        {
          fullName: "90%",
          id: 90,
        },
        {
          fullName: "100%",
          id: 100,
        },
      ],
      typeList: [
        {
          fullName: "审批前",
          id: 1,
        },
        {
          fullName: "审批后",
          id: 2,
        },
      ],
      options: [
        {
          fullName: "或签",
          id: 0,
        },
        {
          fullName: "会签",
          id: 1,
        },
      ],
      list: [
        {
          fullName: "重新审批",
          disabled: false,
          name: 1,
        },
        {
          fullName: "直接提交给我",
          disabled: false,
          name: 2,
        },
      ],
      props: {
        label: "nodeName",
        value: "nodeCode",
      },
      showCommonWords: false,
      dataForm: {
        auditRatio: 100,
        counterSign: 0,
        addSignType: 1,
        handleIds: "",
        handleVal: [],
        addSignUserIdList: "",
        fileList: [],
        handleOpinion: "",
        signImg: "",
        copyIds: "",
        branchList: [],
        candidateList: {},
        backNodeCode: "",
        backType: 1,
        candidateValue: {},
      },
      config: {},
      show: false,
      selectVal: {},
      isCandidates: false,
      rules: {
        backNodeCode: [
          {
            required: true,
            message: "请选择退回节点",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        signImg: [
          {
            required: true,
            message: "请签名",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        addSignUserIdList: [
          {
            required: true,
            message: "请选择加签人员",
            type: "array",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        handleIds: [
          {
            required: true,
            message: "请选择转审人员",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
        handleVal: [
          {
            required: true,
            message: "请选择协办人员",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
            type: "array",
          },
        ],
        handleOpinion: [
          {
            required: true,
            message: "请输入审批意见",
            // 可以单个或者同时写两个触发验证方式
            trigger: "blur,change",
          },
        ],
      },
    };
  },
  computed: {
    showApproval() {
      return (
        this.config.type === "audit" ||
        this.config.type === "reject" ||
        this.config.type === "approvalButton"
      );
    },
    showCustomCopy() {
      return (
        this.config.isCustomCopy &&
        ["audit", "reject"].includes(this.config.type)
      );
    },
    showCandidate() {
      return (
        !["transfer", "revoke", "recall", "back", "assist"].includes(
          this.config.type
        ) && this.isCandidates
      );
    },
    showOpinion() {
      return [
        "transfer",
        "assist",
        "revoke",
        "auditRecall",
        "launchRecall",
        "back",
        "freeApprover",
      ].includes(this.config.type);
    },
    opinionTitle() {
      if (
        this.config.type === "launchRecall" ||
        this.config.type === "auditRecall"
      )
        return "撤回原因";
      if (this.config.type === "transfer") return "转审原因";
      if (this.config.type === "revoke") return "撤销原因";
      if (this.config.type === "assist") return "协办原因";
      if (this.config.type === "back") return "退回意见";
      if (this.config.type === "freeApprover") return "加签意见";
      if (this.config.type === "audit" || this.config.type === "reject")
        return "审批意见";
      return "";
    },
    fileLabel() {
      if (this.config.type === "auditRecall") return "撤回附件";
      if (this.config.type === "audit" || this.config.type === "reject")
        return "审批附件";
      if (this.config.type === "freeApprover") return "加签附件";
      if (this.config.type === "back") return "退回附件";
      if (this.config.type === "transfer") return "转审附件";
      return "";
    },
    signRule() {
      return (
        this.config.hasSign &&
        (this.config.type === "audit" || this.config.type === "reject")
      );
    },
  },
  onLoad(data) {
    try {
      this.config = JSON.parse(decodeURIComponent(data.config));
    } catch {
      this.config = JSON.parse(data.config);
    }
    uni.$on("confirm", (data, nodeCode) => {
      this.selectConfirm(data, nodeCode);
    });
    this.init();
  },
  onReady() {
    this.$refs.dataForm.setRules(this.rules);
  },
  methods: {
    changeUserSelect(e) {
      this.dataForm.handleIds = e.join();
    },
    //选择审批候选人
    openSelect(item) {
      this.selectList = [];
      for (let o in this.selectVal) {
        if (o === item.nodeCode) this.selectList = this.selectVal[o];
      }
      item.formData = this.config.formData;
      item.taskId = this.config.taskId;
      item.selectList = this.selectList;
      item.candidateList = JSON.stringify(this.candidateList);
      uni.navigateTo({
        url:
          "/pages/workFlow/candiDateUserSelect/index?data=" +
          encodeURIComponent(JSON.stringify(item)),
      });
    },
    selectConfirm(e, nodeCode) {
      let data = e;
      let users = [];
      let val = [];
      let selectVal = [];
      if (!data.length) this.candidateValue = {};
      for (let i = 0; i < this.candidateList.length; i++) {
        for (let j = 0; j < data.length; j++) {
          if (data[j].nodeCode === this.candidateList[i].nodeCode) {
            val.push(data[j].fullName);
            selectVal.push(data[j]);
            this.$set(this.candidateValue, nodeCode, val.join(","));
            users.push(data[j].id);
          }
        }
      }
      this.$set(this.selectVal, nodeCode, selectVal);
      this.$set(this.dataForm.candidateList, nodeCode, users);
    },
    change(val, nodeCode) {
      if (val.length < 1) return;
      let vals = [];
      for (let i = 0; i < val.length; i++) {
        vals.push(val[i]);
      }
      this.$set(this.dataForm.candidateList, nodeCode, vals);
    },
    //选择审批候选人 end
    getSelector() {
      getSelector().then((res) => {
        this.commonList = res.data.list || [];
      });
    },
    confirmCommonWord(e) {
      this.dataForm.handleOpinion = e.commonWordsText;
    },
    handlePress(e) {
      this.$emit("handlePress");
    },
    addCommonWords() {
      let data = {
        commonWordsText: this.dataForm.handleOpinion,
        commonWordsType: 1,
      };
      Create(data).then((res) => {
        this.$u.toast(res.msg);
      });
    },
    freeApproverChange(e) {
      this.isCandidates = false;
      if (this.config.hasFreeApprover && e == 2 && this.candidateList.length)
        this.isCandidates = true;
    },
    init() {
      this.getSelector();
      this.config.candidateList.map((o) => {
        this.isCandidates = o.isCandidates;
      });
      this.copyIds = this.config?.circulateUser || "";
      this.dataForm.backNodeCode = this.config?.backNodeList?.length
        ? this.config.backNodeList[0].nodeCode
        : "";
      this.candidateList = this.config.candidateList || [];
      this.userInfo = uni.getStorageSync("userInfo") || {};
      this.dataForm.signImg = this.userInfo.signImg;
      if (
        this.config.type === "freeApprover" &&
        this.dataForm.addSignType == "1"
      )
        this.isCandidates = false;
      switch (this.config.type) {
        case "transfer":
          this.title = "转审";
          this.label = "转审";
          break;
        case "assist":
          this.title = "协办";
          this.label = "协办";
          break;
        case "revoke":
          this.title = "撤销流程";
          this.label = "撤销";
          break;
        case "launchRecall":
          this.title = "撤回流程";
          this.label = "撤回";
          break;
        case "reject":
        case "audit":
          this.title = "审批";
          this.label = "审批";
          break;
        case "auditRecall":
          this.title = "撤回审核";
          this.label = "撤回";
          break;
        case "freeApprover":
          this.title = "加签";
          this.label = "加签";
          break;
        case "back":
          this.title = "退回";
          this.label = "退回";
          break;
        case "submit":
          this.title = "提交审核";
          this.label = "提交审核";
          break;
        default:
          break;
      }
      uni.setNavigationBarTitle({
        title: this.title,
      });
    },
    confirm() {
      if (this.config.type === "freeApprover") {
        this.dataForm.addSignParameter = {
          addSignUserIdList: this.dataForm.addSignUserIdList,
          auditRatio: this.dataForm.auditRatio,
          counterSign: this.dataForm.counterSign,
          addSignType: this.dataForm.addSignType,
        };
      }
      if (!this.config.hasSign) delete this.dataForm.signImg;
      this.dataForm.copyIds =
        Array.isArray(this.copyIds) &&
        this.copyIds.length &&
        this.copyIds.join();
      if (this.config.backType !== 3)
        this.dataForm.backType = this.config.backType;
      let data = {
        ...this.dataForm,
        eventType: ["auditRecall", "launchRecall"].includes(this.config.type)
          ? "recall"
          : this.config.type,
      };
      if (this.isCandidates) {
        data.candidateList = this.dataForm.candidateList;
        if (!Object.keys(this.dataForm.candidateList).length)
          return this.$u.toast("候选人不能为空");
      }
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          uni.$emit("operate", data);
          setTimeout(() => {
            uni.navigateBack();
          }, 500);
        }
      });
    },
  },
};
</script>

<style lang="scss">
page {
  height: 100%;
  background-color: #fff !important;
}

::v-deep .u-form-item--left {
  align-items: flex-start !important;
}

.buttom-btn-left-inner {
  width: 50%;
}

.free-box {
  width: 100%;

  .free-box-txt {
    text-align: end;
  }
}

.flow-popup-content {
  .signature-box {
    border-top: none;
  }

  .content {
    padding: 0 20rpx;

    .head-title {
      height: 80rpx;
      justify-content: space-between;
      color: #333333;
    }

    .uploadFile {
      width: 100%;
      padding-bottom: 8rpx;
      border-top: 1rpx solid #fbfbfc;
    }
  }
}
</style>
