<template>
  <view class="flowLaunch-v">
    <view class="flow-list" v-if="list.length > 0">
      <view class="flow-list-box">
        <uni-swipe-action ref="swipeAction">
          <uni-swipe-action-item
            v-for="(item, index) in list"
            :key="item.id"
            :threshold="0"
            :right-options="options"
            @click="handleClick(index)"
            :disabled="item.swipeAction"
          >
            <view
              class="item"
              @click="goDetail(item)"
              :id="'item' + index"
              ref="mydom"
            >
              <view class="item-left">
                <view class="item-left-top">
                  <view class="u-m-r-8" v-if="item.delegateUser">
                    <u-tag text="委托" type="success" size="mini" />
                  </view>
                  <view
                    class="common-lable"
                    :class="{
                      'urgent-lable': item.flowUrgent == 2,
                      'important-lable': item.flowUrgent == 3,
                    }"
                  >
                    {{ getLableValue(item.flowUrgent) }}
                  </view>
                  <text class="title u-font-28 u-line-1">{{
                    item.fullName
                  }}</text>
                </view>
                <text class="title u-line-1 u-font-24"
                  >审批节点：{{ item.currentNodeName
                  }}<text class="titInner">{{
                    item.thisStep ? item.thisStep : ""
                  }}</text></text
                >
                <text class="time title u-font-24"
                  >发起时间：<text class="titInner">{{
                    item.startTime
                      ? $u.timeFormat(item.startTime, "yyyy-mm-dd hh:MM:ss")
                      : ""
                  }}</text></text
                >
              </view>
              <view class="item-right">
                <image
                  :src="item.flowStatus"
                  mode="widthFix"
                  class="item-right-img"
                >
                </image>
              </view>
            </view>
          </uni-swipe-action-item>
        </uni-swipe-action>
      </view>
    </view>
  </view>
</template>
<script>
import { delFlowLaunch } from "@/api/workFlow/template";
export default {
  name: "FlowList",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    swipeAction: {
      type: Boolean,
      default: false,
    },
    category: {
      type: String,
      default: "0",
    },
  },
  data() {
    return {
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#dd524d",
          },
        },
      ],
      title: "",
    };
  },

  methods: {
    goDetail(item) {
      const config = {
        opType: item.opType,
        operatorId: item.id,
        category: this.category,
        ...item,
      };
      uni.navigateTo({
        url:
          "/pages/workFlow/flowBefore/index?config=" +
          this.xunda.base64.encode(JSON.stringify(config)),
      });
    },
    handleClick(index) {
      const item = this.list[index];
      if ([1, 2, 3, 5].includes(item.status)) {
        this.$u.toast("流程正在审核,请勿删除");
        this.list[index].show = false;
        return;
      }
      delFlowLaunch(item.id).then((res) => {
        this.$u.toast(res.msg);
        this.list.splice(index, 1);
      });
    },
    getLableValue(value) {
      var lableValue = "";
      switch (value) {
        case 1:
          lableValue = "普通";
          break;
        case 2:
          lableValue = "重要";
          break;
        case 3:
          lableValue = "紧急";
          break;
        default:
          lableValue = "普通";
          break;
      }
      return lableValue;
    },
  },
};
</script>
<style scoped lang="scss">
.flowLaunch-v {
  width: 100%;

  .flow-list-box {
    width: 95%;

    .item-left-top {
      display: flex;
      width: 100%;

      .common-lable {
        font-size: 24rpx;
        padding: 2rpx 8rpx;
        margin-right: 8rpx;
        border-radius: 8rpx;
        color: #409eff;
        border: 1px solid #409eff;
        background-color: #e5f3fe;
      }

      .urgent-lable {
        color: #e6a23c;
        border: 1px solid #e6a23c;
        background-color: #fef6e5;
      }

      .important-lable {
        color: #f56c6c;
        border: 1px solid #f56c6c;
        background-color: #fee5e5;
      }

      .title {
        width: unset;
        flex: 1;
        min-width: 0;
      }
    }
  }
}
</style>
