import request from '@/utils/request'

const newsApi = '/api/flowUp/news'

// 获取资讯列表
export const getNewsList = (data) => {
    return request({
        url: newsApi + `/getAppList`,
        method: "post",
        data: data,
    });
}

// 获取视频列表
export const getVideoList = (data) => {
    return request({
        url: newsApi + '/videos',
        method: "post",
        data
    })
}

// 获取资讯列表
export const getNewsDetail = (id) => {
    return request({
        url: newsApi + `/getAppDetail/${id}`,
        method: "get",
    });
}

// 获取相关资讯
export const getRelatedNews = (id) => {
    return request({
        url: newsApi + `/getAppRelated/${id}`,
        method: "get",
    });
}

// 更新文字访问量
export const updateViewCount = (id) => {
    return request({
        url: newsApi + `/updateViewCount`,
        method: "post",
        data: {
            id: id
        }
    });
}
