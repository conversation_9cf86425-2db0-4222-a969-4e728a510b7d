<template>
  <view class="patient-form-container" v-if="!loading">
    <!-- 顶部患者信息卡片 -->
    <view class="patient-info-card">
      <view class="patient-header">
        <view class="patient-avatar">
          <text class="avatar-text">{{ getPatientAvatarText() }}</text>
        </view>
        <view class="patient-details">
          <view class="patient-name">{{ dataForm.name || "未知患者" }}</view>
          <view class="patient-meta">
            <text class="meta-label">住院号:</text>
            <text class="meta-value">{{ dataForm.admissionNo || "暂无" }}</text>
          </view>
        </view>
        <view class="action-type-badge" :class="getActionTypeClass()">
          {{ getActionTypeText() }}
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-sections">
      <!-- 患者基本信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">👨‍⚕️</view>
          <text class="section-title">患者信息</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <text class="form-label required">姓名</text>
            <XundaInput v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.name" :disabled="false"
              :fieldKey="'name'" placeholder="请输入患者姓名" clearable class="form-input">
            </XundaInput>
            <view v-else class="form-value-display">{{ dataForm.name || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label required">年龄</text>
            <XundaInputNumber v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.age" :disabled="false"
              :fieldKey="'age'" placeholder="请输入年龄" clearable class="form-input">
            </XundaInputNumber>
            <view v-else class="form-value-display">{{ dataForm.age || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label required">性别</text>
            <XundaSelect v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.sex" :disabled="false"
              :fieldKey="'sex'" placeholder="请选择性别" clearable :showSearch="false" :options="optionsObj.SexOptions"
              :props="optionsObj.defaultProps" class="form-select">
            </XundaSelect>
            <view v-else class="form-value-display">{{ getSexText(dataForm.sex) || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label">身份证</text>
            <XundaInput v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.idCard" :disabled="false"
              :fieldKey="'idCard'" placeholder="请输入身份证号" clearable class="form-input">
            </XundaInput>
            <view v-else class="form-value-display">{{ dataForm.idCard || "暂无" }}</view>
          </view>
        </view>
      </view>

      <!-- 住院信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">🏥</view>
          <text class="section-title">住院信息</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <text class="form-label">住院号</text>
            <XundaInput v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.admissionNo" :disabled="false"
              :fieldKey="'admissionNo'" placeholder="请输入住院号" clearable class="form-input">
            </XundaInput>
            <view v-else class="form-value-display">{{ dataForm.admissionNo || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label">入院日期</text>
            <XundaDatePicker v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.admissionDate" :disabled="false"
              :fieldKey="'admissionDate'" placeholder="请选择入院日期" clearable format="yyyy-MM-dd" type="date"
              class="form-datepicker">
            </XundaDatePicker>
            <view v-else class="form-value-display">{{ dataForm.admissionDate || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label">出院日期</text>
            <XundaDatePicker v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.dischargeDate" :disabled="false"
              :fieldKey="'dischargeDate'" placeholder="请选择出院日期" clearable format="yyyy-MM-dd" type="date"
              class="form-datepicker">
            </XundaDatePicker>
            <view v-else class="form-value-display">{{ dataForm.dischargeDate || "暂无" }}</view>
          </view>

          <view class="form-item">
            <text class="form-label">入院诊断</text>
            <textarea v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.admittingDiagnosis" :disabled="false"
              :fieldKey="'admittingDiagnosis'" placeholder="请输入入院诊断信息" clearable class="form-textarea">
            </textarea>
            <view v-else class="form-value-display textarea-display">{{ dataForm.admittingDiagnosis || "暂无" }}</view>
          </view>
        </view>
      </view>

      <!-- 地址信息 -->
      <view class="form-section">
        <view class="section-header">
          <view class="section-icon">📍</view>
          <text class="section-title">地址信息</text>
        </view>
        <view class="section-content">
          <view class="form-item">
            <text class="form-label">详细地址</text>
            <XundaLocation v-if="jurisdictionType !== 'btn_detail'" v-model="dataForm.addressDetail" :disabled="false"
              :fieldKey="'addressDetail'" placeholder="请输入详细地址" clearable :adjustmentScope="500"
              :locationScope="locationScope.addressDetail" class="form-location">
            </XundaLocation>
            <view v-else class="form-value-display">{{ dataForm.addressDetail || "暂无" }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons" v-if="jurisdictionType !== 'btn_detail'">
      <view class="action-row">
        <view class="action-btn secondary-btn" @click="resetForm">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">取消</text>
        </view>
        <view class="action-btn primary-btn" @click="submitForm" :class="{ 'btn-loading': btnLoading }">
          <view class="btn-icon" v-if="!btnLoading">✅</view>
          <view class="btn-loading-icon" v-if="btnLoading">⏳</view>
          <text class="btn-text">{{ btnLoading ? "提交中..." : "确定" }}</text>
        </view>
      </view>
    </view>

    <!-- 详情页面的返回按钮 -->
    <view class="action-buttons" v-else>
      <view class="action-row">
        <view class="action-btn primary-btn full-width-btn" @click="resetForm">
          <view class="btn-icon">↩️</view>
          <text class="btn-text">返回</text>
        </view>
      </view>
    </view>

    <u-action-sheet @click="handleAction" :list="actionList" :tips="{ text: '更多操作', color: '#000', fontSize: 30 }"
      v-model="showAction">
    </u-action-sheet>
    <u-modal v-model="show" :content="content" width="70%" border-radius="16" :content-style="{
      fontSize: '28rpx',
      padding: '20rpx',
      lineHeight: '44rpx',
      textAlign: 'left',
    }" :titleStyle="{ padding: '20rpx' }" :confirm-style="{ height: '80rpx', lineHeight: '80rpx' }" :title="title"
      confirm-text="确定">
      <view class="slot-content">
        <rich-text :nodes="content"></rich-text>
      </view>
    </u-modal>
  </view>
</template>

<script>
import { getDictionaryDataSelector } from "@/api/common";
import { useBaseStore } from "@/store/modules/base";
import { getInfo, create, update } from "@/api/flow-up/patient";
export default {
  data() {
    return {
      idList: [],
      index: 0,
      actionList: [],
      actionListLength: false,
      showAction: false,
      btnLoading: false,
      loading: false,
      text: "提示：测试文本",
      tableKey: "",
      timeKey: +new Date(),
      optionsObj: {
        //选项配置
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        }, // 默认下拉选择键值
      },
      labelwidth: 150,
      dataForm: {
        id: "",
        name: "",
        age: undefined,
        sex: "",
        idCard: "",
        admissionNo: "",
        addressDetail: "",
        dischargeDate: undefined,
        admissionDate: undefined,
        admittingDiagnosis: "",
        pipeBedPhysician: "",
        tnumber: undefined,
        dnumber: "",
        rate: undefined,
        fCount: undefined,
        type: "",
        address: "",
        longitude: undefined,
        latitude: undefined,
      },
      rules: {
        name: [
          {
            required: "true",
            message: "姓名不能为空",
          },
        ],
        age: [
          {
            required: "true",
          },
        ],
        sex: [
          {
            required: "true",
            message: "性别不能为空",
          },
        ],
      },
      locationScope: {
        addressDetail: "",
      },
      regList: {},
      ableAll: {},
      childIndex: -1,
      dataValue: {},
      isEdit: false,
      userInfo: {},
      content: "",
      title: "",
      show: false,
      jurisdictionType: "",
    };
  },
  created() {
    uni.$on("linkPageConfirm", (subVal) => {
      if (this.tableKey) {
        for (let i = 0; i < subVal.length; i++) {
          let t = subVal[i];
          if (this["get" + this.tableKey]) {
            this["get" + this.tableKey](t);
          }
        }
        this.childIndex = -1;
        this.collapse();
      }
    });
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onLoad(option) {
    this.jurisdictionType = option.jurisdictionType;
    this.menuId = option.menuId;
    this.intSelectOption();
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.dataForm.id = option.id || "";
    let _title = "";
    if (option.jurisdictionType == "btn_edit") {
      _title = "编辑";
    }
    if (option.jurisdictionType == "btn_detail") {
      _title = "详情";
    }
    if (option.jurisdictionType == "btn_add") {
      _title = "新增";
    }
    if (_title) {
      uni.setNavigationBarTitle({
        title: _title,
      });
    }

    this.dataAll();
    this.initData();
    this.dataValue = JSON.parse(JSON.stringify(this.dataForm));
    this.idList = option.idList ? option.idList.split(",") : [];
    for (let i = 0; i < this.idList.length; i++) {
      if (this.idList[i] == option.id) {
        this.index = i;
      }
    }
    setTimeout(() => {
      uni.$emit("initCollapse");
    }, 50);
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onReady() {
    this.$nextTick(() => {

    });
  },
  watch: {
    dataForm: {
      handler(val, oldVal) { },
      deep: true,
    },
    jurisdictionType: {
      handler() {
        // 监听数据变化
        let _title = "";
        if (this.jurisdictionType == "btn_edit") {
          _title = "编辑";
        }
        if (this.jurisdictionType == "btn_detail") {
          _title = "详情";
        }
        if (this.jurisdictionType == "btn_add") {
          _title = "新增";

        }
        if (_title) {
          uni.setNavigationBarTitle({
            title: _title,
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    // 获取患者头像文字
    getPatientAvatarText() {
      const name = this.dataForm.name;
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取操作类型文本
    getActionTypeText() {
      switch (this.jurisdictionType) {
        case "btn_add":
          return "新增患者";
        case "btn_edit":
          return "编辑患者";
        case "btn_detail":
          return "患者详情";
        default:
          return "患者信息";
      }
    },

    // 获取操作类型样式类
    getActionTypeClass() {
      switch (this.jurisdictionType) {
        case "btn_add":
          return "badge-add";
        case "btn_edit":
          return "badge-edit";
        case "btn_detail":
          return "badge-detail";
        default:
          return "badge-default";
      }
    },

    // 获取性别显示文本
    getSexText(sexCode) {
      if (!sexCode) return "";
      const sexOption = this.optionsObj.SexOptions?.find(
        (item) => item.enCode === sexCode
      );
      return sexOption ? sexOption.fullName : "";
    },

    // 初始化下拉选项
    intSelectOption() {
      getDictionaryDataSelector("Sex").then((res) => {
        this.optionsObj.SexOptions = res.data.list;
      });
      getDictionaryDataSelector("FlowType").then((res) => {
        this.optionsObj.FlowTypeOptions = res.data.list;
      });
    },
    handleAction(index) {
      if (
        this.actionList[index].id === "save_add" ||
        this.actionList[index].id === "save_proceed"
      ) {
        this.submitForm(1);
      } else {
        this.calculation(this.actionList[index].id, index);
      }
    },
    onCollapseChange() {
      uni.$emit("initCollapse");
    },
    calculation(type, index) {
      if (type === "upper") {
        this.index--;
        this.actionList[index + 1].disabled = false;
        if (this.index == 0) this.actionList[index].disabled = true;
      } else {
        this.index++;
        this.actionList[index - 1].disabled = false;
        if (this.index == this.idList.length - 1)
          this.actionList[index].disabled = true;
      }
      this.dataForm.id = this.idList[this.index];
      this.initData();
    },

    clickIcon(label, tipLabel) {
      this.content = tipLabel;
      this.title = label;
      this.show = true;
    },
    resetForm() {
      uni.navigateBack();
    },
    dataAll() {
      this.collapse();
    },
    initData() {
      this.$nextTick(function () {
        if (this.dataForm.id) {
          this.loading = true;
          getInfo(this.dataForm.id).then((res) => {
            this.dataInfo(res.data);
            this.loading = false;
          });
        } else {

        }
      });
    },

    validate() {
      if (!this.dataForm.name) {
        this.$u.toast('请填写姓名');
        return false;
      }
      if (!this.dataForm.age) {
        this.$u.toast('请填写年龄');
        return false;
      }
      if (!this.dataForm.sex) {
        this.$u.toast('请选择性别');
        return false;
      }
      if (!this.dataForm.idCard) {
        this.$u.toast('请填写身份证号');
        return false;
      }

      return true
    },
    submitForm(type) {
      var _data = this.dataList();
      let valid = this.validate();
      if (!valid) return;
      this.btnLoading = true;
      if (this.dataForm.id) {
        update(_data)
          .then((res) => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  if (type != 1) {
                    uni.$emit("refresh");
                    uni.navigateBack();
                  }
                  this.btnLoading = false;
                }, 1500);
              },
            });
          })
          .catch(() => {
            this.btnLoading = false;
          });
      } else {
        create(_data)
          .then((res) => {
            uni.showToast({
              title: res.msg,
              complete: () => {
                setTimeout(() => {
                  if (type == 1) {
                    this.dataForm = JSON.parse(
                      JSON.stringify(this.dataValue)
                    );

                  } else {
                    uni.$emit("refresh");
                    uni.navigateBack();
                  }
                  this.btnLoading = false;
                }, 1500);
              },
            });
          })
          .catch(() => {
            this.btnLoading = false;
          });
      }
    },
    dataList() {
      var _data = this.dataForm;
      return _data;
    },
    dataInfo(dataAll) {
      let dataList = JSON.parse(JSON.stringify(dataAll));
      this.dataForm = dataList;
      this.isEdit = true;
      this.dataAll();
      this.isEdit = false;
      this.childIndex = -1;
      this.collapse();
      setTimeout(() => {
        uni.$emit("initCollapse");
      }, 50);
    },
    collapse() {
      setTimeout(() => { }, 50);
    },
  },
};
</script>

<style lang="scss">
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.patient-form-container {
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: 200rpx;
}

// 顶部患者信息卡片
.patient-info-card {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.3);
  color: #fff;

  .patient-header {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .patient-avatar {
      width: 80rpx;
      height: 80rpx;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3rpx solid rgba(255, 255, 255, 0.3);

      .avatar-text {
        color: #fff;
        font-size: 28rpx;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }
    }

    .patient-details {
      flex: 1;
      min-width: 0;

      .patient-name {
        font-size: 36rpx;
        font-weight: 700;
        color: #fff;
        margin-bottom: 12rpx;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
      }

      .patient-meta {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .meta-label {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }

        .meta-value {
          font-size: 26rpx;
          color: #fff;
          font-weight: 600;
        }
      }
    }

    .action-type-badge {
      padding: 12rpx 20rpx;
      border-radius: 20rpx;
      font-size: 24rpx;
      font-weight: 600;
      border: 2rpx solid rgba(255, 255, 255, 0.3);

      &.badge-add {
        background: rgba(76, 175, 80, 0.9);
        color: #fff;
      }

      &.badge-edit {
        background: rgba(255, 193, 7, 0.9);
        color: #fff;
      }

      &.badge-detail {
        background: rgba(96, 125, 139, 0.9);
        color: #fff;
      }

      &.badge-default {
        background: rgba(255, 255, 255, 0.2);
        color: #fff;
      }
    }
  }
}

// 表单区域
.form-sections {
  .form-section {
    background: #fff;
    border-radius: 20rpx;
    margin-bottom: 24rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
    border: 1rpx solid rgba(25, 118, 210, 0.1);

    .section-header {
      background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
      padding: 24rpx 28rpx;
      display: flex;
      align-items: center;
      gap: 16rpx;
      border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

      .section-icon {
        font-size: 32rpx;
      }

      .section-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }
    }

    .section-content {
      padding: 28rpx;

      .form-item {
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          font-size: 28rpx;
          color: #1565c0;
          font-weight: 600;
          margin-bottom: 12rpx;
          display: block;

          &.required::after {
            content: "*";
            color: #f44336;
            margin-left: 4rpx;
          }
        }

        .form-value-display {
          padding: 20rpx;
          background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
          border-radius: 16rpx;
          border: 1rpx solid rgba(25, 118, 210, 0.1);
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          line-height: 1.4;
          min-height: 44rpx;
          display: flex;
          align-items: center;

          &.textarea-display {
            min-height: 120rpx;
            align-items: flex-start;
            padding-top: 20rpx;
            word-break: break-all;
            line-height: 1.6;
          }
        }

        .form-input,
        .form-select,
        .form-datepicker,
        .form-location {

          ::v-deep .xunda-input,
          ::v-deep .xunda-input-number,
          ::v-deep .xunda-select,
          ::v-deep .xunda-date-picker,
          ::v-deep .xunda-textarea,
          ::v-deep .xunda-location {
            width: 100%;
            min-height: 60rpx;
            padding: 20rpx;
            border: 2rpx solid #e9ecef;
            border-radius: 16rpx;
            font-size: 28rpx;
            color: #333;
            background: #fff;
            transition: all 0.3s ease;
            box-sizing: border-box;

            &:focus {
              border-color: #1976d2;
              box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
              outline: none;
            }

            &::placeholder {
              color: #adb5bd;
              font-size: 26rpx;
            }
          }

          ::v-deep .xunda-textarea {
            min-height: 120rpx;
            resize: none;
          }
        }
      }

      .form-textarea {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        border: 2rpx solid #e9ecef;
        border-radius: 16rpx;
        font-size: 28rpx;
        color: #333;
        background: #fff;
        transition: all 0.3s ease;
        box-sizing: border-box;
        resize: none;

        &:focus {
          border-color: #1976d2;
          box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
          outline: none;
        }

        &::placeholder {
          color: #adb5bd;
          font-size: 26rpx;
        }
      }
    }
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      .btn-loading-icon {
        font-size: 28rpx;
        animation: spin 1s linear infinite;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }

        &.btn-loading {
          opacity: 0.8;
          pointer-events: none;
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.full-width-btn {
        flex: 100%;
      }
    }
  }
}

// 动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .patient-form-container {
    padding: 16rpx;
    padding-bottom: 180rpx;
  }

  .patient-info-card {
    padding: 24rpx;

    .patient-header {
      gap: 16rpx;

      .patient-avatar {
        width: 64rpx;
        height: 64rpx;

        .avatar-text {
          font-size: 24rpx;
        }
      }

      .patient-details {
        .patient-name {
          font-size: 32rpx;
        }

        .patient-meta {
          .meta-label {
            font-size: 22rpx;
          }

          .meta-value {
            font-size: 24rpx;
          }
        }
      }

      .action-type-badge {
        padding: 8rpx 16rpx;
        font-size: 22rpx;
      }
    }
  }

  .form-sections {
    .form-section {
      .section-header {
        padding: 20rpx 24rpx;

        .section-icon {
          font-size: 28rpx;
        }

        .section-title {
          font-size: 28rpx;
        }
      }

      .section-content {
        padding: 20rpx;

        .form-item {
          margin-bottom: 16rpx;

          .form-label {
            font-size: 26rpx;
          }

          .form-value-display {
            padding: 16rpx;
            font-size: 26rpx;

            &.textarea-display {
              min-height: 100rpx;
            }
          }

          ::v-deep .xunda-input,
          ::v-deep .xunda-input-number,
          ::v-deep .xunda-select,
          ::v-deep .xunda-date-picker,
          ::v-deep .xunda-textarea,
          ::v-deep .xunda-location {
            padding: 16rpx;
            font-size: 26rpx;

            &::placeholder {
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }

  .action-buttons {
    padding: 20rpx 16rpx;

    .action-row {
      gap: 12rpx;

      .action-btn {
        height: 80rpx;

        .btn-icon,
        .btn-loading-icon {
          font-size: 24rpx;
        }

        .btn-text {
          font-size: 26rpx;
        }
      }
    }
  }
}
</style>