<template>
  <view class="dynamicModel-v">
    <view class="xunda-wrap xunda-wrap-form" v-if="isShow">
      <childForm ref="child" :config="config" />
    </view>
  </view>
</template>

<script>
import childForm from "@/pages/workFlow/flowBefore/form";
import { getConfigData } from "@/api/apply/visualDev";
export default {
  name: "scanForm",
  components: {
    childForm,
  },
  data() {
    return {
      webType: "",
      origin: "",
      config: {},
      formConf: {},
      flowConfig: {},
      isShow: false,
      dataSource: "",
    };
  },
  onLoad(data) {
    let obj = JSON.parse(data.config);
    this.initData(obj);
  },
  methods: {
    initData(data) {
      getConfigData(data.id, {
        type: data.previewType,
      }).then((res) => {
        if (!res.data || !res.data.formData) return;
        this.config = {
          formEnCode: data.enCode,
          flowId: res.data.id,
          formConf: res.data.formData,
          fullName: res.data.fullName,
        };
        this.isShow = true;
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
}

.dynamicModel-v {
  height: 100%;
}
</style>
