<template>
  <view class="xunda-popup-select">
    <u-input
      input-align="right"
      type="select"
      v-model="innerValue"
      disabled
      @click="openSelect"
      :placeholder="placeholder"
    >
    </u-input>
  </view>
</template>

<script>
import { getDataInterfaceDataInfoByIds } from "@/api/common.js";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
export default {
  name: "xunda-popup-select",
  props: {
    modelValue: {
      default: "",
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    columnOptions: {
      type: Array,
      default: () => [],
    },
    relationField: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "popup",
    },
    propsValue: {
      type: String,
      default: "",
    },
    popupTitle: {
      type: String,
      default: "",
    },
    interfaceId: {
      type: String,
      default: "",
    },
    hasPage: {
      type: Boolean,
      default: false,
    },
    pageSize: {
      type: Number,
      default: 100000,
    },
    vModel: {
      type: String,
      default: "",
    },
    rowIndex: {
      default: null,
    },
    formData: {
      type: Object,
    },
    templateJson: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectShow: false,
      innerValue: "",
      defaultValue: "",
      current: null,
      defaultOptions: [],
      firstVal: "",
      firstId: 0,
      selectData: [],
    };
  },
  watch: {
    modelValue(val) {
      this.setDefault();
    },
  },
  created() {
    uni.$on("confirm", (subVal, innerValue, list, selectData) => {
      this.confirm(subVal, innerValue, list, selectData);
    });
    this.setDefault();
  },
  methods: {
    setDefault() {
      if (this.modelValue) {
        if (!this.interfaceId) return;
        let query = {
          ids: this.multiple ? this.modelValue : [this.modelValue],
          interfaceId: this.interfaceId,
          propsValue: this.propsValue,
          relationField: this.relationField,
          paramList: this.getParamList(),
        };
        getDataInterfaceDataInfoByIds(this.interfaceId, query).then((res) => {
          if (this.multiple) {
            this.selectData = res.data || [];
            let label = [];
            this.selectData.forEach((o, i) => {
              for (let j = 0; j < query.ids.length; j++) {
                if (query.ids[j] == o[this.propsValue]) {
                  if (!!o[this.relationField])
                    label.push(o[this.relationField]);
                }
              }
            });
            this.innerValue = label.length == 1 ? label[0] : label.join(",");
          } else {
            const data = res.data && res.data.length ? res.data[0] : {};
            this.innerValue = data[this.relationField];
            if (!this.vModel) return;
            let relationData = baseStore.relationData;
            relationData[this.vModel] = data;
            baseStore.updateRelationData(relationData);
          }
        });
      } else {
        this.innerValue = "";
        if (!this.vModel) return;
        let relationData = baseStore.relationData;
        relationData[this.vModel] = {};
        baseStore.updateRelationData(relationData);
      }
    },
    getParamList() {
      let templateJson = this.templateJson;
      if (!this.formData) return templateJson;
      for (let i = 0; i < templateJson.length; i++) {
        if (templateJson[i].relationField && templateJson[i].sourceType == 1) {
          if (templateJson[i].relationField.includes("-")) {
            let tableVModel = templateJson[i].relationField.split("-")[0];
            let childVModel = templateJson[i].relationField.split("-")[1];
            templateJson[i].defaultValue =
              (this.formData[tableVModel] &&
                this.formData[tableVModel][this.rowIndex] &&
                this.formData[tableVModel][this.rowIndex][childVModel]) ||
              "";
          } else {
            templateJson[i].defaultValue =
              this.formData[templateJson[i].relationField] || "";
          }
        }
      }
      return templateJson;
    },
    openSelect() {
      if (this.disabled) return;
      const pageSize = this.hasPage ? this.pageSize : 100000;
      let data = {
        columnOptions: this.columnOptions,
        relationField: this.relationField,
        type: this.type,
        propsValue: this.propsValue,
        modelId: this.interfaceId,
        hasPage: this.hasPage,
        pageSize,
        id: !this.multiple ? [this.modelValue] : this.modelValue,
        vModel: this.vModel,
        popupTitle: this.popupTitle || "选择数据",
        innerValue: this.innerValue,
        paramList: this.getParamList(),
        multiple: this.multiple,
        selectData: this.selectData,
      };
      uni.navigateTo({
        url:
          "/pages/apply/popSelect/index?data=" +
          encodeURIComponent(JSON.stringify(data)),
      });
    },
    confirm(subVal, innerValue, vModel, selectData) {
      if (vModel === this.vModel) {
        this.firstVal = innerValue;
        this.firstId = subVal;
        this.innerValue = innerValue;
        this.$emit("update:modelValue", subVal);
        this.$emit("change", subVal, selectData);
      }
    },
  },
};
</script>

<style>
.xunda-popup-select {
  width: 100%;
  height: 100%;
}
</style>
