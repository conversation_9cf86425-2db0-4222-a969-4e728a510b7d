

import request from "@/utils/request";

/**
 * 获取患者列表
 * @param {*} query 
 * @returns 
 */
export const getList = function (query) {
    return request({
        url: "/api/flowUp/physician/getList",
        method: "post",
        data: query,
    })
}


export const createPhysician = function (data) {
    return request({
        url: "/api/flowUp/physician",
        method: "post",
        data: data,
    })
}



export const updatePhysician = function (data) {
    return request({
        url: "/api/flowUp/physician/" + data.id,
        method: "put",
        data: data,
    })
}



export const getForEdit = function (id) {
    return request({
        url: "/api/flowUp/physician/" + id,
        method: "get",
    })
}

/**
 * 获取患者详情
 * @param {*} query
 * @returns
 */
export const getDetail = function (id) {
    return request({
        url: "/api/flowUp/physician/" + "detail/" + id,
        method: "get",
    })
}

/**
 * 删除患者
 * @param {*} id 
 */
export const deletePhysician = function (id) {
    return request({
        url: "/api/flowUp/physician/" + id,
        method: "delete",
    })
}