<template>
  <view>
    <u-time-line>
      <u-time-line-item
        nodeTop="2"
        class="u-p-b-20"
        v-for="(item, index) in progressList"
        :key="index"
      >
        <template v-slot:node>
          <view
            class="u-node"
            :style="{ background: getTimeLineTagColor(item.nodeStatus) }"
          ></view>
        </template>
        <template v-slot:content>
          <view class="u-font-24 content" @click="openApprover(item)">
            <view class="u-order-title u-flex u-m-b-8">
              <view
                class="u-line-1 name"
                v-if="item.nodeType === 'start' || item.nodeType === 'end'"
              >
                {{ item.nodeName }}
              </view>
              <view class="u-line-1 name" v-else>
                {{ item.nodeName + getCounterSignContent(item.counterSign) }}
              </view>
              <view class="u-m-l-10">
                <u-tag
                  :text="getNodeStatusContent(item.nodeStatus)"
                  mode="light"
                  size="mini"
                  shape="circle"
                  :type="getNodeStatusColor(item.nodeStatus)"
                  v-if="
                    item.nodeType !== 'end' &&
                    getNodeStatusContent(item.nodeStatus)
                  "
                />
                <text class="u-m-l-10">{{
                  item.startTime
                    ? $u.timeFormat(item.startTime, "mm-dd hh:MM")
                    : ""
                }}</text>
              </view>
            </view>
            <view class="u-flex avatar-box" v-if="item.nodeStatus == 1">
              <u-avatar
                :src="baseURL + taskInfo.headIcon"
                size="mini"
                mode="circle"
                class="avatar"
              ></u-avatar>
              <text class="u-m-l-8">发起人：{{ taskInfo.creatorUser }}</text>
            </view>
            <view
              class="u-flex approver-list"
              v-if="item.nodeStatus != 1 && item.approver?.length"
            >
              <view class="u-flex approver-list-l">
                <view
                  class="u-flex-col approver-list-l-box"
                  v-for="(child, i) in item.approver"
                  :key="i"
                >
                  <u-avatar
                    :src="baseURL + child.headIcon"
                    size="mini"
                    mode="circle"
                    class="avatar"
                  ></u-avatar>
                  <u-tag
                    :text="useDefine.getFlowStateContent(child.handleType)"
                    mode="light"
                    v-if="useDefine.getFlowStateContent(child.handleType)"
                    class="tag"
                    size="mini"
                    :border-color="
                      useDefine.getHexColor(
                        useDefine.getFlowStateColor(child.handleType)
                      )
                    "
                    :bg-color="
                      useDefine.getHexColor(
                        useDefine.getFlowStateColor(child.handleType)
                      )
                    "
                    color="#fff"
                  />
                  <text class="u-m-t-20 u-line-1 approver-user-name">
                    {{ child.userName }}
                  </text>
                </view>
              </view>
              <view class="u-m-l-20 approver-list-r u-flex">
                <view class="approver-list-r-box">{{
                  item.approverCount
                }}</view>
                <text class="icon-ym icon-ym-right u-m-r-12"></text>
              </view>
            </view>
          </view>
        </template>
      </u-time-line-item>
    </u-time-line>
  </view>
  <uni-popup
    ref="flowStepPopup"
    background-color="#fff"
    border-radius="8rpx 8rpx 0 0"
    :is-mask-click="false"
  >
    <view class="timeLine-popup-content u-flex-col">
      <view class="u-flex head-title">
        <text class="text">{{ popupTitle }}</text>
        <text class="text icon-ym icon-ym-fail" @click="popupClose"></text>
      </view>
      <view
        class="content"
        v-for="(item, index) in recordList"
        :key="index"
        v-if="recordList.length"
      >
        <view class="u-flex u-m-t-20 content-info">
          <view class="u-flex content-info-left">
            <u-avatar
              :src="baseURL + item.headIcon"
              size="mini"
              mode="circle"
              class="avatar"
            ></u-avatar>
            <view class="u-m-l-10 name-box">
              <p>{{ item.userName }}</p>
              <p class="name">
                {{
                  item.handleTime
                    ? $u.timeFormat(item.handleTime, "yyyy-mm-dd hh:MM:ss")
                    : ""
                }}
              </p>
            </view>
          </view>
          <u-tag
            :text="useDefine.getFlowStateContent(item.handleType)"
            :border-color="
              useDefine.getHexColor(
                useDefine.getFlowStateColor(item.handleType)
              )
            "
            :bg-color="
              useDefine.getHexColor(
                useDefine.getFlowStateColor(item.handleType)
              )
            "
            color="#fff"
            size="mini"
            shape="circle"
          />
          <view class="content-info-right u-line-1" v-if="item.handleUserName">
            <u-icon
              name="arrow-rightward"
              color="#1890ff"
              class="u-m-l-10 u-m-r-10"
            ></u-icon>
            <text class="u-font-24 txt u-line-1">{{
              item.handleUserName
            }}</text>
          </view>
        </view>
        <view
          class="content-info-bottom"
          v-if="item.signImg || item.fileList?.length || item.handleOpinion"
        >
          <text class="u-line-2" v-if="item.handleOpinion">{{
            item.handleOpinion
          }}</text>
          <fileList
            :fileList="item.fileList"
            v-if="item?.fileList?.length"
          ></fileList>
          <view class="u-flex sign-box" v-if="item.signImg">
            <view class="sign-title">签名：</view>
            <XundaSign v-model="item.signImg" align="left" detailed />
          </view>
        </view>
      </view>
      <NoData v-else paddingTop="0" backgroundColor="#fff"></NoData>
    </view>
  </uni-popup>
</template>
<script>
import { recordList } from "@/api/workFlow/flowBefore";
import fileList from "./fileList.vue";
import NoData from "@/components/noData";
import { useDefineSetting } from "@/utils/useDefineSetting";
export default {
  components: {
    fileList,
    NoData,
  },
  props: {
    progressList: {
      type: Array,
      default: () => [],
    },
    taskInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      useDefine: useDefineSetting(),
      recordList: [],
    };
  },
  computed: {
    baseURL() {
      return this.define.baseURL;
    },
  },
  methods: {
    getCounterSignContent(counterSign) {
      return counterSign == 0
        ? "(或签)"
        : counterSign == 1
        ? "(会签)"
        : "(依次审批)";
    },
    getNodeStatusColor(status) {
      return status == 1 || status == 2
        ? "success"
        : status == 3
        ? "error"
        : "primary";
    },
    getNodeStatusContent(status) {
      const list = [
        "",
        "已提交",
        "已通过",
        "已拒绝",
        "审批中",
        "已退回",
        "已撤回",
      ];
      return list[status] || "";
    },
    openApprover(item) {
      if (item.nodeType === "start" || item.nodeType === "end") return;
      this.popupTitle =
        item.nodeName + this.getCounterSignContent(item.counterSign);
      this.nodeId = item.nodeId;
      this.$nextTick(() => {
        this.$refs.flowStepPopup.open("bottom");
        this.getRecordList();
      });
    },
    getTimeLineTagColor(status) {
      return status == 1 || status == 2
        ? "#08AF28"
        : status != 3
        ? "#0177FF"
        : "#ed6f6f";
    },
    getRecordList() {
      let data = {
        taskId: this.taskInfo.id,
        nodeId: this.nodeId,
      };
      recordList(data).then((res) => {
        let list = res.data || [];
        list.map((o) => {
          o.fileList = JSON.parse(o.fileList);
        });
        this.recordList = list;
      });
    },
    popupClose() {
      this.$refs.flowStepPopup.close();
    },
  },
};
</script>

<style lang="scss">
.timeLine-popup-content {
  padding: 20rpx;
  height: 1200rpx;
  overflow-y: scroll;

  .head-title {
    justify-content: space-between;
    color: #333333;
    /* #ifdef APP-PLUS */
    padding: 20rpx 0;
    /* #endif */
  }

  .content {
    .content-info {
      height: 100rpx;

      .content-info-left {
        flex: 1;

        .name-box {
          .name {
            color: #606266;
          }
        }
      }

      .content-info-right {
        max-width: 228rpx;

        .txt {
          color: #303133;
        }
      }
    }

    .content-info-bottom {
      background-color: #f5f5f5;
      padding: 20rpx;
      border-radius: 8rpx;
      color: #303133;

      .sign-box {
        height: 88rpx;

        .sign-title {
          width: 120rpx;
        }
      }
    }
  }
}

.u-node {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
}

.active {
  background: #02a7f0;
}

.content {
  .u-order-title {
    .name {
      flex: 1;
    }
  }

  .avatar-box {
    ::v-deep .u-avatar {
      // width: 2rem !important;
      // height: 2rem !important;
    }
  }

  .approver-list {
    border-radius: 8rpx;
    height: 140rpx;
    background-color: #f5f5f5;

    .approver-list-l {
      flex: 1;

      .approver-list-l-box {
        width: 120rpx;
        align-items: center;
        position: relative;

        .tag {
          position: absolute;
          top: 50rpx;
        }

        .approver-user-name {
          color: #303133;
          width: 120rpx;
          text-align: center;
        }
      }
    }

    .approver-list-r {
      height: 4.375rem;

      .approver-list-r-box {
        width: 24px;
        height: 24px;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        background-color: #fff;
      }
    }
  }
}
</style>
