<template>
  <view class="item">
    <div>
      <span class="name">{{ motoinfo.name }}</span
      ><span class="type"
        >{{ motoinfo.mototype }}
        <span v-if="motoinfo.maxspeed"
          >({{ motoinfo.maxspeed }}km/h)</span
        ></span
      >
    </div>

    <u-row customStyle="margin-bottom: 15rpx">
      <u-col :span="1">
        <u-icon size="18" name="tags-fill" color="#5677fc"></u-icon>
      </u-col>
      <u-col :span="5">
        <view class="item-left">学院</view>
      </u-col>
      <u-col :span="6" textAlign="right">
        <view class="item-right snowy-bold snowy-ellipsis">
          {{ motoinfo.colleage }}
        </view>
      </u-col>
    </u-row>
    <u-row customStyle="margin-bottom: 15rpx">
      <u-col :span="1">
        <u-icon size="18" name="tags-fill" color="#5677fc"></u-icon>
      </u-col>
      <u-col :span="5">
        <view class="item-left">学号</view>
      </u-col>
      <u-col :span="6" textAlign="right">
        <view class="item-right snowy-bold snowy-ellipsis">
          {{ motoinfo.no }}
        </view>
      </u-col>
    </u-row>
    <u-row customStyle="margin-bottom: 15rpx">
      <u-col :span="1">
        <u-icon size="18" name="tags-fill" color="#5677fc"></u-icon>
      </u-col>
      <u-col :span="5">
        <view class="item-left">专业</view>
      </u-col>
      <u-col :span="6" textAlign="right">
        <view class="item-right snowy-bold snowy-ellipsis">
          {{ motoinfo.profess }}
        </view>
      </u-col>
    </u-row>
    <u-row customStyle="margin-bottom: 15rpx">
      <u-col :span="1">
        <u-icon size="18" name="tags-fill" color="#5677fc"></u-icon>
      </u-col>
      <u-col :span="5">
        <view class="item-left">联系方式</view>
      </u-col>
      <u-col :span="6" textAlign="right">
        <view class="item-right snowy-bold snowy-ellipsis">
          {{ motoinfo.phone }}
        </view>
      </u-col>
    </u-row>
  </view>
</template>
<script>
export default {
  props: {
    motoinfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      item: {},
    };
  },
};
</script>
<style lang="scss" scoped>
.item {
  background: #ffffff;
  margin: 20rpx 0;
  padding: 25rpx;
  box-shadow: 0 1px 2px #ccc;
  border-radius: 15rpx;

  .item-left {
    color: #999;
    font-size: 26rpx;
  }

  .item-right {
    font-size: 26rpx;
  }
  .name {
    font-size: 45rpx;
  }

  .type {
    color: white;
    background-color: #5677fc;
    padding: 5px;
    border-radius: 10px;
    float: right;
  }
}

.item:hover {
  box-shadow: 1upx 5upx 5upx #5677fc;
}
</style>
