<template>
  <view class="patient-list-container">
    <!-- 固定顶部筛选区域 -->
    <view class="filter-header-fixed">
      <view class="filter-trigger" @click="toggleFilter">
        <text class="filter-text">筛选条件</text>
        <text class="filter-arrow" :class="{ 'filter-arrow-up': showFilter }">▼</text>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view class="filter-panel" v-show="showFilter" @touchmove.stop.prevent>
      <!-- 遮罩层 -->
      <view class="filter-mask" @click="closeFilter"></view>

      <!-- 筛选内容 -->
      <view class="filter-content" @touchmove.stop>
        <view class="filter-form-container">
          <scroll-view scroll-y="true" class="filter-scroll">
            <u-form :label-width="150" class="filter-form">
              <view class="form-section">
                <view class="section-title">基本信息</view>
                <u-form-item label="姓名">
                  <u-input v-model="searchForm.name" placeholder="请输入患者姓名" class="uni-input" type="text"
                    :clearable="true" />
                </u-form-item>
                <u-form-item label="性别">
                  <picker :value="getSexIndex()" :range="getSexOptions()" range-key="fullName" @change="onSexChange">
                    <view class="uni-picker">
                      {{ getSexText() || "请选择性别" }}
                    </view>
                  </picker>
                </u-form-item>
                <u-form-item label="身份证">
                  <u-input v-model="searchForm.idCard" placeholder="请输入身份证号" class="uni-input" type="idcard"
                    :clearable="true" />
                </u-form-item>
              </view>

              <view class="form-section">
                <view class="section-title">住院信息</view>
                <u-form-item label="住院号">
                  <u-input v-model="searchForm.admissionNo" placeholder="请输入住院号" class="uni-input" type="text"
                    :clearable="true" />
                </u-form-item>
                <u-form-item label="出院日期">
                  <uni-datetime-picker type="date" v-model="searchForm.dischargeDate" />
                </u-form-item>
              </view>

              <view class="form-section">
                <view class="section-title">随访信息</view>
                <u-form-item label="随访类型">
                  <picker :value="getTypeIndex()" :range="getTypeOptions()" range-key="fullName" @change="onTypeChange">
                    <view class="uni-picker">
                      {{ getTypeText() || "请选择随访类型" }}
                    </view>
                  </picker>
                </u-form-item>
              </view>
            </u-form>
          </scroll-view>
        </view>

        <view class="filter-actions">
          <u-button class="action-btn reset-btn" @click="reset">
            重置
          </u-button>
          <u-button class="action-btn search-btn" type="primary" @click="search">
            检索
          </u-button>
        </view>
      </view>
    </view>
    <!-- 患者列表区域 -->
    <view class="patient-list-wrapper" :class="{ 'list-disabled': showFilter }">
      <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
        :fixed="false">
        <view class="patient-list-content">
          <uni-swipe-action ref="swipeAction">
            <uni-swipe-action-item v-for="(item, index) in list" :key="index" :threshold="0" :right-options="options"
              class="patient-item-wrapper">
              <view class="patient-card">
                <!-- 患者头像和基本信息 -->
                <view class="patient-header" @click="goDetail(item)">
                  <view class="patient-avatar">
                    <text class="avatar-text">{{
                      getAvatarText(item.name)
                    }}</text>
                  </view>
                  <view class="patient-basic-info">
                    <view class="patient-name">
                      <text class="name-text">{{ item.name }}</text>
                      <view class="age-badge">{{ item.age }}岁</view>
                    </view>
                    <view class="patient-id">
                      <text class="id-label">身份证:</text>
                      <text class="id-value">{{
                        formatIdCard(item.idCard)
                      }}</text>
                    </view>
                  </view>
                  <view class="follow-status" v-if="false">
                    <view class="status-badge" :class="getFollowStatusClass(item)">
                      {{ getFollowStatusText(item) }}
                    </view>
                  </view>
                </view>

                <!-- 住院信息 -->
                <view class="hospital-info">
                  <view class="info-row">
                    <view class="info-item">
                      <text class="info-label">住院号:</text>
                      <text class="info-value">{{
                        item.admissionNo || "暂无"
                      }}</text>
                    </view>
                    <view class="info-item" v-if="item.dischargeDate">
                      <text class="info-label">出院:</text>
                      <text class="info-value">{{
                        formatDate(item.dischargeDate)
                      }}</text>
                    </view>
                  </view>
                  <view class="info-row" v-if="item.addressDetail">
                    <view class="info-item address-item">
                      <XundaLocation v-model:modelValue="item.addressDetail" :disabled="true" :detailed="true"
                        :clearable="false" :enableLocationScope="true" :adjustmentScope="500"
                        :enableDesktopLocation="true" :locationScope="locationScope" @change="handleLocation">
                      </XundaLocation>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 滑动操作按钮 -->
              <template v-slot:right>
                <view class="swipe-actions">
                  <view class="action-btn follow-btn" @click.stop="startFollow(item)">
                    <text class="action-text">随访</text>
                  </view>
                  <view class="action-btn delete-btn" @click.stop="handleClick(index)">
                    <text class="action-text">删除</text>
                  </view>
                </view>
              </template>
            </uni-swipe-action-item>
          </uni-swipe-action>
        </view>
      </mescroll-uni>
    </view>
  </view>
</template>
<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getDictionaryDataSelector } from "@/api/common";
import { getList, del, columns } from "@/api/flow-up/patient";
export default {
  mixins: [MescrollMixin],
  components: {},
  data() {
    return {
      isAuthority: true,
      icon: resources.message.nodata,
      searchForm: {},
      showFilter: false,
      upOption: {
        page: {
          num: 0,
          size: 20,
        },
        empty: {
          use: true,
          icon: resources.message.nodata,
          tip: "暂无患者数据\n点击筛选条件查找患者",
          fixed: true,
          zIndex: 5,
        },
        textNoMore: "已显示全部患者",
        textLoading: "正在加载患者信息...",
        toTop: {
          src: "/static/image/to-top.png",
          bottom: 120,
          width: 72,
          radius: "50%",
        },
      },
      list: [],
      columns: columns,
      listQuery: {
        moduleId: "***********",
        sidx: "",
        keyword: "",
        json: "",
      },
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#dd524d",
          },
        },
      ],
      sortOptions: [
        {
          label: "主键降序",
          sidx: "id",
          value: "-id",
          sort: "desc",
        },
        {
          label: "主键升序",
          sidx: "id",
          value: "id",
          sort: "asc",
        },
        {
          label: "姓名降序",
          sidx: "name",
          value: "-name",
          sort: "desc",
        },
        {
          label: "姓名升序",
          sidx: "name",
          value: "name",
          sort: "asc",
        },

        {
          label: "年龄降序",
          sidx: "age",
          value: "-age",
          sort: "desc",
        },
        {
          label: "年龄升序",
          sidx: "age",
          value: "age",
          sort: "asc",
        },

        {
          label: "性别降序",
          sidx: "sex",
          value: "-sex",
          sort: "desc",
        },
        {
          label: "性别升序",
          sidx: "sex",
          value: "sex",
          sort: "asc",
        },

        {
          label: "身份证降序",
          sidx: "idCard",
          value: "-idCard",
          sort: "desc",
        },
        {
          label: "身份证升序",
          sidx: "idCard",
          value: "idCard",
          sort: "asc",
        },

        {
          label: "住院号降序",
          sidx: "admissionNo",
          value: "-admissionNo",
          sort: "desc",
        },
        {
          label: "住院号升序",
          sidx: "admissionNo",
          value: "admissionNo",
          sort: "asc",
        },

        {
          label: "详细地址降序",
          sidx: "addressDetail",
          value: "-addressDetail",
          sort: "desc",
        },
        {
          label: "详细地址升序",
          sidx: "addressDetail",
          value: "addressDetail",
          sort: "asc",
        },

        {
          label: "出院日期降序",
          sidx: "dischargeDate",
          value: "-dischargeDate",
          sort: "desc",
        },
        {
          label: "出院日期升序",
          sidx: "dischargeDate",
          value: "dischargeDate",
          sort: "asc",
        },

        {
          label: "入院日期降序",
          sidx: "admissionDate",
          value: "-admissionDate",
          sort: "desc",
        },
        {
          label: "入院日期升序",
          sidx: "admissionDate",
          value: "admissionDate",
          sort: "asc",
        },

        {
          label: "入院诊断降序",
          sidx: "admittingDiagnosis",
          value: "-admittingDiagnosis",
          sort: "desc",
        },
        {
          label: "入院诊断升序",
          sidx: "admittingDiagnosis",
          value: "admittingDiagnosis",
          sort: "asc",
        },

        {
          label: "管床医师降序",
          sidx: "pipeBedPhysician",
          value: "-pipeBedPhysician",
          sort: "desc",
        },
        {
          label: "管床医师升序",
          sidx: "pipeBedPhysician",
          value: "pipeBedPhysician",
          sort: "asc",
        },

        {
          label: "随访总人数降序",
          sidx: "tnumber",
          value: "-tnumber",
          sort: "desc",
        },
        {
          label: "随访总人数升序",
          sidx: "tnumber",
          value: "tnumber",
          sort: "asc",
        },

        {
          label: "随访应完成人数降序",
          sidx: "dnumber",
          value: "-dnumber",
          sort: "desc",
        },
        {
          label: "随访应完成人数升序",
          sidx: "dnumber",
          value: "dnumber",
          sort: "asc",
        },

        {
          label: "随访率降序",
          sidx: "rate",
          value: "-rate",
          sort: "desc",
        },
        {
          label: "随访率升序",
          sidx: "rate",
          value: "rate",
          sort: "asc",
        },

        {
          label: "随访次数降序",
          sidx: "fCount",
          value: "-fCount",
          sort: "desc",
        },
        {
          label: "随访次数升序",
          sidx: "fCount",
          value: "fCount",
          sort: "asc",
        },

        {
          label: "随访类型降序",
          sidx: "type",
          value: "-type",
          sort: "desc",
        },
        {
          label: "随访类型升序",
          sidx: "type",
          value: "type",
          sort: "asc",
        },

        {
          label: "地址降序",
          sidx: "address",
          value: "-address",
          sort: "desc",
        },
        {
          label: "地址升序",
          sidx: "address",
          value: "address",
          sort: "asc",
        },

        {
          label: "纬度降序",
          sidx: "longitude",
          value: "-longitude",
          sort: "desc",
        },
        {
          label: "纬度升序",
          sidx: "longitude",
          value: "longitude",
          sort: "asc",
        },

        {
          label: "经度降序",
          sidx: "latitude",
          value: "-latitude",
          sort: "desc",
        },
        {
          label: "经度升序",
          sidx: "latitude",
          value: "latitude",
          sort: "asc",
        },
      ],
      ableAll: {},
      menuId: "",
      columnList: [],
      dataValue: {},
      userInfo: {},
      firstInitSearchData: false,
      tabList: [],
      tabKey: 0,
      optionsObj: {
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
      },
    };
  },
  onLoad(e) {
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.menuId = e.menuId;
    this.setDefaultQuery();
    this.dataAll();
    this.getColumnList();
  },
  onShow() {
    this.$nextTick(() => {
      this.mescroll.resetUpScroll();
    });
  },
  onUnload() {
    uni.$off("refresh");
  },
  methods: {
    getSelectOptions() {
      getDictionaryDataSelector("Sex").then((res) => {
        this.optionsObj.SexOptions = res.data.list;
      });
      getDictionaryDataSelector("FlowType").then((res) => {
        this.optionsObj.FlowTypeOptions = res.data.list;
      });
    },

    // 性别选择器相关方法
    getSexOptions() {
      return this.optionsObj.SexOptions || [];
    },
    getSexIndex() {
      if (!this.searchForm.sex) return 0;
      const options = this.getSexOptions();
      const index = options.findIndex(
        (item) => item.enCode === this.searchForm.sex
      );
      return index >= 0 ? index : 0;
    },
    getSexText() {
      if (!this.searchForm.sex) return "";
      const options = this.getSexOptions();
      const item = options.find((item) => item.enCode === this.searchForm.sex);
      return item ? item.fullName : "";
    },
    onSexChange(e) {
      const index = e.detail.value;
      const options = this.getSexOptions();
      if (options[index]) {
        this.searchForm.sex = options[index].enCode;
      }
    },

    // 随访类型选择器相关方法
    getTypeOptions() {
      return this.optionsObj.FlowTypeOptions || [];
    },
    getTypeIndex() {
      if (!this.searchForm.type) return 0;
      const options = this.getTypeOptions();
      const index = options.findIndex(
        (item) => item.enCode === this.searchForm.type
      );
      return index >= 0 ? index : 0;
    },
    getTypeText() {
      if (!this.searchForm.type) return "";
      const options = this.getTypeOptions();
      const item = options.find((item) => item.enCode === this.searchForm.type);
      return item ? item.fullName : "";
    },
    onTypeChange(e) {
      const index = e.detail.value;
      const options = this.getTypeOptions();
      if (options[index]) {
        this.searchForm.type = options[index].enCode;
      }
    },
    dataAll() {
      this.getSelectOptions();
    },
    // 切换筛选面板显示状态
    toggleFilter() {
      this.showFilter = !this.showFilter;
    },

    // 关闭筛选面板
    closeFilter() {
      this.showFilter = false;
    },
    //设置默认排序
    setDefaultQuery() {
      const defaultSortConfig = [];
      const sortField = defaultSortConfig.map(
        (o) => (o.sort === "desc" ? "-" : "") + o.field
      );
      this.listQuery.sidx = sortField.join(",");
    },
    //初始化查询的默认数据
    async initSearchData() {
      this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
    },
    // 上拉刷新
    async upCallback(page) {
      if (!this.firstInitSearchData) {
        await this.initSearchData();
        this.firstInitSearchData = true;
      }
      const query = {
        currentPage: page.num,
        pageSize: page.size,
        menuId: this.menuId,
        ...this.listQuery,
        ...this.searchForm,
        dataType: 0,
        patientFlag: true,
      };
      getList(query)
        .then((res) => {
          let _list = res.data.list;
          this.mescroll.endSuccess(_list.length);
          if (page.num == 1) this.list = [];
          // const list = _list.map((o) => ({
          //   show: false,
          //   ...o,
          // }));
          this.list = this.list.concat(_list);
        })
        .catch(() => {
          this.mescroll.endSuccess(this.list.length);
        });
    },
    handleClick(index) {
      const item = this.list[index];
      uni.showModal({
        title: "提示",
        content: "确定删除?",
        success: (res) => {
          if (res.confirm) {
            this.delete(item);
          }
        },
      });
    },
    // 删除
    delete(item) {
      del(item.id).then((res) => {
        uni.showToast({
          title: res.msg,
          complete: () => {
            this.$u.toast(res.msg);
            this.mescroll.resetUpScroll();
          },
        });
      });
    },
    open(index) {
      this.list[index].show = true;
      this.list.forEach((_, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    search() {
      // 关闭筛选面板
      this.showFilter = false;

      if (this.isPreview == "1") return;
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      }, 300);
    },
    // 跳转详情页
    goDetail(item) {
      let id = item.id;
      // let btnType = "";
      let btnList = [];
      btnList.push("btn_edit");
      btnList.push("btn_detail");
      if (btnList.length == 0) return;
      this.jumPage(id, btnList);
    },
    // 跳转添加页
    addPage() {
      this.jumPage();
    },
    jumPage(id, btnList) {
      let idVal = id ? "&id=" + id : "";
      let idList = [];
      for (let i = 0; i < this.list.length; i++) {
        idList.push(this.list[i].id);
      }
      let idListVal = "&idList=" + idList;
      if (btnList.includes("btn_detail")) {
        uni.navigateTo({
          url:
            "/pages/flow-up/patient/detail?menuId=" +
            this.menuId +
            "&btnList=" +
            btnList +
            idVal +
            idListVal,
        });
      }
    },
    // 权限列
    getColumnList() {
      let columnPermissionList = [];
      let _appColumnList = this.columns;
      for (let i = 0; i < _appColumnList.length; i++) {
        columnPermissionList.push(_appColumnList[i]);
      }
      this.columnList = columnPermissionList;
    },
    // 重置查询条件
    reset() {
      this.searchForm = {};
      // 重置后自动执行搜索
      // this.search();
    },
    // 查询检索（保留兼容性）
    closeDropdown() {
      this.search();
    },
    dataList(data) {
      let _list = data.list;
      return _list;
    },

    // 获取头像文字
    getAvatarText(name) {
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return "暂无";
      return idCard;

      if (idCard.length <= 8) return idCard;
      return idCard.slice(0, 4) + "****" + idCard.slice(-4);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "暂无";
      const date = new Date(dateStr);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    },

    // 获取随访状态
    getFollowStatusText(item) {
      if (item.fCount > 0) {
        return "已随访";
      }
      return "待随访";
    },

    // 获取随访状态样式
    getFollowStatusClass(item) {
      if (item.fCount > 0) {
        return "status-completed";
      }
      return "status-pending";
    },

    // 获取随访类型文本
    getFollowTypeText(type) {
      // 这里可以根据实际的类型映射来处理
      return type || "常规随访";
    },

    // 开始随访
    startFollow(item) {
      uni.showModal({
        title: "随访确认",
        content: `确定要对患者 ${item.name} 进行随访吗？`,
        success: (res) => {
          if (res.confirm) {
            // 这里可以跳转到随访页面或执行随访逻辑
            uni.showToast({
              title: "开始随访",
              icon: "success",
            });
          }
        },
      });
    },
  },
};
</script>

<style lang="scss">
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.patient-list-container {
  min-height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 固定顶部筛选区域样式
.filter-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
  z-index: 100;
  border-bottom: 1rpx solid #e9ecef;

  .filter-trigger {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16rpx;
    border: 2rpx solid #dee2e6;
    cursor: pointer;
    transition: all 0.3s ease;

    .filter-text {
      font-size: 30rpx;
      color: #495057;
      font-weight: 600;
    }

    .filter-arrow {
      font-size: 24rpx;
      color: #6c757d;
      transition: transform 0.3s ease;

      &.filter-arrow-up {
        transform: rotate(180deg);
      }
    }

    &:active {
      background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      transform: scale(0.98);
    }
  }
}

// 筛选面板样式
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;

  .filter-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
  }

  .filter-content {
    position: absolute;
    top: 0rpx;
    /* 从固定头部下方开始 */
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);

    .filter-form-container {
      flex: 1;
      overflow: hidden;

      .filter-scroll {
        height: 100%;
        padding: 24rpx 24rpx;

        .filter-form {
          margin-right: 24rpx;

          .form-section {
            margin-bottom: 3rpx;

            .section-title {
              font-size: 28rpx;
              font-weight: 600;
              color: #34495e;
              margin-bottom: 20rpx;
              padding: 12rpx 16rpx;
              border-left: 6rpx solid #3498db;
              background: #f8f9fa;
              border-radius: 8rpx;
            }
          }
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 20rpx;
      padding: 24rpx;
      background: #f8f9fa;
      border-top: 1rpx solid #e9ecef;

      .action-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 40rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;

        &.reset-btn {
          background: #f8f9fa;
          border: 2rpx solid #dee2e6;
          color: #6c757d;
        }

        &.search-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: #fff;
        }
      }
    }
  }
}

// 患者列表样式
.patient-list-wrapper {
  flex: 1;
  padding: 160rpx 24rpx 24rpx;
  /* 顶部留出固定头部的空间 */
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;

  &.list-disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  .patient-list-content {
    .patient-item-wrapper {
      margin-bottom: 32rpx;
      /* 增加患者卡片间距 */
      border-radius: 24rpx;
      /* 更大的圆角 */
      overflow: hidden;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
      background: #fff;
      transition: all 0.3s ease;

      /* 添加渐变边框效果 */
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6rpx;
        background: linear-gradient(90deg,
            #667eea 0%,
            #764ba2 50%,
            #f093fb 100%);
        border-radius: 24rpx 24rpx 0 0;
      }

      &:hover {
        transform: translateY(-8rpx);
        box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 患者卡片样式
.patient-card {
  background: #fff;
  padding: 32rpx 28rpx;
  /* 增加内边距 */
  position: relative;
  z-index: 2;
  margin-top: 6rpx;
  /* 为渐变边框留出空间 */

  .patient-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 28rpx;
    /* 增加间距 */

    .patient-avatar {
      position: relative;
      width: 96rpx;
      /* 增大头像 */
      height: 96rpx;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      /* 增加间距 */
      box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
      /* 添加阴影 */

      .avatar-text {
        color: #fff;
        font-size: 32rpx;
        /* 增大字体 */
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        /* 添加文字阴影 */
      }
    }

    .patient-basic-info {
      flex: 1;
      min-width: 0;

      .patient-name {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        /* 增加间距 */

        .name-text {
          font-size: 36rpx;
          /* 增大字体 */
          font-weight: 700;
          color: #2c3e50;
          margin-right: 16rpx;
          /* 增加间距 */
        }

        .age-badge {
          background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
          color: #fff;
          padding: 6rpx 16rpx;
          /* 增加内边距 */
          border-radius: 16rpx;
          /* 更大圆角 */
          font-size: 24rpx;
          /* 增大字体 */
          font-weight: 600;
          box-shadow: 0 4rpx 12rpx rgba(243, 156, 18, 0.3);
          /* 添加阴影 */
        }
      }

      .patient-id {
        display: flex;
        align-items: center;

        .id-label {
          font-size: 26rpx;
          /* 增大字体 */
          color: #7f8c8d;
          margin-right: 10rpx;
          /* 增加间距 */
        }

        .id-value {
          font-size: 26rpx;
          /* 增大字体 */
          color: #34495e;
          font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono",
            Consolas, "Courier New", monospace;
          font-weight: 500;
        }
      }
    }

    .follow-status {
      .status-badge {
        padding: 10rpx 20rpx;
        /* 增加内边距 */
        border-radius: 24rpx;
        /* 更大圆角 */
        font-size: 24rpx;
        /* 增大字体 */
        font-weight: 600;
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
        /* 添加阴影 */

        &.status-completed {
          background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
          color: #155724;
          border: 2rpx solid #b8dacc;
        }

        &.status-pending {
          background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
          color: #856404;
          border: 2rpx solid #f1c40f;
        }
      }
    }
  }

  // 住院信息样式
  .hospital-info {
    margin-bottom: 24rpx;
    /* 增加间距 */
    padding: 20rpx;
    /* 添加内边距 */
    background: #f8f9fa;
    /* 添加背景色 */
    border-radius: 16rpx;
    /* 添加圆角 */
    border: 1rpx solid #e9ecef;
    /* 添加边框 */

    .info-row {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      /* 增加间距 */
      margin-bottom: 16rpx;
      /* 增加间距 */

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
        padding: 8rpx 12rpx;
        /* 添加内边距 */
        background: #fff;
        /* 添加背景色 */
        border-radius: 12rpx;
        /* 添加圆角 */
        border: 1rpx solid #dee2e6;
        /* 添加边框 */

        &.address-item {
          flex: 100%;
        }

        .info-label {
          font-size: 26rpx;
          /* 增大字体 */
          color: #6c757d;
          margin-right: 12rpx;
          /* 增加间距 */
          white-space: nowrap;
          font-weight: 600;
        }

        .info-value {
          font-size: 26rpx;
          /* 增大字体 */
          color: #495057;
          flex: 1;
          min-width: 0;
          font-weight: 500;

          &.address-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  // 随访信息样式
  .follow-info {
    border-top: 2rpx solid #e9ecef;
    /* 加粗分割线 */
    padding-top: 20rpx;
    /* 增加间距 */
    margin-top: 8rpx;
    /* 添加上边距 */

    .follow-stats {
      display: flex;
      gap: 32rpx;
      /* 增加间距 */

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        padding: 16rpx 12rpx;
        /* 添加内边距 */
        background: linear-gradient(135deg,
            #f8f9fa 0%,
            #e9ecef 100%);
        /* 添加背景渐变 */
        border-radius: 16rpx;
        /* 添加圆角 */
        border: 1rpx solid #dee2e6;
        /* 添加边框 */
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
        /* 添加阴影 */

        .stat-number {
          font-size: 32rpx;
          /* 增大字体 */
          font-weight: 700;
          color: #3498db;
          margin-bottom: 8rpx;
          /* 增加间距 */
          text-shadow: 0 2rpx 4rpx rgba(52, 152, 219, 0.2);
          /* 添加文字阴影 */
        }

        .stat-label {
          font-size: 24rpx;
          /* 增大字体 */
          color: #6c757d;
          text-align: center;
          font-weight: 500;
        }
      }
    }
  }
}

// 滑动操作按钮样式
.swipe-actions {
  display: flex;
  height: 100%;

  .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 140rpx;
    /* 增加宽度 */
    color: #fff;
    font-weight: 600;
    position: relative;
    overflow: hidden;

    .action-text {
      font-size: 26rpx;
      /* 增大字体 */
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
      /* 添加文字阴影 */
    }

    &.follow-btn {
      background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
      box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
      /* 添加内阴影 */

      &:active {
        background: linear-gradient(135deg, #229954 0%, #27ae60 100%);
      }
    }

    &.delete-btn {
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      box-shadow: inset 0 2rpx 4rpx rgba(255, 255, 255, 0.2);
      /* 添加内阴影 */

      &:active {
        background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .filter-header-fixed {
    padding: 16rpx;

    .filter-trigger {
      padding: 16rpx 20rpx;

      .filter-text {
        font-size: 28rpx;
      }

      .filter-arrow {
        font-size: 22rpx;
      }
    }
  }

  .patient-list-wrapper {
    padding: 140rpx 16rpx 16rpx;
    /* 调整移动端的顶部间距 */

    .patient-list-content {
      .patient-item-wrapper {
        margin-bottom: 24rpx;
        /* 减少移动端间距 */
      }
    }

    .patient-card {
      padding: 24rpx 20rpx;
      /* 减少移动端内边距 */

      .patient-header {
        .patient-avatar {
          width: 80rpx;
          /* 移动端稍小的头像 */
          height: 80rpx;

          .avatar-text {
            font-size: 28rpx;
          }
        }

        .patient-basic-info {
          .patient-name {
            .name-text {
              font-size: 32rpx;
            }

            .age-badge {
              font-size: 22rpx;
              padding: 4rpx 12rpx;
            }
          }

          .patient-id {

            .id-label,
            .id-value {
              font-size: 24rpx;
            }
          }
        }
      }

      .hospital-info {
        padding: 16rpx;

        .info-row {
          gap: 16rpx;

          .info-item {

            .info-label,
            .info-value {
              font-size: 24rpx;
            }
          }
        }
      }

      .follow-info {
        .follow-stats {
          gap: 20rpx;

          .stat-item {
            padding: 12rpx 8rpx;

            .stat-number {
              font-size: 28rpx;
            }

            .stat-label {
              font-size: 22rpx;
            }
          }
        }
      }
    }
  }

  .swipe-actions {
    .action-btn {
      width: 120rpx;

      .action-text {
        font-size: 24rpx;
      }
    }
  }
}

// 自定义下拉刷新样式
::v-deep .mescroll-downwarp {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border-radius: 0 0 20rpx 20rpx;

  .mescroll-downwarp-content {
    font-size: 28rpx;
    font-weight: 500;
  }
}

// 自定义上拉加载样式
::v-deep .mescroll-upwarp {
  .mescroll-upwarp-tip {
    color: #7f8c8d;
    font-size: 26rpx;
  }
}

// 确保患者列表区域可以正常点击
.patient-list-wrapper {
  pointer-events: auto;

  .patient-card {
    pointer-events: auto;
    cursor: pointer;
  }
}
</style>
