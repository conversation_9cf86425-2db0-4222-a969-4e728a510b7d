<template>
  <view class="patient-index-container">
    <!-- 医师信息区域 -->
    <view class="physician-section">
      <view class="physician-card flowup-card" v-if="myPhysician && !isLoading" @click="navigateToDoctor">
        <view class="card-header">
          <text class="header-title">我的管床医师</text>
        </view>
        <view class="physician-content">
          <view class="physician-info">
            <view class="physician-name">{{ myPhysician.name }}</view>
            <view class="physician-meta">
              <view class="meta-item" v-if="myPhysician.department">
                <text class="meta-label">科室:</text>
                <text class="meta-value">{{ myPhysician.department }}</text>
              </view>
              <view class="meta-item" v-if="myPhysician.title">
                <text class="meta-label">职称:</text>
                <text class="meta-value">{{ myPhysician.title }}</text>
              </view>
            </view>
          </view>
          <view class="avatar-wrapper">
            <image class="avatar-img" :src="avatar" mode="aspectFill"></image>
            <view class="avatar-ring"></view>
          </view>
        </view>
      </view>

      <view class="physician-card flowup-card" v-else-if="!isLoading">
        <view class="card-header">
          <text class="header-title">绑定管床医师</text>
        </view>
        <view class="bind-content">
          <view class="bind-tip">您尚未绑定管床医师</view>
          <view class="bind-button flowup-action-buttons" @click="scanCodeToBind">
            <view class="action-btn primary-btn">
              <view class="btn-icon">📷</view>
              <text class="btn-text">扫码绑定</text>
            </view>
          </view>
        </view>
      </view>

      <view class="loading-placeholder" v-else>
        <u-loading-icon mode="circle" size="28"></u-loading-icon>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 功能网格 -->
    <view class="grid-section">
      <view class="section-header">
        <text class="section-title">功能中心</text>
      </view>
      <view class="grid-container">
        <view class="grid-item flowup-card" v-for="item in gridItems" :key="item.id" @click="navigateTo(item.pagePath)">
          <view class="grid-icon-wrapper">
            <image :src="item.iconPath" class="grid-icon"></image>
          </view>
          <text class="grid-text">{{ item.text }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
var wv; //计划创建的webview
import logoImg from "@/static/logo.png";
import { getMyPhysicianAsync, scanBindAsync } from "@/api/flow-up/physician";
// #ifndef MP
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
// #endif

export default {
  // #ifndef MP
  mixins: [MescrollMixin, IndexMixin],
  // #endif
  components: {},
  expose: ["loadMyInfo"], // 暴露方法给父组件
  data() {
    return {
      logoImg,
      gridItems: [],
      myPhysician: null,
      avatar: "",
      isLoading: true,
    };
  },
  methods: {
    navigateTo(pagePath) {
      uni.navigateTo({
        url: pagePath,
      });
    },
    navigateToDoctor() {
      const params = {
        // 在这里添加你需要的参数
      };
      // 将参数转换为查询字符串
      const queryString = Object.keys(params)
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
        )
        .join("&");
      const pagePath = "/pages/flow-up/myPhysician";
      // 如果有参数，添加到URL后面
      const url = queryString ? `${pagePath}?${queryString}` : pagePath;
      // 跳转到医师界面
      uni.navigateTo({
        url: url,
      });
    },
    loadMyInfo() {
      var data = this;
      getMyPhysicianAsync().then((res) => {
        data.myPhysician = res.data;
        if (res.data && res.data.picture) {
          this.avatar = this.define.baseURL + res.data.picture;
        } else {
          this.avatar =
            this.define.baseURL + "/api/file/Image/userAvatar/001.png";
        }
      });
    },
    scanCodeToBind() {
      uni.showLoading({
        title: "扫码中",
        mask: true,
      });
      uni.scanCode({
        success: (res) => {
          // 解析二维码内容，假设格式为：bindPhysician:physicianId
          if (res.result) {
            const result = JSON.parse(res.result.trim());
            let url = "";
            if (result.t === "bindPhysician") {
              url = "/pages/flow-up/physician/bindPhysician?id=" + result.id;
            } else {
              url = "/pages/my/scanResult/index?result=" + res.result;
            }

            uni.navigateTo({
              url,
              fail: (err) => {
                this.$u.toast("暂无此页面");
              },
            });
          } else {
            uni.showToast({
              title: "无效的绑定码",
              icon: "none",
            });
          }
          uni.hideLoading();
        },
        fail: (err) => {
          uni.hideLoading();
          uni.showToast({
            title: "扫码失败",
            icon: "none",
          });
        },
      });
    },
    bindPhysician(qrCode) {
      // 调用绑定医师的API
      uni.showLoading({
        title: "绑定中",
        mask: true,
      });
      scanBindAsync({ qrCode }).then((res) => {
        if (res.code === 200) {
          uni.showToast({
            title: "绑定成功",
            icon: "success",
          });
          // 重新加载医师信息
          this.loadMyInfo();
        } else {
          uni.showToast({
            title: res.data.message || "绑定失败",
            icon: "none",
          });
        }
        uni.hideLoading();
      });
    },
  },
  created() {
    this.gridItems = [
      {
        id: 1,
        text: "我的随访",
        iconPath: "/static/image/flow-up/medical-visit.png",
        pagePath: "/pages/flow-up/myVisitRecord?type=patient"
      },
      {
        id: 2,
        text: "我的问卷",
        iconPath: "/static/image/flow-up/medical-form.png",
        pagePath:
          "/pages/flow-up/questionnaireRecord/myQuestionnaireRecord?type=patient",
      },
    ];
    this.isLoading = true;
    setTimeout(() => {
      this.loadMyInfo();
      this.isLoading = false;
    }, 800);
  },
  computed: {},
};
</script>

<style lang="scss">
page {
  background: linear-gradient(180deg, #f0f6ff 0%, #ffffff 100%);
  min-height: 100vh;
}

.patient-index-container {
  min-height: 100vh;
  padding: 24rpx;
}

// 医师信息区域
.physician-section {
  margin-bottom: 32rpx;

  .physician-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-radius: 24rpx;
    padding: 0;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.15);
    border: 1rpx solid rgba(25, 118, 210, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 8rpx;
      background: linear-gradient(90deg, #1976d2 0%, #1565c0 50%, #bbdefb 100%);
      border-radius: 24rpx 24rpx 0 0;
    }

    .card-header {
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      padding: 24rpx 32rpx;

      .header-title {
        color: #fff;
        font-size: 32rpx;
        font-weight: 600;
      }
    }

    .physician-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;

      .physician-info {
        flex: 1;

        .physician-name {
          font-size: 40rpx;
          font-weight: 700;
          color: #1565c0;
          margin-bottom: 24rpx;
        }

        .physician-meta {
          .meta-item {
            display: flex;
            margin-bottom: 16rpx;

            &:last-child {
              margin-bottom: 0;
            }

            .meta-label {
              font-size: 28rpx;
              color: #6c757d;
              margin-right: 16rpx;
              font-weight: 500;
            }

            .meta-value {
              font-size: 28rpx;
              color: #2c3e50;
              font-weight: 600;
            }
          }
        }
      }

      .avatar-wrapper {
        position: relative;
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        padding: 6rpx;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);
        transition: transform 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .avatar-img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
    }

    .bind-content {
      padding: 40rpx 32rpx;
      text-align: center;

      .bind-tip {
        font-size: 32rpx;
        color: #6c757d;
        margin-bottom: 32rpx;
        font-weight: 500;
      }

      .bind-button {
        .action-btn {
          max-width: 300rpx;
          margin: 0 auto;
        }
      }
    }
  }

  .loading-placeholder {
    background: #fff;
    border-radius: 24rpx;
    padding: 60rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);

    .loading-text {
      margin-top: 24rpx;
      font-size: 28rpx;
      color: #6c757d;
    }
  }
}

// 功能网格区域
.grid-section {
  .section-header {
    margin-bottom: 24rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #2c3e50;
      padding-left: 16rpx;
      border-left: 8rpx solid #1976d2;
    }
  }

  .grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;

    .grid-item {
      background: #fff;
      border-radius: 24rpx;
      padding: 40rpx 24rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
      border: 1rpx solid rgba(25, 118, 210, 0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6rpx;
        background: linear-gradient(90deg, #1976d2 0%, #1565c0 50%, #bbdefb 100%);
        border-radius: 24rpx 24rpx 0 0;
      }

      &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.1);
      }

      .grid-icon-wrapper {
        width: 100rpx;
        height: 100rpx;
        border-radius: 20rpx;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24rpx;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.15);

        .grid-icon {
          width: 60rpx;
          height: 60rpx;
        }
      }

      .grid-text {
        font-size: 32rpx;
        font-weight: 600;
        color: #2c3e50;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .patient-index-container {
    padding: 16rpx;
  }

  .physician-section {
    margin-bottom: 24rpx;

    .physician-card {
      .card-header {
        padding: 20rpx 24rpx;

        .header-title {
          font-size: 28rpx;
        }
      }

      .physician-content {
        padding: 24rpx;

        .physician-info {
          .physician-name {
            font-size: 36rpx;
            margin-bottom: 16rpx;
          }

          .physician-meta {
            .meta-item {
              margin-bottom: 12rpx;

              .meta-label,
              .meta-value {
                font-size: 24rpx;
              }
            }
          }
        }

        .avatar-wrapper {
          width: 120rpx;
          height: 120rpx;
        }
      }

      .bind-content {
        padding: 32rpx 24rpx;

        .bind-tip {
          font-size: 28rpx;
          margin-bottom: 24rpx;
        }
      }
    }
  }

  .grid-section {
    .section-header {
      margin-bottom: 16rpx;

      .section-title {
        font-size: 28rpx;
      }
    }

    .grid-container {
      gap: 16rpx;

      .grid-item {
        padding: 32rpx 20rpx;

        .grid-icon-wrapper {
          width: 80rpx;
          height: 80rpx;
          margin-bottom: 16rpx;

          .grid-icon {
            width: 48rpx;
            height: 48rpx;
          }
        }

        .grid-text {
          font-size: 28rpx;
        }
      }
    }
  }
}
</style>
