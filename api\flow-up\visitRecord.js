import request from '@/utils/request'


export const visitRecordAppApi = '/api/app/flowUp/visitRecord'
export const visitRecordApi = '/api/flowUp/visitRecord'
// 获取应用菜单
export function getAppList(query) {
    return request({
        url: visitRecordAppApi + `/getAppList`,
        method: "post",
        data: query,
    })
}



export const createVisitRecord = function (data) {
    return request({
        url: visitRecordAppApi,
        method: "post",
        data: data,
    })
}

export const getForEdit = function (id) {
    return request({
        url: visitRecordApi + "/" + id,
        method: "get",
    })
}


export const updateVisitRecord = function (data) {
    return request({
        url: visitRecordApi + "/" + data.id,
        method: "put",
        data: data,
    })
}

