## 3.0.0（2025-07-17）
纯API式调用选择器，支持pdf、doc、zip...等所有类型文件
## 2.6.6（2025-06-04）
更新示例项目
## 2.6.5（2025-06-04）
屏蔽h5试用提示弹窗
## 2.6.4（2024-12-24）
1.修复App端position设置为absolute时定位不准确问题；
2.增加属性：rangeScroll 是否在局部scroll内使用，超出隐藏（用于解决scroll外有其他内容会被透明层覆盖问题）（使用官方api监听的，测试时发现若快速来回滚动偶现未触发监听回调，暂时无解）

示例项目更新内容：
1.增加【基本使用示例、scroll内使用示例、popup弹框内使用示例、nvue使用示例】等页面。

## 2.6.3（2024-12-08）
【优化】解决权限检测时弹出toast华为市场审核不通过的问题，已将原2秒自动消失toast提示框改为与权限按钮同步显示与关闭；同时组件props=>permission增加height属性，为当前权限弹框高度，默认两行文字，可根据实际提示内容自行修改。
## 2.6.2（2024-12-06）
【修复】检查到cli创建的 Vue3项目@change事件与Vue冲突导致额外触发一次且files传入为null的问题，现将@change回调更名为@changeFile
## 2.6.1（2024-12-03）
提示：试用版或示例项目仅提供H5端，如需App端，请联系我。
## 2.6.0（2024-12-03）
优化：toBase功能调整：App端toBase传true时上传base64格式，但返回页面为原file(因为app端跨浏览器base64太长会不完整)，H5端不变。
提示：试用版或示例项目仅提供H5端演示，如需App端，请联系我。
## 2.5.3（2024-12-03）
优化：toBase功能调整：App端toBase传true时上传base64格式，但返回页面为原file(因为app端跨浏览器base64太长会不完整)，H5端不变。
提示：试用版或示例项目仅提供H5端演示，如需App端，请联系我。
## 2.5.2（2024-12-03）
优化：toBase功能调整：App端toBase传true时上传base64格式，但返回页面为原file(因为app端跨浏览器base64太长会不完整)，H5端不变。
提示：试用版或示例项目仅提供H5端演示，如需App端，请联系我。

## 2.5.1（2024-11-27）
删除默认的录音权限校验请求，若需要录音权限或其他权限校验可通过props.permission设置
## 2.4.9（2024-09-02）
优化：回调函数增加返回childId字段
## 2.4.8（2024-08-31）
修复小程序编译报错问题
## 2.4.7（2024-08-31）
修复小程序编译报错问题
## 2.4.6（2024-08-06）
部分设备兼容问题处理
## 2.4.5（2024-08-01）
修复重复文件重命名时跳过了文件(1)直接从(2)开始的问题
## 2.4.4（2024-07-31）
【问题修复】修复不去重文件时重名文件只能选择2个的问题
## 2.4.3（2024-07-29）
问题修复
## 2.4.2（2024-07-17）
已知问题修复
## 2.4.1（2024-07-17）
已知问题修复
## 2.4.0（2024-07-16）
【老版本Android系统兼容问题修复】修复2.3.7添加内置权限校验后部分老安卓系统不兼容input.click时未能正常弹出文件选择界面的问题，处理方式-- 检测到若不兼容input.click则使用默认input file，此时若需要检测权限请自己在页面添加（当然，上华为商店审核时应该不存在老安卓系统问题）
## 2.3.9（2024-07-16）
移除测试代码（老是忘记，汗！！）
## 2.3.8（2024-07-16）
【优化】组件增加属性：toBase 文件是否转base64，使用和注意事项查看下方js示例注释说明。
## 2.3.7（2024-07-04）
已知问题修复
## 2.3.6（2024-07-04）
已知问题修复
## 2.3.5（2024-07-03）
【优化】优化权限校验，增加权限校验时顶部弹框
## 2.3.4（2024-06-27）
【兼容优化】修复部分老安卓系统设备设置不去重时不兼容问题
## 2.3.3（2024-06-26）
【新增】APP端内置权限校验。目前仅在OPPO FINDX机型测试验证，若其他机型有问题可加群反馈，或找我拿v2.3.2稳定版源码。
## 2.3.2（2024-06-13）
问题修复：2.3.1版本引起的部分设备不支持findLastIndex问题
## 2.3.1（2024-05-20）
修复：文件不去重时返回文件列表name与组件内置列表key不一致问题。
## 2.3.0（2024-05-20）
优化：1：增加属性distinct【选择文件是否去重】、2：对show/hide函数增加uni.$emit事件监听，若页面存在多个上传组件时，可通过uni.$emit控制所有上传组件webview透明层是否显示。
## 2.2.9（2023-06-01）
优化：将是否多选与count字段解绑(原逻辑是count>1为允许多选)，改为新增multiple属性控制是否多选。
## 2.2.8（2023-06-01）
修复上版本提交时accept测试值未删除导致h5端只能选择图片的问题。
## 2.2.7（2023-05-06）
应群友建议，当instantly为true时，触发change事件后延迟1000毫秒再自动上传，方便动态修改参数，其实个人还是建议想在change事件动态设置参数的伙伴将instantly设置为false,修改参数后手动调用upload()
## 2.2.6（2023-02-09）
修复多个文件同时选择时返回多次change回调的问题
## 2.2.5（2022-12-27）
1.修复多选文件时未能正常校验数量的问题；
2.app端与H5端支持单选或多选文件，通过count数量控制，超过1开启多选。
## 2.2.4（2022-12-27）
1.修复多选文件时未能正常校验数量的问题；
2.app端修复多选只取到第一个文件的问题。
## 2.2.3（2022-12-06）
修复手动调用show()导致count失效的问题
## 2.2.2（2022-12-01）
Vue3自行修改兼容
## 2.2.1（2022-10-19）
修复childId警告提示
## 2.2.0（2022-10-10）
更新app端webview窗口参数clidId，默认值添加时间戳保证唯一性
## 2.1.9（2022-07-13）
[修复] app端选择文件后初始化设置的文件列表被清空问题
## 2.1.8（2022-07-13）
[新增] ref方法初始化文件列表，用于已提交后再次编辑时需带入已上传文件：setFiles(files)，可传入数组或Map对象，传入格式请与组件选择返回格式保持一致，且name为必须属性。
## 2.1.7（2022-07-12）
修复ios端偶现创建webview初始化参数未生效的问题
## 2.1.6（2022-07-11）
[修复]：修复上个版本更新导致nvue窗口组件不能选择文件的问题；
[新增]：
1.应群友建议(填写禁止格式太多)格式限制formats由原来填写禁止选择的格式改为填写允许被选择的格式；
2.应群友建议(增加上传结束回调事件)，上传结束回调事件@uploadEnd
3.如能帮到你请留下你的免费好评，组件使用过程中有问题可以加QQ群交流，至于Map对象怎么使用这类前端基础问题请自行百度
## 2.1.5（2022-07-01）
app端组件销毁时添加自动销毁webview功能，避免v-if销毁组件的情况控件还能被点击的问题
## 2.1.4（2022-07-01）
修复小程序端回显问题
## 2.1.3（2022-06-30）
回调事件返回参数新增path字段(文件临时地址)，用于回显
## 2.1.2（2022-06-16）
修复APP端Tabbar窗口无法选择文件的问题
## 2.1.1（2022-06-16）
优化：
1.组件优化为允许在v-if中使用；
2.允许option直接在data赋值，不再强制在onRead中初始化；
## 2.1.0（2022-06-13）
h5 pc端更改为单次可多选
## 2.0.9（2022-06-10）
更新演示内容，部分同学不知道怎么获取服务端返回的数据
## 2.0.8（2022-06-09）
优化动态更新上传参数函数，具体查看下方说明：动态更新参数演示
## 2.0.7（2022-06-07）
新增wxFileType属性，用于小程序端选择附件时可选文件类型
## 2.0.6（2022-06-07）
修复小程序端真机选择文件提示失败的问题
## 2.0.5（2022-06-02）
优化小程序端调用hide()后未阻止触发文件选择问题
## 2.0.4（2022-06-01）
优化APP端选择器初始定位
## 2.0.3（2022-05-31）
修复nvue窗口选择文件报错问题 
## 2.0.2（2022-05-20）
修复ios端opiton设置过早未传入webview导致不自动上传问题
## 2.0.1（2022-05-19）
修复APP端子窗口点击选择文件不响应问题
## 2.0.0（2022-05-18）
此次组件更新至2.0版本，与1.0版本使用上略有差异，已使用1.0的同学请自行斟酌是否需要升级！
部分差异：
一、 2.0新增异步触发上传功能；
二、2.0新增文件批量上传功能；
三、2.0优化option，剔除属性，只保留上传接口所需字段，且允许异步更改option的值；
四、组件增加size(文件大小限制)、count(文件个数限制)、formats(文件后缀限制)、accept(文件类型限制)、instantly(是否立即自动上传)、debug(日志打印)等属性；
五、回调事件取消input事件、callback事件，新增change事件和progress事件；
六、ref事件新增upload事件、clear事件；
七、优化组件代码，show和hide函数改为显示隐藏，不再重复开关webview；

## 1.2.3（2022-03-22）
修复Demo里传入待完善功能[手动上传属性manual=true]导致不自动上传的问题，手动提交上传待下个版本更新
## 1.2.2（2022-02-21）
修复上版本APP优化导致H5和小程序端不自动初始化的问题，此次更新仅修复此问题。异步提交功能下个版本更新~
## 1.2.1（2022-01-25）
QQ1群已满，已开放2群：469580165
## 1.2.0（2021-12-09）
优化APP端页面中DOM重排后每次需要重新定位的问题
## 1.1.1（2021-12-09）
优化，与上版本使用方式有改变，请检查后确认是否需要更新，create更名为show,  close更名为hide，取消初始化时手动create, 传参方式改为props=>option
## 1.1.0（2021-12-09）
新增refresh方法，用于DOM发生重排时重新定位控件(APP端)
## 1.0.9（2021-07-15）
修复上传进度未同步渲染，直接返回100%的BUG
## 1.0.8（2021-07-12）
修复H5端传入height和width未生效的bug
## 1.0.7（2021-07-07）
修复h5和小程序端上传完成callback未返回fileName字段问题
## 1.0.6（2021-07-07）
修复h5端提示信息debug
## 1.0.5（2021-06-29）
感谢小伙伴找出bug,上传成功回调success未置为true,已修复
## 1.0.4（2021-06-28）
新增兼容APP,H5,小程序手动关闭控件，关闭后不再弹出文件选择框，需要重新create再次开启
## 1.0.3（2021-06-28）
close增加条件编译，除app端外不需要close
## 1.0.2（2021-06-28）
1.修复页面滚动位置后再create控件导致控件位置不正确的问题；
2.修复nvue无法create控件；
3.示例项目新增nvue使用案例；
## 1.0.1（2021-06-28）
因为有的朋友不清楚app端切换tab时应该怎么处理webview，现重新上传一版示例项目，需要做tab切换的朋友可以导入示例项目查看
## 1.0.0（2021-06-25）
此插件为l-file插件中上传功能改版，更新内容为：
1. 按钮内嵌入页面，不再强制固定底部，可跟随页面滚动
2.无需再单独弹框点击上传，减去中间层
3.通过slot自定义按钮样式
