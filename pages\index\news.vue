<template>
  <view class="news-index-container">
    <mescroll-uni ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption"
      :fixed="false">
      <view class="content-container">
        <!-- 资讯卡片 -->
        <view class="consult-card flowup-card" v-for="(item, index) in consultList" :key="index"
          @click="toDetail(item)">
          <!-- 新闻卡片 -->
          <view v-if="item.category === 'news'" class="news-card-content" :key="'news-' + index">
            <view class="news-image-wrapper">
              <image class="news-image" :src="this.define.baseURL + item.coverImage" mode="aspectFill"></image>
              <view class="news-category-tag">资讯</view>
            </view>
            <view class="news-content">
              <view class="news-title">{{ item.title }}</view>
              <view class="news-desc">{{ item.desc }}</view>
              <view class="news-meta">
                <view class="news-time">
                  <text class="time-icon">🕒</text>
                  {{ xunda.toDate(item.publishTime) }}
                </view>
                <view class="news-views" v-if="item.viewCount">
                  <text class="view-icon">👁</text>
                  {{ item.viewCount }}
                </view>
              </view>
            </view>
          </view>

          <!-- 视频卡片 -->
          <view v-if="item.category === 'video'" class="video-card-content">
            <view class="video-image-wrapper">
              <image class="video-image" :src="this.define.baseURL + item.coverImage" mode="aspectFill"></image>
              <view class="video-play-overlay">
                <view class="play-button">
                  <text class="play-icon">▶</text>
                </view>
              </view>
              <view class="video-duration-tag">{{ item.duration }}</view>
              <view class="video-category-tag">视频</view>
            </view>
            <view class="video-content">
              <view class="video-title">{{ item.title }}</view>
              <view class="video-meta">
                <view class="video-time">
                  <text class="time-icon">🕒</text>
                  {{ xunda.toDate(item.publishTime) }}
                </view>
                <view class="video-views" v-if="item.viewCount">
                  <text class="view-icon">👁</text>
                  {{ item.viewCount }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </mescroll-uni>
  </view>
</template>
<script>
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
import portalItem from "@/pages/portal/components/index.vue";
import defaultPortal from "@/pages/portal/components/defaultPortal.vue";
import PasswordPopup from "./components/PasswordPopup";
import { getNewsList, updateViewCount } from '@/api/flow-up/news';
import xunda from "@/utils/xunda.js";
export default {
  mixins: [MescrollMixin, IndexMixin],
  components: {
    portalItem,
    defaultPortal,
    PasswordPopup,
  },
  data() {
    return {
      upOption: {
        page: {
          num: 0,
          size: 10,
          time: null,
        },
        empty: {
          use: true,
          tip: "📰 暂无资讯内容",
          btnText: "刷新试试",
          icon: "/static/images/empty-news.png"
        },
        textNoMore: "🎉 已经到底啦，没有更多内容了",
        textLoading: "📖 正在加载精彩内容...",
      },
      consultList: [
        // {
        //   id: 1,
        //   image: "https://picsum.photos/300/200?random=1",
        //   title: "糖尿病饮食指导",
        //   desc: "专业营养师为您讲解糖尿病患者的饮食注意事项",
        //   time: "2023-05-15",
        // },
        // {
        //   id: 2,
        //   image: "https://picsum.photos/300/200?random=2",
        //   title: "高血压日常护理",
        //   desc: "高血压患者的日常护理要点和注意事项",
        //   time: "2023-05-10",
        // },
      ],
      videoList: [
        // {
        //   thumb: "https://picsum.photos/300/200?random=3",
        //   title: "心肺复苏急救教学",
        //   duration: "05:30",
        //   url: "https://example.com/video1",
        // },
        // {
        //   thumb: "https://picsum.photos/300/200?random=4",
        //   title: "孕期营养指导",
        //   duration: "08:15",
        //   url: "https://example.com/video2",
        // },
      ],
    };
  },
  onShow() { },
  onReady() { },
  onLoad(e) { },
  computed: {},
  methods: {
    mescrollInit(mescroll) {
      this.mescroll = mescroll;
    },
    async downCallback() {
      // 下拉刷新的回调
      try {
        // 重置分页参数
        this.mescroll.resetUpScroll();
        // 重新加载第一页数据
        const [newsRes] = await Promise.all([
          getNewsList({ page: 1, pageSize: 10 }),
        ]);

        if (newsRes.code === 200) {
          this.consultList = newsRes.data.list || [];
        }

        this.mescroll.endSuccess();
      } catch (error) {
        console.error('下拉刷新失败:', error);
        this.mescroll.endErr();
        uni.showToast({
          title: '刷新失败，请重试',
          icon: 'none'
        });
      }
    },

    async upCallback(page) {
      // 上拉加载的回调
      try {
        // 加载资讯列表
        const newsRes = await getNewsList({
          page: page.num,
          pageSize: page.size
        });

        if (newsRes.code === 200) {
          const { list = [], total = 0 } = newsRes.data;
          // 如果是第一页，直接赋值
          if (page.num === 1) {
            this.consultList = list;
          } else {
            // 不是第一页，追加数据
            this.consultList = this.consultList.concat(list);
          }

          // 判断是否还有下一页
          const hasNext = this.consultList.length < total;
          this.mescroll.endSuccess(list.length, hasNext);

        } else {
          this.mescroll.endErr();
          uni.showToast({
            title: newsRes.msg || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('上拉加载失败:', error);
        this.mescroll.endErr();
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      }
    },
    playVideo(video) {
      uni.navigateTo({
        url: `/pages/news/video?id=${video.id}`,
      });
    },
    async toDetail(record) {
      try {
        // 异步更新阅读量，但不等待结果，让用户可以立即看到内容
        updateViewCount(record.id).catch(() => {
          // 静默处理错误，不影响用户体验
        });
        if (record.category === 'news') {
          uni.navigateTo({
            url: `/pages/news/detail?id=${record.id}`,
          });
        }
        if (record.category === 'video') {
          uni.navigateTo({
            url: `/pages/news/video?id=${record.id}`,
          });
        }
      } catch (error) {
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        });
      }
    },
  },
};
</script>

<style lang="scss">
@import "@/assets/styles/flowup-design.scss";

page {
  background: linear-gradient(135deg, #f0f7ff 0%, #d9ecff 100%);
  min-height: 100vh;
}

// 自定义下拉刷新样式
::v-deep .mescroll-downwarp {
  background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
  color: #fff;
  border-radius: 0 0 20rpx 20rpx;

  .mescroll-downwarp-content {
    font-size: 28rpx;
    font-weight: 500;
    padding: 20rpx 0;
  }
}

// 自定义上拉加载样式
::v-deep .mescroll-upwarp {
  .mescroll-upwarp-tip {
    color: #888;
    font-size: 26rpx;
    padding: 20rpx 0;
  }
}

.content-container {
  padding: 24rpx 20rpx;
}

.consult-card {
  background: #fff;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #2196f3 0%, #1976d2 50%, #d9ecff 100%);
    border-radius: 24rpx 24rpx 0 0;
  }

  &:hover {
    transform: translateY(-6rpx);
    box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
  }
}

// 新闻卡片样式
.news-card-content {
  .news-image-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
    overflow: hidden;

    .news-image {
      width: 100%;
      height: 100%;
      transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
      object-fit: cover;
    }

    .news-category-tag {
      position: absolute;
      top: 16rpx;
      left: 16rpx;
      background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
      backdrop-filter: blur(4rpx);
    }
  }

  .news-content {
    padding: 28rpx 24rpx 24rpx;

    .news-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 16rpx;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .news-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      line-height: 1.6;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .news-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #999;

      .news-time,
      .news-views {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .time-icon,
        .view-icon {
          font-size: 20rpx;
        }
      }
    }
  }
}

// 视频卡片样式
.video-card-content {
  .video-image-wrapper {
    position: relative;
    width: 100%;
    height: 320rpx;
    overflow: hidden;

    .video-image {
      width: 100%;
      height: 100%;
      transition: transform 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
      object-fit: cover;
    }

    .video-play-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;

      .play-button {
        width: 90rpx;
        height: 90rpx;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;

        .play-icon {
          font-size: 36rpx;
          color: #e74c3c;
          margin-left: 4rpx;
        }
      }
    }

    .video-duration-tag {
      position: absolute;
      bottom: 16rpx;
      right: 16rpx;
      background: rgba(0, 0, 0, 0.7);
      color: #fff;
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
      font-size: 22rpx;
      font-weight: 500;
    }

    .video-category-tag {
      position: absolute;
      top: 16rpx;
      left: 16rpx;
      background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
      color: #fff;
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      font-size: 22rpx;
      font-weight: 500;
      box-shadow: 0 4rpx 12rpx rgba(231, 76, 60, 0.3);
    }
  }

  .video-content {
    padding: 28rpx 24rpx 24rpx;

    .video-title {
      font-size: 36rpx;
      font-weight: 700;
      color: #1a1a1a;
      margin-bottom: 20rpx;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .video-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 24rpx;
      color: #999;

      .video-time,
      .video-views {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .time-icon,
        .view-icon {
          font-size: 20rpx;
        }
      }
    }
  }
}

// 悬停效果
.consult-card:hover {

  .news-image,
  .video-image {
    transform: scale(1.08);
  }

  .video-play-overlay .play-button {
    transform: scale(1.12);
    background: rgba(255, 255, 255, 1);
  }
}
</style>
