<template>
  <view class="grid-container">
    <view class="grid-item" v-for="item in gridItems" :key="item.id" @click="navigateTo(item.pagePath)">
      <image :src="item.iconPath" class="grid-icon"></image>
      <view class="grid-text">{{ item.text }}</view>
    </view>
  </view>
</template>
<script>
var wv; //计划创建的webview
import logoImg from "@/static/logo.png";
import { getMyPhysicianAsync, scanBindAsync } from "@/api/flow-up/physician";
// #ifndef MP
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import IndexMixin from "./mixin.js";
// #endif

export default {
  // #ifndef MP
  mixins: [MescrollMixin, IndexMixin],
  // #endif
  components: {},
  data() {
    return {
      logoImg,
      gridItems: [],
      myPhysician: null,
      avatar: "",
    };
  },
  methods: {
    navigateTo(pagePath) {
      uni.navigateTo({
        url: pagePath,
      });
    },
    navigateToDoctor() {
      const params = {
        // 在这里添加你需要的参数
      };
      // 将参数转换为查询字符串
      const queryString = Object.keys(params)
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
        )
        .join("&");
      const pagePath = "/pages/flow-up/doctor/index";
      // 如果有参数，添加到URL后面
      const url = queryString ? `${pagePath}?${queryString}` : pagePath;
      // 跳转到医师界面
      uni.navigateTo({
        url: url,
      });
    },
    loadMyInfo() {
      var data = this;
      getMyPhysicianAsync().then((res) => {
        data.myPhysician = res.data;
        if (res.data) {
          this.avatar = this.define.baseURL + res.data.picture;
        }
      });
    },
  },
  computed: {},
  created() {
    this.gridItems = [
      {
        id: 1,
        text: "我的患者",
        iconPath: "/static/image/flow-up/my-patient.png",
        pagePath: "/pages/flow-up/physician/myPatient",
      },
      {
        id: 2,
        text: "我的随访",
        iconPath: "/static/image/flow-up/my-record.png",
        pagePath: "/pages/flow-up/myVisitRecord?type=physician",
      },
    ];
  },
};
</script>

<style lang="scss">
.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30vh;
  background-color: #f8f9fa;
  margin-top: 20px;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  padding: 15px;
  margin-bottom: 20px;
}

.grid-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 10px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.grid-item:hover {
  transform: translateY(-2px);
}

.grid-icon {
  width: 40px;
  height: 40px;
  margin-bottom: 5px;
}

.grid-text {
  color: #4e5969;
  margin-top: 5px;
}

.content {
  text-align: center;

  .content-title {
    font-size: 20px;
    color: #333;
  }

  .content-desc {
    font-size: 20px;
    margin-bottom: 15px;
  }
}

.avatar-container {
  display: flex;
  justify-content: center;
  padding: 300rpx 0 20rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 60px;
}

.avatar-box {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  border: 4rpx solid #ffffff;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-physician {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10rpx 0 20rpx;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  margin-top: 100px;
}

.bind-tip {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.bind-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15rpx 30rpx;
  background-color: #4a86e8;
  color: #ffffff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.scan-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
</style>
