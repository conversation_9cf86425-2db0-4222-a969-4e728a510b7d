<template>
  <XundaText
    v-if="config.xundaKey == 'text'"
    :content="item.content"
    :textStyle="item.textStyle"
  />
  <XundaGroupTitle
    v-else-if="config.xundaKey == 'groupTitle'"
    :content="item.content"
    :content-position="item.contentPosition"
    :helpMessage="item.helpMessage"
    @groupIcon="clickIcon(item)"
  />
  <XundaDivider
    v-else-if="config.xundaKey == 'divider'"
    :content="item.content"
  />
  <view
    class="xunda-card"
    v-else-if="config.xundaKey === 'card' || config.xundaKey === 'row'"
  >
    <view
      class="xunda-card-cap u-line-1 u-flex"
      v-if="item.header"
      @click="clickIcon(item)"
    >
      {{ item.header }}
      <u-icon
        :name="config.tipLabel ? 'question-circle-fill' : ''"
        class="u-m-l-10"
        color="#a0acb7"
      />
    </view>
    <template
      v-for="(child, index) in config.children"
      :key="child.__config__.renderKey"
    >
      <item
        v-if="!child.__config__.noShow && child.__config__.isVisibility"
        :itemData="child"
        :ref="
          child.__vModel__ ? child.__vModel__ : 'ref' + item.__config__.formId
        "
        :formConf="formConf"
        :formData="formData"
        @input="setValue"
        @clickIcon="clickIcon"
        @clickFun="onChildClick"
        @collapse-change="onChildCollapseChange"
        @tab-change="onChildTabChange"
      />
    </template>
  </view>
  <!-- 步骤条 -->
  <view v-else-if="config.xundaKey === 'steps'">
    <view class="step-container">
      <u-steps
        :list="config.children"
        :mode="item.simple ? 'dot' : 'number'"
        name="title"
        @change="onStepChange($event, item)"
        :current="stepCurrent"
      >
      </u-steps>
    </view>
    <view v-for="(it, i) in config.children" :key="i">
      <view v-show="i == stepCurrent">
        <template
          v-for="(child, index) in it.__config__.children"
          :key="child.__config__.renderKey"
        >
          <item
            v-if="!child.__config__.noShow && child.__config__.isVisibility"
            :itemData="child"
            :formConf="formConf"
            :formData="formData"
            :ref="
              child.__vModel__
                ? child.__vModel__
                : 'ref' + item.__config__.formId
            "
            @input="setValue"
            @clickIcon="clickIcon"
            @clickFun="onChildClick"
            @collapse-change="onChildCollapseChange"
          />
        </template>
      </view>
    </view>
  </view>
  <view class="xunda-tab" v-else-if="config.xundaKey === 'tab'">
    <u-tabs
      is-scroll
      :list="config.children"
      name="title"
      v-model="tabCurrent"
      @change="onTabChange"
      :key="tabKey"
    />
    <view v-for="(it, i) in config.children" :key="i">
      <view v-show="i == tabCurrent">
        <template
          v-for="(child, index) in it.__config__.children"
          :key="child.__config__.renderKey"
        >
          <item
            v-if="!child.__config__.noShow && child.__config__.isVisibility"
            :itemData="child"
            :formConf="formConf"
            :formData="formData"
            :ref="
              child.__vModel__
                ? child.__vModel__
                : 'ref' + item.__config__.formId
            "
            @input="setValue"
            @clickIcon="clickIcon"
            @clickFun="onChildClick"
            @collapse-change="onChildCollapseChange"
            @tab-change="onChildTabChange"
          />
        </template>
      </view>
    </view>
  </view>
  <view v-else-if="config.xundaKey === 'collapse'">
    <u-collapse
      ref="collapseRef"
      :head-style="{ 'padding-left': '20rpx' }"
      :accordion="item.accordion"
    >
      <u-collapse-item
        v-for="(it, i) in config.children"
        :key="i"
        :title="it.title"
        :open="config.active.indexOf(it.name) > -1"
        @change="onCollapseChange"
      >
        <template
          v-for="(child, index) in it.__config__.children"
          :key="child.__config__.renderKey"
        >
          <item
            v-if="!child.__config__.noShow && child.__config__.isVisibility"
            :itemData="child"
            :formConf="formConf"
            :formData="formData"
            :ref="
              child.__vModel__
                ? child.__vModel__
                : 'ref' + item.__config__.formId
            "
            @input="setValue"
            @clickIcon="clickIcon"
            @clickFun="onChildClick"
            @collapse-change="onChildCollapseChange"
            @tab-change="onChildTabChange"
          />
        </template>
      </u-collapse-item>
    </u-collapse>
  </view>
  <view v-else-if="config.xundaKey === 'table'">
    <child-table
      v-if="config.isVisibility"
      v-model="value"
      :config="item"
      :ref="item.__vModel__"
      :formData="formData"
      @input="setValue"
    />
  </view>
  <u-form-item
    v-else
    :label="realLabel"
    :prop="item.__vModel__"
    :required="config.required"
    :label-width="labelWidth"
    :left-icon="leftIcon"
    :left-icon-style="{ color: '#a8aaaf' }"
    @clickIcon="clickIcon(item)"
  >
    <XundaInput
      v-if="config.xundaKey == 'input'"
      v-model="value"
      :showPassword="item.showPassword"
      :placeholder="item.placeholder"
      :maxlength="item.maxlength"
      :showCount="item.showCount"
      :disabled="item.disabled"
      :clearable="item.clearable"
      :useScan="item.useScan"
      :addonBefore="item.addonBefore"
      :addonAfter="item.addonAfter"
      @change="onChange"
      @blur="onBlur"
    />
    <XundaTextarea
      v-if="config.xundaKey == 'textarea'"
      v-model="value"
      :placeholder="item.placeholder"
      :maxlength="item.maxlength"
      :showCount="item.showCount"
      :disabled="item.disabled"
      :clearable="item.clearable"
      @change="onChange"
      @blur="onBlur"
    />
    <XundaInputNumber
      v-if="config.xundaKey == 'inputNumber'"
      v-model="value"
      :step="item.step"
      :max="item.max || 999999999999999"
      :min="item.min || -999999999999999"
      :disabled="item.disabled"
      :placeholder="item.placeholder"
      :isAmountChinese="item.isAmountChinese"
      :thousands="item.thousands"
      :addonAfter="item.addonAfter"
      :addonBefore="item.addonBefore"
      :controls="item.controls"
      :precision="item.precision"
      @change="onChange"
      @blur="onBlur"
    />
    <XundaSwitch
      v-if="config.xundaKey == 'switch'"
      v-model="value"
      :disabled="item.disabled"
      @change="onChange"
    />
    <XundaRadio
      v-if="config.xundaKey == 'radio'"
      v-model="value"
      :options="item.options"
      :props="item.props"
      :disabled="item.disabled"
      :direction="item.direction"
      @change="onChange"
    />
    <XundaCheckbox
      v-if="config.xundaKey == 'checkbox'"
      v-model="value"
      :options="item.options"
      :props="item.props"
      :disabled="item.disabled"
      :direction="item.direction"
      @change="onChange"
    />
    <XundaSelect
      v-if="config.xundaKey == 'select'"
      v-model="value"
      :placeholder="item.placeholder"
      :options="item.options"
      :props="item.props"
      :multiple="item.multiple"
      :disabled="item.disabled"
      @change="onChange"
      :filterable="item.filterable"
    />
    <XundaCascader
      v-if="config.xundaKey == 'cascader'"
      v-model="value"
      :placeholder="item.placeholder"
      :options="item.options"
      :props="item.props"
      :disabled="item.disabled"
      :multiple="item.multiple"
      :filterable="item.filterable"
      :clearable="item.clearable"
      :showAllLevels="item.showAllLevels"
      @change="onChange"
    />
    <XundaDatePicker
      v-if="config.xundaKey == 'datePicker'"
      v-model="value"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :format="item.format"
      :startTime="item.startTime"
      :endTime="item.endTime"
      @change="onChange"
    />
    <XundaTimePicker
      v-if="config.xundaKey == 'timePicker'"
      v-model="value"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :format="item.format"
      :startTime="item.startTime"
      :endTime="item.endTime"
      @change="onChange"
    />
    <XundaUploadFile
      v-if="config.xundaKey == 'uploadFile'"
      v-model="value"
      :disabled="item.disabled"
      :limit="item.limit"
      :sizeUnit="item.sizeUnit"
      :fileSize="item.fileSize"
      :pathType="item.pathType"
      :isAccount="item.isAccount"
      :folder="item.folder"
      :accept="item.accept"
      :tipText="item.tipText"
      @change="onChange"
      :sortRule="item.sortRule"
      :timeFormat="item.timeFormat"
    />
    <XundaUploadImg
      v-if="config.xundaKey == 'uploadImg'"
      v-model="value"
      :disabled="item.disabled"
      :limit="item.limit"
      :sizeUnit="item.sizeUnit"
      :fileSize="item.fileSize"
      :pathType="item.pathType"
      :isAccount="item.isAccount"
      :folder="item.folder"
      :tipText="item.tipText"
      @change="onChange"
      :sortRule="item.sortRule"
      :timeFormat="item.timeFormat"
    />
    <XundaColorPicker
      v-if="config.xundaKey == 'colorPicker'"
      v-model="value"
      :colorFormat="item.colorFormat"
      :disabled="item.disabled"
      @change="onChange"
    />
    <XundaRate
      v-if="config.xundaKey == 'rate'"
      v-model="value"
      :max="item.count"
      :allowHalf="item.allowHalf"
      :disabled="item.disabled"
      @change="onChange"
    />
    <XundaSlider
      v-if="config.xundaKey == 'slider'"
      v-model="value"
      :step="item.step"
      :min="item.min"
      :max="item.max"
      :disabled="item.disabled"
      @change="onChange"
    />
    <XundaBarcode
      v-if="config.xundaKey == 'barcode'"
      :staticText="item.staticText"
      :width="item.width"
      :height="item.height"
      :format="item.format"
      :dataType="item.dataType"
      :lineColor="item.lineColor"
      :background="item.background"
      :relationField="item.relationField"
      :formData="formData"
    />
    <XundaQrcode
      v-if="config.xundaKey == 'qrcode'"
      :staticText="item.staticText"
      :width="item.width"
      :dataType="item.dataType"
      :colorDark="item.colorDark"
      :colorLight="item.colorLight"
      :relationField="item.relationField"
      :formData="formData"
    />
    <XundaOrganizeSelect
      v-if="config.xundaKey == 'organizeSelect'"
      v-model="value"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :ableIds="item.ableIds"
      :selectType="item.selectType"
      @change="onChange"
    />
    <XundaDepSelect
      v-if="config.xundaKey == 'depSelect'"
      v-model="value"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :ableIds="item.ableIds"
      :selectType="item.selectType"
      @change="onChange"
    />
    <XundaPosSelect
      v-if="config.xundaKey == 'posSelect'"
      v-model="value"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :ableIds="item.ableIds"
      :selectType="item.selectType"
      @change="onChange"
    />
    <XundaUserSelect
      v-if="config.xundaKey == 'userSelect'"
      v-model="value"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :selectType="item.selectType"
      :ableIds="item.ableIds"
      :clearable="item.clearable"
      :ableRelationIds="item.ableRelationIds"
      @change="onChange"
    />
    <XundaUsersSelect
      v-if="config.xundaKey == 'usersSelect'"
      v-model="value"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :selectType="item.selectType"
      :ableIds="item.ableIds"
      :clearable="item.clearable"
      @change="onChange"
    />
    <XundaRoleSelect
      v-if="config.xundaKey == 'roleSelect'"
      v-model="value"
      :vModel="item.__vModel__"
      :multiple="item.multiple"
      :disabled="item.disabled"
      :placeholder="item.placeholder"
      :ableIds="item.ableIds"
      :selectType="item.selectType"
      @change="onChange"
    />
    <XundaGroupSelect
      v-if="config.xundaKey == 'groupSelect'"
      v-model="value"
      :vModel="item.__vModel__"
      :multiple="item.multiple"
      :disabled="item.disabled"
      :ableIds="item.ableIds"
      :selectType="item.selectType"
      :placeholder="item.placeholder"
      @change="onChange"
    />
    <XundaTreeSelect
      v-if="config.xundaKey == 'treeSelect'"
      v-model="value"
      :options="item.options"
      :props="item.props"
      :multiple="item.multiple"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :filterable="item.filterable"
      @change="onChange"
    />
    <XundaAutoComplete
      v-if="config.xundaKey == 'autoComplete'"
      v-model="value"
      :disabled="item.disabled"
      :interfaceName="item.interfaceName"
      :placeholder="item.placeholder"
      :interfaceId="item.interfaceId"
      :total="item.total"
      :templateJson="item.templateJson"
      :formData="formData"
      :relationField="item.relationField"
      :propsValue="item.propsValue"
      :clearable="item.clearable"
      @change="onChange"
    />
    <XundaAreaSelect
      v-if="config.xundaKey == 'areaSelect'"
      v-model="value"
      :placeholder="item.placeholder"
      :level="item.level"
      :disabled="item.disabled"
      :multiple="item.multiple"
      @change="onChange"
    />
    <XundaRelationForm
      v-if="config.xundaKey == 'relationForm'"
      v-model="value"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :modelId="item.modelId"
      :columnOptions="item.columnOptions"
      :relationField="item.relationField"
      :hasPage="item.hasPage"
      :pageSize="item.pageSize"
      :vModel="
        config.tableName
          ? item.__vModel__ +
            '_xundaTable_' +
            config.tableName +
            (config.isSubTable ? '0' : '1')
          : item.__vModel__
      "
      :popupTitle="item.popupTitle"
      @change="onChange"
    />
    <XundaRelationFormAttr
      v-if="config.xundaKey == 'relationFormAttr'"
      v-model="value"
      :showField="item.showField"
      :relationField="item.relationField"
      :isStorage="item.isStorage"
      @change="onChange"
    />
    <XundaPopupSelect
      v-if="
        config.xundaKey == 'popupSelect' ||
        config.xundaKey == 'popupTableSelect'
      "
      v-model="value"
      :placeholder="item.placeholder"
      :disabled="item.disabled"
      :interfaceId="item.interfaceId"
      :formData="formData"
      :templateJson="item.templateJson"
      :columnOptions="item.columnOptions"
      :relationField="item.relationField"
      :propsValue="item.propsValue"
      :hasPage="item.hasPage"
      :pageSize="item.pageSize"
      :vModel="
        config.tableName
          ? item.__vModel__ +
            '_xundaTable_' +
            config.tableName +
            (config.isSubTable ? '0' : '1')
          : config.__vModel__
      "
      :popupTitle="item.popupTitle"
      :multiple="item.multiple"
      @change="onChange"
    />
    <XundaPopupAttr
      v-if="config.xundaKey == 'popupAttr'"
      v-model="value"
      :showField="item.showField"
      :relationField="item.relationField"
      :isStorage="item.isStorage"
      @change="onChange"
    />
    <XundaCalculate
      v-if="config.xundaKey == 'calculate'"
      v-model="value"
      :expression="item.expression"
      :vModel="item.__vModel__"
      :config="item.__config__"
      :formData="formData"
      :precision="item.precision"
      :isAmountChinese="item.isAmountChinese"
      :thousands="item.thousands"
    />
    <XundaSign
      v-if="config.xundaKey == 'sign'"
      v-model="value"
      :disabled="item.disabled"
      :fieldKey="item.__vModel__"
      @change="onChange"
      :isInvoke="item.isInvoke"
    />
    <XundaSignature
      v-if="config.xundaKey == 'signature'"
      v-model="value"
      :disabled="item.disabled"
      @change="onChange"
      :ableIds="item.ableIds"
    />
    <XundaLocation
      v-if="config.xundaKey == 'location'"
      v-model="value"
      :autoLocation="item.autoLocation"
      :adjustmentScope="item.adjustmentScope"
      :enableLocationScope="item.enableLocationScope"
      :enableDesktopLocation="item.enableDesktopLocation"
      :locationScope="item.locationScope"
      :disabled="item.disabled"
      :clearable="item.clearable"
      @change="onChange"
    />
    <XundaOpenData
      v-if="isSystem"
      v-model="value"
      :type="item.type"
      :showLevel="item.showLevel"
    />
    <XundaInput
      v-if="
        config.xundaKey === 'modifyUser' || config.xundaKey === 'modifyTime'
      "
      v-model="value"
      placeholder="系统自动生成"
      disabled
    />
    <!--start labelwidth=0-->
    <XundaLink
      v-if="config.xundaKey == 'link'"
      :content="item.content"
      :href="item.href"
      :target="item.target"
      :textStyle="item.textStyle"
      @click="onClick"
    />
    <XundaEditor
      v-if="config.xundaKey == 'editor'"
      v-model="value"
      :disabled="item.disabled"
      :placeholder="item.placeholder"
    />
    <XundaButton
      v-if="config.xundaKey == 'button'"
      :buttonText="item.buttonText"
      :align="item.align"
      :type="item.type"
      :disabled="item.disabled"
      @click="onClick($event)"
    />
    <XundaAlert
      v-if="config.xundaKey == 'alert'"
      :type="item.type"
      :title="item.title"
      :tagIcon="item.tagIcon"
      :showIcon="item.showIcon"
      :closable="item.closable"
      :description="item.description"
      :closeText="item.closeText"
    />
    <!--end  labelwidth=0-->
  </u-form-item>
</template>

<script>
import childTable from "./childTable.vue";
// #ifdef MP
import Item from "./Item.vue"; //兼容小程序
// #endif
const systemList = [
  "createUser",
  "createTime",
  "currOrganize",
  "currDept",
  "currPosition",
  "billRule",
];
const specialList = ["link", "editor", "button", "alert"];
export default {
  name: "Item",
  inject: ["parameter"],
  emits: ["input", "clickIcon", "clickFun", "collapseChange", "tabChange"],
  components: {
    childTable,
    // #ifdef MP
    Item,
    // #endif
  },
  data() {
    return {
      value: undefined,
      tabCurrent: 0,
      stepCurrent: 0,
      tabKey: +new Date(),
    };
  },
  props: {
    itemData: {
      type: Object,
      required: true,
    },
    formConf: {
      type: Object,
      required: true,
    },
    formData: {
      type: Object,
      required: true,
    },
  },
  computed: {
    item() {
      return uni.$u.deepClone(this.itemData);
    },
    config() {
      return this.item.__config__;
    },
    isSystem() {
      return systemList.indexOf(this.config.xundaKey) > -1;
    },
    labelWidth() {
      if (specialList.indexOf(this.config.xundaKey) > -1) return 0;
      return this.config.labelWidth ? this.config.labelWidth * 1.5 : undefined;
    },
    label() {
      return this.config.showLabel &&
        specialList.indexOf(this.config.xundaKey) < 0
        ? this.config.label
        : "";
    },
    realLabel() {
      return this.label ? this.label + (this.formConf.labelSuffix || "") : "";
    },
    leftIcon() {
      return this.config.tipLabel && this.label && this.config.showLabel
        ? "question-circle-fill"
        : "";
    },
  },
  watch: {
    value(val) {
      this.item.__config__.defaultValue = val;
      this.$emit("input", this.item);
    },
  },
  created() {
    this.initData();
  },
  mounted() {
    if (this.config.xundaKey === "collapse") {
      uni.$on("initCollapse", () => {
        this.$refs.collapseRef && this.$refs.collapseRef.init();
      });
    }
  },
  methods: {
    onStepChange(index, item) {
      if (this.stepCurrent === index) return;
      item.__config__.active = index;
      this.stepCurrent = index;
      this.$nextTick(() => {
        uni.$emit("updateCode");
        uni.$emit("initCollapse");
      });
    },
    initData() {
      if (this.config.xundaKey === "steps")
        this.stepCurrent = this.config.active;
      if (this.config.xundaKey != "tab")
        return (this.value = this.config.defaultValue);
      for (var i = 0; i < this.config.children.length; i++) {
        if (this.config.active == this.config.children[i].name) {
          this.tabCurrent = i;
          break;
        }
      }
    },
    onBlur(val) {
      this.setScriptFunc(val, this.item, "blur");
    },
    onChange(val, data) {
      this.setScriptFunc(data || val, this.item);
      if (
        ["popupSelect", "relationForm"].includes(this.item.__config__.xundaKey)
      ) {
        this.setTransferFormData(
          data || val,
          this.item.__config__,
          this.item.__config__.xundaKey
        );
      }
      this.$nextTick(() => {
        uni.$emit("subChange", this.item, data || val);
      });
    },
    setScriptFunc(val, data, type = "change") {
      if (data && data.on && data.on[type]) {
        const func = this.xunda.getScriptFunc(data.on[type]);
        if (!func) return;
        func.call(this, {
          data: val,
          ...this.parameter,
        });
      }
    },
    setTransferFormData(data, config, xundaKey) {
      if (!config.transferList.length) return;
      for (let index = 0; index < config.transferList.length; index++) {
        const element = config.transferList[index];
        this.parameter.setFormData(
          element.sourceValue,
          data[element.targetField]
        );
      }
    },
    onTabChange(index) {
      if (this.tabCurrent === index) return;
      this.tabCurrent = index;
      const id = this.item.__config__.children[index].name;
      this.$emit("tab-change", this.item, id);
      this.$nextTick(() => {
        uni.$emit("updateCode");
        uni.$emit("initCollapse");
      });
    },
    onChildTabChange(item, id) {
      this.$emit("tab-change", item, id);
    },
    onCollapseChange(data) {
      this.$emit("collapse-change", this.item, data);
      this.$nextTick(() => {
        uni.$emit("initCollapse");
      });
    },
    onChildCollapseChange(item, id) {
      this.$emit("collapse-change", item, id);
    },
    setValue(item, data) {
      this.$emit("input", item, data);
    },
    onClick(e) {
      this.$emit("clickFun", this.item, e || "");
    },
    onChildClick(e, item) {
      this.$emit("clickFun", item, e || "");
    },
    clickIcon(e) {
      this.$emit("clickIcon", e);
    },
  },
};
</script>
<style lang="scss" scoped>
.form-item-box {
  padding: 0 20rpx;
}
</style>
