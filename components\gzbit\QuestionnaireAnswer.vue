<template>
  <view class="questionnaire-answer-container">
    <view v-if="radioQuestions.length > 0">
      <view class="question-section">
        <view class="section-header">
          <view class="section-icon">🔘</view>
          <text class="section-title">单选题</text>
          <text class="question-count">({{ radioQuestions.length }}题)</text>
        </view>
        <view class="questions-list">
          <view class="question-item" v-for="(item, index) in radioQuestions" :key="index">
            <view class="question-header">
              <text class="question-text">
                <text v-if="item.required" class="required-tag">[必填]</text>
                <text class="question-number">{{ index + 1 }}.</text>
                <text class="question-content">{{ item.question }}</text>
              </text>
            </view>
            <view>
              <XundaRadio v-model="item.answer" :disabled="readonly" :style="{ width: '100%' }" :options="item.options"
                :props="radioProps" :direction="direction">
              </XundaRadio>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="checkboxQuestions.length > 0">
      <view class="question-section">
        <view class="section-header">
          <view class="section-icon">✅</view>
          <text class="section-title">多选题</text>
          <text class="question-count">({{ checkboxQuestions.length }}题)</text>
        </view>
        <view class="questions-list">
          <view class="question-item" v-for="(item, index) in checkboxQuestions" :key="index">
            <view class="question-header">
              <text class="question-text">
                <text v-if="item.required" class="required-tag">[必填]</text>
                <text class="question-number">{{ index + 1 }}.</text>
                <text class="question-content">{{ item.question }}</text>
              </text>
            </view>
            <view>
              <XundaCheckbox v-model="item.selectAnswer" :options="item.options" :disabled="readonly"
                :direction="direction" :props="defaultProps" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="textAreaQuestions.length > 0">
      <view class="question-section">
        <view class="section-header">
          <view class="section-icon">📝</view>
          <text class="section-title">简答题</text>
          <text class="question-count">({{ textAreaQuestions.length }}题)</text>
        </view>
        <view class="questions-list">
          <view class="question-item" v-for="(item, index) in textAreaQuestions" :key="index">
            <view class="question-header">
              <text class="question-text">
                <text v-if="item.required" class="required-tag">[必填]</text>
                <text class="question-number">{{ index + 1 }}.</text>
                <text class="question-content">{{ item.question }}</text>
              </text>
            </view>
            <u-input v-model="item.answer" type="textarea" :placeholder="readonly ? '' : '请输入您的回答...'"
              :disabled="readonly" border="surround" :custom-style="{
                minHeight: '160rpx',
                padding: '20rpx',
                backgroundColor: '#fff',
                borderRadius: '12rpx',
                marginTop: '16rpx',
              }" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "QuestionnaireAnswers",
  components: {},
  props: {
    answers: {
      type: Array,
      default: () => {
        return [];
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      direction: "vertical", //水平或者垂直 horizontal vertical
      radioProps: { label: "option", value: "title" },
      defaultProps: { label: "option", value: "title" },
    };
  },
  computed: {
    radioQuestions() {
      return this.answers.filter((item) => item.type === "radio");
    },
    checkboxQuestions() {
      return this.answers.filter((item) => item.type === "checkbox");
    },
    textAreaQuestions() {
      return this.answers.filter((item) => item.type === "textarea");
    },
  },
  created() { },
  methods: {},
};
</script>

<style lang="scss" scoped>
.questionnaire-answer-container {
  .question-section {
    margin-bottom: 32rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

    .section-header {
      display: flex;
      align-items: center;
      gap: 16rpx;
      padding: 20rpx 24rpx;
      background: linear-gradient(to right, #4caf50, #8bc34a);
      color: #fff;

      .section-icon {
        font-size: 28rpx;
      }

      .section-title {
        font-size: 28rpx;
        font-weight: 600;
      }

      .question-count {
        font-size: 24rpx;
        opacity: 0.9;
      }
    }

    .questions-list {
      padding: 24rpx;

      .question-item {
        margin-bottom: 32rpx;
        padding: 24rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        border: 2rpx solid #e9ecef;

        &:last-child {
          margin-bottom: 0;
        }

        .question-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20rpx;

          .question-number {
            font-weight: 700;
            color: #1976d2;
            margin-right: 12rpx;
            font-size: 28rpx;
            line-height: 1.5;
          }

          .question-text {
            flex: 1;
            font-size: 28rpx;
            color: #333;
            line-height: 1.5;
          }

          .required-tag {
            margin-left: 10rpx;
            font-size: 20rpx;
            color: #fff;
            background-color: #f44336;
            padding: 2rpx 8rpx;
            border-radius: 4rpx;
            align-self: center;
          }
        }

        .question-options {
          padding: 16rpx 0;

          :deep(.u-radio-group) {
            .u-radio {
              margin-bottom: 16rpx;
              padding: 20rpx;
              background: #fff;
              border-radius: 12rpx;
              border: 2rpx solid #e0e0e0;
              transition: all 0.3s ease;

              &.u-radio--checked {
                border-color: #1976d2;
                background: rgba(25, 118, 210, 0.05);
                box-shadow: 0 4rpx 8rpx rgba(25, 118, 210, 0.1);
              }

              .u-radio__label {
                font-size: 26rpx;
                color: #444;
                margin-left: 16rpx;
              }

              .u-radio__icon-wrap {
                width: 36rpx;
                height: 36rpx;

                .u-icon {
                  font-size: 36rpx;
                }
              }
            }
          }

          :deep(.u-checkbox-group) {
            .u-checkbox {
              margin-bottom: 16rpx;
              padding: 20rpx;
              background: #fff;
              border-radius: 12rpx;
              border: 2rpx solid #e0e0e0;
              transition: all 0.3s ease;

              &.u-checkbox--checked {
                border-color: #1976d2;
                background: rgba(25, 118, 210, 0.05);
                box-shadow: 0 4rpx 8rpx rgba(25, 118, 210, 0.1);
              }

              .u-checkbox__label {
                font-size: 26rpx;
                color: #444;
                margin-left: 16rpx;
              }

              .u-checkbox__icon-wrap {
                width: 36rpx;
                height: 36rpx;

                .u-icon {
                  font-size: 36rpx;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .questionnaire-answer-container {
    .question-section {
      margin-bottom: 24rpx;

      .section-header {
        padding: 16rpx 20rpx;

        .section-icon {
          font-size: 24rpx;
        }

        .section-title {
          font-size: 26rpx;
        }

        .question-count {
          font-size: 22rpx;
        }
      }

      .questions-list {
        padding: 20rpx;

        .question-item {
          margin-bottom: 24rpx;
          padding: 20rpx;

          .question-header {
            margin-bottom: 16rpx;

            .question-number {
              font-size: 26rpx;
              margin-right: 10rpx;
            }

            .question-text {
              font-size: 26rpx;
            }

            .required-tag {
              font-size: 18rpx;
              padding: 2rpx 6rpx;
              margin-left: 8rpx;
            }
          }

          .question-options {
            padding: 12rpx 0;

            :deep(.u-radio),
            :deep(.u-checkbox) {
              margin-bottom: 12rpx;
              padding: 16rpx;

              .u-radio__label,
              .u-checkbox__label {
                font-size: 24rpx;
              }

              .u-radio__icon-wrap,
              .u-checkbox__icon-wrap {
                width: 32rpx;
                height: 32rpx;

                .u-icon {
                  font-size: 32rpx;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>