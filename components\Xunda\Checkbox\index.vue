<template>
  <u-checkbox-group
    :disabled="disabled"
    :wrap="direction == 'horizontal' ? false : true"
    @change="onChange"
    :borderBottom="true"
    placement="column"
    iconPlacement="right"
  >
    <u-checkbox
      v-model="item.checked"
      v-for="(item, index) in optionList"
      :key="index"
      :name="item[props.value]"
      :custom-style="checkboxStyle"
      icon-size="36"
      label-size="26"
      :shape="disabled ? 'circle' : 'square'"
      active-color="#1976d2"
    >
      {{ item[props.label] }}
    </u-checkbox>
  </u-checkbox-group>
</template>

<script>
export default {
  name: "xunda-checkbox",
  inheritAttrs: false,
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    direction: {
      type: String,
      default: "horizontal",
    },
    options: {
      type: Array,
      default: () => [],
    },
    props: {
      type: Object,
      default: () => ({
        label: "fullName",
        value: "id",
      }),
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
  },
  data() {
    return {
      optionList: [],
    };
  },
  computed: {
    checkboxStyle() {
      return {
        marginBottom: this.direction === "horizontal" ? "0" : "16rpx",
        padding: "20rpx",
        backgroundColor: "#fff",
        borderRadius: "12rpx",
        border: "2rpx solid #e0e0e0",
        transition: "all 0.3s ease",
      };
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        if (!val || !val?.length) return this.setColumnData();
        this.setDefault();
      },
      immediate: true,
    },
    options: {
      handler(val) {
        this.setColumnData();
      },
      immediate: true,
    },
  },
  methods: {
    setDefault() {
      if (!this.modelValue || !this.modelValue?.length) return;
      outer: for (let i = 0; i < this.modelValue.length; i++) {
        inner: for (let j = 0; j < this.optionList.length; j++) {
          if (this.modelValue[i] === this.optionList[j][this.props.value]) {
            this.optionList[j].checked = true;
            break inner;
          }
        }
      }
    },
    setColumnData() {
      this.optionList = this.options.map((o) => ({
        ...o,
        checked: false,
      }));
      this.setDefault();
    },
    onChange(value) {
      const selectData = this.optionList.filter((o) => o.checked) || [];
      this.$emit("update:modelValue", value);
      this.$emit("change", value, selectData);
    },
  },
};
</script>
