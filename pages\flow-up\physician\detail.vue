<template>
  <view class="xunda-wrap xunda-wrap-form" v-if="!loading">
    <u-form :model="dataForm" ref="dataFormRef" :errorType="['toast']" label-position="left" label-align="left"
      :label-width="labelwidth" class="xunda-form">
      <view prop="activetab">
        <u-tabs ref="activetab" :is-scroll="false" :list="activetabData" name="title" v-model="activetab"
          @change="clickActivetab" />
        <view>
          <view v-for="(pane, i) in tabList" :key="i">
            <view v-show="i == activetab">
              <view class="u-p-l-20 u-p-r-20 form-item-box" v-for="(configItem, childIndex) in pane.config.items">
                <view v-if="configItem.key == 'id'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      {{ dataForm[configItem.key] }}
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'picture'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      <u-avatar size="200" :src="picture" @click="doPreviewImage(picture)"></u-avatar>
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'phone'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      <text :style="callPhone
                        ? 'color: blue; text-decoration: underline'
                        : ''
                        ">
                        {{ dataForm.phone }}
                      </text>
                      <uni-icons v-show="callPhone" @tap="telPhone(dataForm.phone)" style="margin-left: 20px"
                        type="phone" size="20"></uni-icons>
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'certificate'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      <XundaUploadFile v-model="dataForm.certificate" :fieldKey="'certificate'" detailed
                        :list="dataForm.certificate" sizeUnit="MB" :fileSize="10" :limit="9" pathType="defaultPath"
                        timeFormat="YYYY">
                      </XundaUploadFile>
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'qualificationCertificate'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      <XundaUploadFile v-model="dataForm.qualificationCertificate"
                        :fieldKey="'qualificationCertificate'" detailed :list="dataForm.qualificationCertificate"
                        sizeUnit="MB" :fileSize="10" :limit="9" pathType="defaultPath" timeFormat="YYYY">
                      </XundaUploadFile>
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'workCertificate'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      <XundaUploadFile v-model="dataForm.workCertificate" :fieldKey="'workCertificate'" detailed
                        :list="dataForm.workCertificate" sizeUnit="MB" :fileSize="10" :limit="9" pathType="defaultPath"
                        timeFormat="YYYY">
                      </XundaUploadFile>
                    </view>
                  </u-form-item>
                </view>
                <view v-else-if="configItem.key == 'technicalTitle'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                  </u-form-item>
                  <TechnicalTitleItem :items="dataForm[configItem.key]"></TechnicalTitleItem>
                </view>
                <view v-else-if="configItem.key == 'majorField'">
                  <MajorFieldItem v-model:items="dataForm.majorField"></MajorFieldItem>
                </view>
                <view v-else-if="configItem.key == 'workHistory'">
                  <WorkHistoryItem v-model:items="dataForm.workHistory"></WorkHistoryItem>
                </view>
                <view v-else-if="configItem.key == 'academicPosition'">
                  <AcademicPositionItem v-model:items="dataForm.academicPosition"></AcademicPositionItem>
                </view>
                <view v-else-if="configItem.type == 'dictionary'">
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      {{
                        xunda.dictionaryText(
                          dataForm[configItem.key],
                          optionsObj[configItem.options]
                        )
                      }}
                    </view>
                  </u-form-item>
                </view>
                <view v-else>
                  <u-form-item :label="configItem.label" :prop="configItem.key">
                    <view class="xunda-detail-text">
                      {{ dataForm[configItem.key] }}
                    </view>
                  </u-form-item>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-form>
    <view class="buttom-actions">
      <u-button class="buttom-btn" @click="resetForm">取消</u-button>
      <u-button class="buttom-btn" type="primary" @click.stop="submitForm"
        v-if="btnList.includes('btn_edit')">编辑</u-button>
    </view>
    <u-modal v-model="show" :content="content" width="70%" border-radius="16" :content-style="{
      fontSize: '28rpx',
      padding: '20rpx',
      lineHeight: '44rpx',
      textAlign: 'left',
    }" :titleStyle="{ padding: '20rpx' }" :confirm-style="{ height: '80rpx', lineHeight: '80rpx' }" :title="title"
      confirm-text="确定">
    </u-modal>
  </view>
</template>

<script>
import { getPhysicianAllDictionaryData } from "@/pages/flow-up/physician/index.js";
import { getDetail } from "@/pages/flow-up/physician/api.js";
import {
  getProvinceSelectorInfoList
} from '@/api/common.js'
import TechnicalTitleItem from "@/pages/flow-up/physician/components/TechnicalTitleItem.vue";
import MajorFieldItem from "@/pages/flow-up/physician/components/MajorFieldItem.vue";
import AcademicPositionItem from "@/pages/flow-up/physician/components/AcademicPositionItem.vue";
import WorkHistoryItem from "@/pages/flow-up/physician/components/WorkHistoryItem.vue";
export default {
  components: {
    TechnicalTitleItem,
    MajorFieldItem,
    AcademicPositionItem,
    WorkHistoryItem,
  },
  data() {
    return {
      tabList: [
        {
          name: "个人信息",
          config: {
            items: [
              { label: "姓名", key: "name", type: "text" },
              {
                label: "性别",
                key: "sex",
                type: "dictionary",
                optionsProps: "sexProps",
                options: "sexOptions",
              },
              { label: "照片", key: "picture", type: "avatar" },
              {
                label: "出生日期",
                key: "birthdate",
                type: "date",
                xundaKey: "datePicker",
              },
              {
                label: "民族",
                key: "nation",
                type: "dictionary",
                optionsProps: "nationProps",
                options: "nationOptions",
              },
              {
                label: "党派",
                key: "politicalGroup",
                type: "dictionary",
                options: "politicalGroupOptions",
              },
              { label: "手机号", key: "phone", type: "text" },
              { label: "籍贯", key: "nativeplace", type: "text" },
              { label: "身份证号", key: "idCard", type: "text" },
              { label: "家庭住址", key: "address", type: "text" },
              {
                label: "单位任职",
                key: "position",
                type: "dictionary",
                options: "positionOptions",
              },
              {
                label: "医师级别",
                key: "medicalLevel",
                type: "dictionary",
                options: "medicalLevelOptions",
              },
              {
                label: "执业状况",
                key: "vocationalStatus",
                type: "dictionary",
                options: "vocationalStatusOptions",
              },
              { label: "执业证书编号", key: "certificateNo", type: "text" },
              { label: "执业证书", key: "certificate", type: "uploadFile" },
              {
                label: "资格证书编号",
                key: "qualificationCertificateNo",
                type: "text",
              },
              {
                label: "资格证书",
                key: "qualificationCertificate",
                type: "uploadFile",
              },
              { label: "工作证书编号", key: "workCertificateNo", type: "text" },
              { label: "工作证书", key: "workCertificate", type: "uploadFile" },
              { label: "专业技术职称", key: "technicalTitle", type: "text" },
              { label: "备注说明", key: "remarks", type: "text" },
            ],
          },
        },
        {
          name: "专业方向",
          config: {
            items: [
              { label: "毕业学校", key: "graduationSchool", type: "text" },
              { label: "毕业时间", key: "graduationTime", type: "date" },
              { label: "学历", key: "educationLevel", type: "text" },
              { label: "学位", key: "degree", type: "text" },
              { label: "专业", key: "major", type: "text" },
              { label: "从业时间", key: "workingSeniority", type: "date" },
              {
                label: "从业类型",
                key: "workType",
                type: "dictionary",
                options: "workTypeOptions",
              },
              { label: "专业方向", key: "majorField", type: "text" },
            ],
          },
        },
        {
          name: "工作经历",
          config: {
            items: [
              { label: "省份", key: "provinceAndCityString", type: "text" },
              { label: "医院名称", key: "hospitalName", type: "text" },
              { label: "科室", key: "department", type: "text" },
              { label: "科室名称", key: "departmentName", type: "text" },
              { label: "工作经历", key: "workHistory", type: "text" },
              { label: "学术任职", key: "academicPosition", type: "text" },
            ],
          },
        },
      ],
      tabCurrent: 0,
      idList: [],
      index: 0,
      actionList: [],
      actionListLength: false,
      showAction: false,
      btnLoading: false,
      loading: false,
      text: "提示：测试文本",
      tableKey: "",
      timeKey: +new Date(),
      dataForm: {
        id: "",
        name: undefined,
        sex: "",
        picture: [],
        phone: undefined,
        nativePlace: undefined,
        idCard: undefined,
        birthdate: undefined,
        nation: "",
        politicalGroup: undefined,
        address: undefined,
        position: undefined,
        technicalTitle: undefined,
        vocationalStatus: undefined,
        certificate: [],
        graduationSchool: undefined,
        graduationTime: undefined,
        major: undefined,
        degree: undefined,
        educationLevel: undefined,
        workType: undefined,
        workExperienceYears: undefined,
        majorField: undefined,
        remarks: undefined,
        province: [],
        hospitalName: undefined,
        department: undefined,
        academicPosition: undefined,
        workHistory: undefined,
      },
      rules: {
        name: [
          {
            required: true,
            message: "姓名请输入",
          },
        ],
        sex: [
          {
            required: true,
            message: "性别请选择",
          },
        ],
        phone: [
          {
            pattern: /^1[3456789]\d{9}$|^0\d{2,3}-?\d{7,8}$/,
            message: "手机号请输入正确的联系方式",
          },
        ],
        idCard: [
          {
            pattern:
              /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            message: "身份证号请输入正确的身份证号码",
          },
        ],
        birthdate: [
          {
            required: true,
            message: "出生日期请选择",
          },
        ],
      },
      activetab: 0,
      activetabData: [
        {
          title: "个人信息",
        },
        {
          title: "专业方向",
        },
        {
          title: "工作经历",
        },
      ],
      labelwidth: 100 * 2.5,
      menuId: "",
      jurisdictionType: "",
      btnList: [],
      ruleList: {},
      ableRelation: {},
      regList: {},
      ableAll: {},
      childIndex: -1,
      dataValue: {},
      isEdit: false,
      userInfo: {},
      content: "",
      title: "",
      show: false,
      formatType: {
        yyyy: "yyyy",
        "yyyy-MM": "yyyy-mm",
        "yyyy-MM-dd": "yyyy-mm-dd",
        "yyyy-MM-dd HH:mm": "yyyy-mm-dd hh:MM",
        "yyyy-MM-dd HH:mm:ss": "yyyy-mm-dd hh:MM:ss",
        "HH:mm:ss": "hh:MM:ss",
        "HH:mm": "hh:MM",
        YYYY: "yyyy",
        "YYYY-MM": "yyyy-mm",
        "YYYY-MM-DD": "yyyy-mm-dd",
        "YYYY-MM-DD HH:mm": "yyyy-mm-dd hh:MM",
        "YYYY-MM-DD HH:mm:ss": "yyyy-mm-dd hh:MM:ss",
      },
      optionsObj: {
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        sexProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        nationProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        sexOptions: [],
        nationOptions: [],
      },
      pictureList: [],
    };
  },
  computed: {
    callPhone() {
      return this.dataForm.phone;
    },
    picture() {
      return this.dataForm.picture
        ? this.define.baseURL + this.dataForm.picture
        : this.define.baseURL + "/api/file/Image/userAvatar/001.png";
    },
    technicalTitle() {
      return this.dataForm.technicalTitle;
    },
    majorField() {
      return this.dataForm.majorField;
    },
  },
  created() {
    uni.$on("linkPageConfirm", (subVal) => {
      if (this.tableKey) {
        for (let i = 0; i < subVal.length; i++) {
          let t = subVal[i];
          if (this["get" + this.tableKey]) {
            this["get" + this.tableKey](t);
          }
        }
        this.childIndex = -1;
        this.collapse();
      }
    });
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onLoad(option) {
    this.btnList = option.btnList.split(",");
    this.jurisdictionType = option.jurisdictionType;
    this.menuId = option.menuId;
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.dataForm.id = option.id || "";
    let _title = "";
    if (option.jurisdictionType == "btn_edit") {
      _title = "编辑";
    }
    if (option.jurisdictionType == "btn_detail") {
      _title = "详情";
    }
    if (option.jurisdictionType == "btn_add") {
      _title = "新增";
    }
    if (_title) {
      uni.setNavigationBarTitle({
        title: _title,
      });
    }
    this.dataAll();
    this.initData();
    this.dataValue = JSON.parse(JSON.stringify(this.dataForm));
    this.idList = option.idList ? option.idList.split(",") : [];
    for (let i = 0; i < this.idList.length; i++) {
      if (this.idList[i] == option.id) {
        this.index = i;
      }
    }
    setTimeout(() => {
      uni.$emit("initCollapse");
    }, 50);
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onShow() {

  },
  onReady() {

  },
  watch: {

  },
  methods: {
    getProvinceSelectorInfoList,


    resetForm() {
      uni.navigateBack();
    },
    dataAll() {
      getPhysicianAllDictionaryData(this.optionsObj);
      this.collapse();
    },
    clickActivetab(index) {
      this.activetab = index;
      this.timeKey = +new Date();
      this.collapse();
      setTimeout(() => {
        uni.$emit("initCollapse");
      }, 50);
    },
    initData() {
      this.$nextTick(function () {
        if (this.dataForm.id) {
          this.loading = true;
          getDetail(this.dataForm.id).then((res) => {
            this.dataInfo(res.data);
            this.loading = false;
          });
        }
      });
    },
    submitForm() {
      uni.navigateTo({
        url:
          "./form?menuId=" +
          this.menuId +
          "&jurisdictionType=btn_edit&id=" +
          this.dataForm.id +
          "&idList=" +
          this.idList,
      });
    },

    dataInfo(dataAll) {
      let _dataAll = dataAll;
      this.dataForm = _dataAll;
      if (_dataAll.phone) {
        this.callPhone = true;
      }
      if (_dataAll.provinceAndCity) {
        if (Array.isArray(_dataAll.provinceAndCity) && _dataAll.provinceAndCity.length > 0) {
          this.getProvinceSelectorInfoList([_dataAll.provinceAndCity]).then(res => {
            const list = res.data
            let txt = ''
            for (let i = 0; i < list.length; i++) {
              txt += (i ? ',' : '') + list[i].join('/')
            }
            this.dataForm.provinceAndCityString = txt
          })
        }
      }
      this.collapse();
    },
    collapse() {
      setTimeout(() => {
        this.$refs.activetab && this.$refs.activetab.init();
      }, 50);
    },
    //拨打电话
    telPhone(phone) {
      if (this.callPhone) {
        uni.showModal({
          title: "拨打电话提示",
          content: "您即将拨打电话：" + phone + ",是否继续拨打",
          success: (res) => {
            if (res.confirm) {
              uni.makePhoneCall({
                phoneNumber: phone,
                success: function () {
                  console.log("拨打电话成功");
                },
                fail: function () {
                  console.log("拨打电话失败");
                },
              });
            } else if (res.cancel) {
              // 用户点击了取消按钮
              console.log("用户取消了授权");
            }
          },
        });
      }
    },
    doPreviewImage(url) {
      uni.previewImage({
        urls: [this.picture],
        current: this.picture,
        success: () => { },
        fail: () => {
          uni.showToast({
            title: "预览图片失败",
            icon: "none",
          });
        },
      });
    },
  },
};
</script>
<style>
page {
  background-color: #f0f2f6;
}
</style>
