<template>
  <view class="questionnaire-detail-fillout-container" v-if="!loading">
    <!-- 顶部问卷信息卡片 -->
    <view class="questionnaire-header-card">
      <view class="questionnaire-avatar-section">
        <view class="questionnaire-basic-info">
          <view class="questionnaire-title">{{
            dataForm.questionnaireTitle || "未命名问卷"
            }}</view>
          <view class="questionnaire-meta">
            <view class="meta-item">
              <text class="meta-label">患者:</text>
              <text class="meta-value">{{
                dataForm.patientName || "未知患者"
                }}</text>
            </view>
            <view class="meta-item" v-if="mode === 'detail'">
              <text class="meta-label">状态:</text>
              <text class="meta-value" :class="getStatusClass()">{{
                getStatusText()
                }}</text>
            </view>
            <view class="meta-item" v-else>
              <text class="meta-label">住院号:</text>
              <text class="meta-value">{{
                dataForm.patientAdmissionNo || "暂无"
                }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 详细信息区域 -->
    <!-- 问卷内容 -->
    <view class="detail-section">
      <view class="section-content">
        <QuestionnaireAnswer v-model:answers="dataForm.answers" :readonly="mode === 'detail'"></QuestionnaireAnswer>
      </view>
    </view>
  </view>

  <!-- 底部操作按钮 -->
  <view class="action-buttons">
    <view class="action-row" v-if="mode === 'detail'">
      <view class="action-btn secondary-btn" @click="resetForm">
        <view class="btn-icon">↩️</view>
        <text class="btn-text">返回</text>
      </view>
      <view class="action-btn primary-btn" @click.stop="submitForm" v-if="btnList.includes('btn_edit')">
        <view class="btn-icon">✏️</view>
        <text class="btn-text">编辑</text>
      </view>
      <view class="action-btn primary-btn" @click.stop="fillOut"
        v-if="btnList.includes('btn_fill_out') && !dataForm.isAnswered">
        <view class="btn-icon">✍️</view>
        <text class="btn-text">填写</text>
      </view>
    </view>

    <view class="action-row" v-else>
      <view class="action-btn secondary-btn" @click="resetForm">
        <view class="btn-icon">↩️</view>
        <text class="btn-text">取消</text>
      </view>
      <view class="action-btn primary-btn" @click="handleSubmit" :loading="btnLoading">
        <view class="btn-icon">✅</view>
        <text class="btn-text">确定</text>
      </view>
    </view>
  </view>

  <u-modal v-model="show" :content="content" width="70%" border-radius="16" :content-style="{
    fontSize: '28rpx',
    padding: '20rpx',
    lineHeight: '44rpx',
    textAlign: 'left',
  }" :titleStyle="{ padding: '20rpx' }" :confirm-style="{ height: '80rpx', lineHeight: '80rpx' }" :title="title"
    confirm-text="确定">
  </u-modal>
</template>

<script>
import { buildUUID } from "@/utils/uuid";
import { useBaseStore } from "@/store/modules/base";
import { getPhysicianAllDictionaryData } from "@/pages/flow-up/physician/index.js";
import {
  getDetail,
  getForEdit,
  createQuestionnaireRecord,
  doQuestionnaireRecord,
} from "@/api/flow-up/questionnaireRecord";
import QuestionnaireAnswer from "@/components/gzbit/QuestionnaireAnswer.vue";

export default {
  components: {
    QuestionnaireAnswer,
  },
  data() {
    return {
      mode: "detail", // 'detail' 或 'fillOut'
      configList: [],
      tabCurrent: 0,
      idList: [],
      index: 0,
      actionList: [],
      actionListLength: false,
      showAction: false,
      btnLoading: false,
      loading: false,
      text: "提示：测试文本",
      tableKey: "",
      timeKey: +new Date(),
      dataForm: {
        id: "",
        questionnaireTitle: "",
        patientName: "",
        patientAdmissionNo: undefined,
        answers: [],
        isAnswered: false,
      },
      rules: {
        name: [
          {
            required: true,
            message: "姓名请输入",
          },
        ],
        sex: [
          {
            required: true,
            message: "性别请选择",
          },
        ],
        phone: [
          {
            pattern: /^1[3456789]\d{9}$|^0\d{2,3}-?\d{7,8}$/,
            message: "手机号请输入正确的联系方式",
          },
        ],
        idCard: [
          {
            pattern:
              /^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
            message: "身份证号请输入正确的身份证号码",
          },
        ],
        birthdate: [
          {
            required: true,
            message: "出生日期请选择",
          },
        ],
      },
      activetab: 0,
      activetabData: [
        {
          title: "问卷信息",
        },
      ],
      labelwidth: 100 * 2.5,
      menuId: "",
      jurisdictionType: "",
      btnList: [],
      ruleList: {},
      ableRelation: {},
      regList: {},
      ableAll: {},
      childIndex: -1,
      dataValue: {},
      isEdit: false,
      userInfo: {},
      content: "",
      title: "",
      show: false,
      formatType: {
        yyyy: "yyyy",
        "yyyy-MM": "yyyy-mm",
        "yyyy-MM-dd": "yyyy-mm-dd",
        "yyyy-MM-dd HH:mm": "yyyy-mm-dd hh:MM",
        "yyyy-MM-dd HH:mm:ss": "yyyy-mm-dd hh:MM:ss",
        "HH:mm:ss": "hh:MM:ss",
        "HH:mm": "hh:MM",
        YYYY: "yyyy",
        "YYYY-MM": "yyyy-mm",
        "YYYY-MM-DD": "yyyy-mm-dd",
        "YYYY-MM-DD HH:mm": "yyyy-mm-dd hh:MM",
        "YYYY-MM-DD HH:mm:ss": "yyyy-mm-dd hh:MM:ss",
      },
      optionsObj: {
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        sexProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        nationProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        sexOptions: [],
        nationOptions: [],
      },
      pictureList: [],
    };
  },
  computed: {
    technicalTitle() {
      return this.dataForm.technicalTitle;
    },
    majorField() {
      return this.dataForm.majorField;
    },
  },
  created() {
    uni.$on("linkPageConfirm", (subVal) => {
      if (this.tableKey) {
        for (let i = 0; i < subVal.length; i++) {
          let t = subVal[i];
          if (this["get" + this.tableKey]) {
            this["get" + this.tableKey](t);
          }
        }
        this.childIndex = -1;
        this.collapse();
      }
    });
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onLoad(option) {
    // 判断是详情模式还是填写模式
    if (option.jurisdictionType === "btn_edit") {
      this.mode = "fillOut";
    } else {
      this.mode = "detail";
    }

    if (this.mode === "detail") {
      this.btnList = option.btnList ? option.btnList.split(",") : [];
      this.jurisdictionType = option.jurisdictionType;
    } else {
      this.jurisdictionType = option.jurisdictionType;
    }

    this.menuId = option.menuId;
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.dataForm.id = option.id || "";

    let _title = "";
    if (this.mode === "fillOut") {
      _title = "填写问卷";
    } else {
      if (this.jurisdictionType == "btn_edit") {
        _title = "编辑";
      }
      if (this.jurisdictionType == "btn_detail") {
        _title = "详情";
      }
      if (this.jurisdictionType == "btn_add") {
        _title = "新增";
      }
    }

    if (_title) {
      uni.setNavigationBarTitle({
        title: _title,
      });
    }

    this.selfInit();
    this.dataAll();
    this.initData();
    this.dataValue = JSON.parse(JSON.stringify(this.dataForm));
    this.idList = option.idList ? option.idList.split(",") : [];
    for (let i = 0; i < this.idList.length; i++) {
      if (this.idList[i] == option.id) {
        this.index = i;
      }
    }
    setTimeout(() => {
      uni.$emit("initCollapse");
    }, 50);
    uni.$on("initCollapse", () => {
      //初始化折叠面板高度高度
      this.collapse();
    });
  },
  onShow() {
    this.dataAll();
    this.initData();
  },
  onReady() {
    setTimeout(() => {
      this.$refs.dataFormRef && this.$refs.dataFormRef.setRules(this.rules);
    }, 1000);
  },
  watch: {
    dataForm: {
      handler(val, oldVal) { },
      deep: true,
    },
  },
  methods: {
    // 获取头像文字
    getAvatarText(name) {
      if (!name) return "问";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取状态文本
    getStatusText() {
      return this.dataForm.isAnswered ? "已填写" : "未填写";
    },

    // 获取状态样式类
    getStatusClass() {
      return this.dataForm.isAnswered ? "status-completed" : "status-pending";
    },

    resetForm() {
      uni.navigateBack();
    },

    dataAll() {
      if (this.mode === "detail") {
        this.configList = [
          {
            label: "问卷标题",
            key: "questionnaireTitle",
          },
          {
            label: "问卷患者",
            key: "patientName",
          },
          {
            label: "问卷内容",
            key: "answers",
          },
        ];
      } else {
        this.configList = [
          {
            label: "随访患者住院号",
            key: "patientAdmissionNo",
          },
          {
            label: "随访患者",
            key: "patientName",
          },
          {
            label: "问卷内容",
            key: "answers",
          },
        ];
      }

      getPhysicianAllDictionaryData(this.optionsObj);
      this.collapse();
    },

    clickActivetab(index) {
      this.activetab = index;
      this.timeKey = +new Date();
      this.collapse();
      setTimeout(() => {
        uni.$emit("initCollapse");
      }, 50);
    },

    initData() {
      this.$nextTick(function () {
        if (this.dataForm.id) {
          this.loading = true;
          if (this.mode === "detail") {
            getDetail(this.dataForm.id).then((res) => {
              this.dataInfo(res.data);
              this.loading = false;
            });
          } else {
            getForEdit(this.dataForm.id).then((res) => {
              this.dataInfo(res.data);
              this.loading = false;
            });
          }
        }
      });
    },

    submitForm() {
      uni.navigateTo({
        url:
          "./detailFillOut?menuId=" +
          this.menuId +
          "&jurisdictionType=btn_edit&id=" +
          this.dataForm.id +
          "&idList=" +
          this.idList,
      });
    },

    fillOut() {
      let btnList = [];
      btnList.push("btn_detail");
      btnList.push("btn_fill_out");
      uni.navigateTo({
        url:
          "./doQuestionnaireRecord?menuId=" +
          this.menuId +
          "&jurisdictionType=btn_edit&id=" +
          this.dataForm.id +
          "&idList=" +
          btnList,
      });
    },

    dataInfo(dataAll) {
      let _dataAll = dataAll;
      this.dataForm = _dataAll;
      this.collapse();
    },

    collapse() {
      setTimeout(() => {
        this.$refs.activetab && this.$refs.activetab.init();
      }, 50);
    },

    // 填写模式相关方法
    selfInit() {
      useBaseStore().updateRelationData({});
    },

    selfGetInfo(dataForm) {
      this.dataForm.id = this.dataForm.id;
      this.dataInfo(dataForm);
    },

    dataList() {
      var _data = this.dataForm;
      return _data;
    },

    vilidate() {
      if (!this.dataForm) return;

      const radioQuestions = this.dataForm.answers.filter((item) => item.type === "radio");
      const checkboxQuestions = this.dataForm.answers.filter((item) => item.type === "checkbox");
      const textAreaQuestions = this.dataForm.answers.filter((item) => item.type === "textarea");

      for (let i = 0; i < radioQuestions.length; i++) {
        let question = radioQuestions[i];
        if (!question.required) {
          continue;
        }
        if (
          !question.answer ||
          question.answer == [] ||
          question.answer == ""
        ) {
          this.$u.toast("请选择单选题第" + (i + 1) + "题的答案！");
          return false;
        }
      }


      for (let i = 0; i < checkboxQuestions.length; i++) {
        let question = checkboxQuestions[i];
        if (!question.required) {
          continue;
        }
        if (
          !question.selectAnswer ||
          question.selectAnswer == [] ||
          question.selectAnswer == ""
        ) {
          this.$u.toast("请选择多选题第" + (i + 1) + "题的答案！");
          return false;
        }
      }

      for (let i = 0; i < textAreaQuestions.length; i++) {
        let question = textAreaQuestions[i];
        if (!question.required) {
          continue;
        }
        if (
          !question.answer ||
          question.answer == [] ||
          question.answer == ""
        ) {
          this.$u.toast("请填写简答题第" + (i + 1) + "题的答案！");
          return false;
        }
      }

      return true;
    },

    handleSubmit() {
      if (!this.vilidate()) return;
      uni.showModal({
        title: "提示",
        content: "确认提交吗？",
        success: (res) => {
          if (res.confirm) {
            this.submitFormFillOut();
          }
        },
      });
    },

    submitFormFillOut() {
      var _data = this.dataList();
      if (this.$refs.dataFormRef) {
        this.$refs.dataFormRef.validate((valid) => {
          if (!valid) return;
          if (!!this.exist()) return this.$u.toast(this.exist());
          this.btnLoading = true;
          if (this.dataForm.id) {
            doQuestionnaireRecord(_data)
              .then((res) => {
                uni.showToast({
                  title: res.msg,
                  complete: () => {
                    setTimeout(() => {
                      uni.$emit("refresh");
                      uni.navigateBack();
                    }, 1500);
                  },
                });
              })
              .catch(() => {
                this.btnLoading = false;
              });
          }
        });
      } else {
        // 如果没有表单引用，直接提交
        this.btnLoading = true;
        if (this.dataForm.id) {
          doQuestionnaireRecord(_data)
            .then((res) => {
              uni.showToast({
                title: res.msg,
                complete: () => {
                  setTimeout(() => {
                    uni.$emit("refresh");
                    uni.navigateBack();
                  }, 1500);
                },
              });
            })
            .catch(() => {
              this.btnLoading = false;
            });
        }
      }
    },

    exist() {
      let title = [];
      let _ruleList = this.ruleList;
      for (let k in _ruleList) {
        let childData = this.dataForm[k];
        childData.forEach((item, index) => {
          for (let model in _ruleList[k]) {
            if (item[model] instanceof Array) {
              if (item[model].length == 0) {
                title.push(_ruleList[k][model]);
              }
            } else if (!item[model] && item[model] !== 0) {
              title.push(_ruleList[k][model]);
            }
          }
        });
      }
      let _regList = this.regList;
      for (let k in _regList) {
        let childData = this.dataForm[k];
        for (let n in _regList[k]) {
          for (let i = 0; i < _regList[k][n].length; i++) {
            const element = _regList[k][n][i];
            if (element.pattern) {
              element.pattern = element.pattern.toString();
              let start = element.pattern.indexOf("/");
              let stop = element.pattern.lastIndexOf("/");
              let str = element.pattern.substring(start + 1, stop);
              let reg = new RegExp(str);
              element.pattern = reg;
            }
            childData.forEach((item, index) => {
              if (item[n] && !element.pattern.test(item[n])) {
                title.push(element.message);
              }
            });
          }
        }
      }
      if (title.length > 0) {
        return title[0];
      }
    },
  },
};
</script>

<style lang="scss">
page {
  background-color: #f0f2f6;
}

.questionnaire-detail-fillout-container {
  min-height: 100vh;
  background-color: #f0f2f6;
  padding-bottom: 180rpx;

  // 顶部问卷信息卡片
  .questionnaire-header-card {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    border-radius: 0 0 40rpx 40rpx;
    padding: 40rpx 32rpx;
    box-shadow: 0 12rpx 32rpx rgba(25, 118, 210, 0.3);
    margin-bottom: 32rpx;

    .questionnaire-avatar-section {
      display: flex;
      align-items: center;
      gap: 24rpx;

      .questionnaire-avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10rpx);
        border: 2rpx solid rgba(255, 255, 255, 0.3);

        .avatar-text {
          color: #fff;
          font-size: 40rpx;
          font-weight: 700;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }
      }

      .questionnaire-basic-info {
        flex: 1;
        color: #fff;

        .questionnaire-title {
          font-size: 36rpx;
          font-weight: 700;
          margin-bottom: 16rpx;
          text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
        }

        .questionnaire-meta {
          display: flex;
          gap: 32rpx;

          .meta-item {
            .meta-label {
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.8);
              margin-right: 8rpx;
            }

            .meta-value {
              font-size: 28rpx;
              font-weight: 600;

              &.status-completed {
                color: #a5d6a7;
              }

              &.status-pending {
                color: #ffcdd2;
              }
            }
          }
        }
      }
    }
  }

  // 详细信息区域
  .detail-sections {
    padding: 0 24rpx;

    .detail-section {
      background: #fff;
      border-radius: 24rpx;
      margin-bottom: 24rpx;
      box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.1);
      overflow: hidden;

      .section-header {
        display: flex;
        align-items: center;
        gap: 16rpx;
        padding: 24rpx 32rpx;
        background: linear-gradient(to right, #1976d2, #2196f3);
        color: #fff;

        .section-icon {
          font-size: 32rpx;
        }

        .section-title {
          font-size: 32rpx;
          font-weight: 600;
        }
      }

      .section-content {
        padding: 24rpx;
      }
    }
  }
}

// 底部操作按钮
.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 24rpx 20rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 32rpx rgba(25, 118, 210, 0.15);
  border-top: 1rpx solid rgba(25, 118, 210, 0.1);

  .action-row {
    display: flex;
    gap: 16rpx;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .action-btn {
      flex: 1;
      height: 88rpx;
      border-radius: 20rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      border: 2rpx solid transparent;

      .btn-icon {
        font-size: 28rpx;
      }

      .btn-text {
        font-size: 28rpx;
      }

      &.primary-btn {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: #fff;
        box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.3);

        &:active {
          transform: translateY(2rpx);
          box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.3);
        }
      }

      &.secondary-btn {
        background: #f8f9fa;
        color: #6c757d;
        border-color: #dee2e6;

        &:active {
          background: #e9ecef;
        }
      }

      &.full-width-btn {
        flex: 100%;
      }
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .questionnaire-detail-fillout-container {
    padding: 16rpx;
    padding-bottom: 180rpx;

    .questionnaire-header-card {
      padding: 30rpx 24rpx;
      margin-bottom: 24rpx;

      .questionnaire-avatar-section {
        gap: 16rpx;

        .questionnaire-avatar {
          width: 90rpx;
          height: 90rpx;

          .avatar-text {
            font-size: 32rpx;
          }
        }

        .questionnaire-basic-info {
          .questionnaire-title {
            font-size: 32rpx;
            margin-bottom: 12rpx;
          }

          .questionnaire-meta {
            gap: 20rpx;

            .meta-item {
              .meta-label {
                font-size: 22rpx;
              }

              .meta-value {
                font-size: 26rpx;
              }
            }
          }
        }
      }
    }

    .detail-sections {
      padding: 0 16rpx;

      .detail-section {
        margin-bottom: 16rpx;

        .section-header {
          padding: 20rpx 24rpx;

          .section-icon {
            font-size: 28rpx;
          }

          .section-title {
            font-size: 28rpx;
          }
        }

        .section-content {
          padding: 20rpx;
        }
      }
    }

    .action-buttons {
      padding: 20rpx 16rpx;

      .action-row {
        gap: 12rpx;

        .action-btn {
          height: 80rpx;

          .btn-icon {
            font-size: 24rpx;
          }

          .btn-text {
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>