<template>
  <view>
    <!-- 卡片 -->
    <template v-if="item.show && item.xundaKey === 'card'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <view class="card-inner u-p-l-8 u-p-r-8 u-p-t-8">
              <Item
                v-for="(child, index) in item.children"
                :item="child"
                :key="index"
              />
            </view>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 排行榜 -->
    <template v-if="item.show && item.xundaKey === 'rankList'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HRankList :config="item"></HRankList>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 文本 -->
    <template v-if="item.show && item.xundaKey === 'text'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HText :config="item"></HText>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 图片 -->
    <template v-if="item.show && item.xundaKey === 'image'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HImage :config="item"></HImage>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 轮播图 -->
    <template v-if="item.show && item.xundaKey === 'carousel'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HCarousel :config="item"></HCarousel>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 视频 -->
    <template v-if="item.show && item.xundaKey === 'video'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HVideo :config="item"></HVideo>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 图表 -->
    <template
      v-if="
        item.show &&
        (item.xundaKey === 'barChart' ||
          item.xundaKey === 'lineChart' ||
          item.xundaKey === 'pieChart' ||
          item.xundaKey == 'radarChart' ||
          item.xundaKey === 'mapChart')
      "
    >
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HCharts :config="item" :key="key"></HCharts>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 自定义echarts -->
    <template v-if="item.show && item.xundaKey === 'customEcharts'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HCustomeCharts :config="item" :key="key"></HCustomeCharts>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 我的待办 -->
    <template v-if="item.show && item.xundaKey === 'todo'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HTodo :config="item" :key="key"></HTodo>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 常用功能 -->
    <template v-if="item.show && item.xundaKey === 'dataBoard'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HDataBoard :config="item" :key="key"></HDataBoard>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 数据面板 -->
    <template v-if="item.show && item.xundaKey === 'commonFunc'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HCommonFunc :config="item" :key="key"></HCommonFunc>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 时间轴 -->
    <template v-if="item.show && item.xundaKey === 'timeAxis'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HTimeAxis :config="item"></HTimeAxis>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 表格 -->
    <template v-if="item.show && item.xundaKey === 'tableList'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HTable :config="item"></HTable>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 待办事项 -->
    <template v-if="item.show && item.xundaKey === 'todoList'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HTodoList :config="item"></HTodoList>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 未读邮件 -->
    <template v-if="item.show && item.xundaKey === 'email'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HEmail :config="item"></HEmail>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 公告通知 -->
    <template v-if="item.show && item.xundaKey === 'notice'">
      <view class="u-m-b-20">
        <HCard :cardData="item">
          <template #content>
            <HNotice :config="item"></HNotice>
          </template>
        </HCard>
      </view>
    </template>
    <!-- 标签 -->
    <template v-if="item.show && item.xundaKey === 'tab'">
      <view class="u-m-b-20" style="background-color: #ffffff">
        <u-tabs
          :list="item.children"
          name="title"
          :is-scroll="item.children.length > 3 ? true : false"
          v-model="tabCurrent"
          @change="onTabChange"
          :show-bar="item.type ? false : true"
          :class="tabsClass"
          :inactive-color="item.type === 'border-card' ? ' #9ea1a6' : '#303133'"
          :active-item-style="activeItemStyle"
          :bg-color="item.type === 'border-card' ? '#f5f7fa' : '#fff'"
        >
        </u-tabs>
        <view
          v-for="(item, i) in item.children"
          :key="i"
          class="tab-inner u-p-l-8 u-p-r-8 u-p-b-8 u-p-t-8"
        >
          <view v-show="i == tabCurrent">
            <Item
              v-for="(child, index) in item.children"
              :item="child"
              :key="key"
            />
          </view>
        </view>
      </view>
    </template>
  </view>
</template>
<script>
import HCard from "./HCard";
import HDataBoard from "./HDataBoard";
import HTable from "./HTable";
import HNotice from "./HNotice";
import HEmail from "./HEmail";
import HTodoList from "./HTodoList";
import HCharts from "./HCharts";
import HCustomeCharts from "./HCustomeCharts";
import HRankList from "./HRankList";
import HImage from "./HImage";
import HCarousel from "./HCarousel";
import HText from "./HText";
import HVideo from "./HVideo";
import HTodo from "./HTodo";
import HCommonFunc from "./HCommonFunc";
import HTimeAxis from "./HTimeAxis";
export default {
  name: "Item",
  props: {
    item: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    HCard,
    HDataBoard,
    HTable,
    HNotice,
    HEmail,
    HTodoList,
    HCharts,
    HRankList,
    HImage,
    HCarousel,
    HCustomeCharts,
    HText,
    HVideo,
    HTodo,
    HCommonFunc,
    HTimeAxis,
  },
  data() {
    return {
      cardData: {},
      current: 0,
      tabCurrent: 0,
      key: +new Date(),
      tabsClass: "",
      activeItemStyle: {
        "background-color": "#fff",
      },
    };
  },
  created() {
    if (this.item.xundaKey === "tab") {
      const list = this.item.children;
      for (let i = 0; i < list.length; i++) {
        if (this.item.active == list[i].name) {
          this.tabCurrent = i;
          break;
        }
      }
      if (this.item.type === "border-card" || this.item.type === "card") {
        this.tabsClass = "htabs";
      }
    }
  },
  methods: {
    change(index) {
      this.current = index;
    },
    onTabChange(index) {
      if (this.tabCurrent === index) return;
      this.tabCurrent = index;
      this.key = +new Date();
    },
  },
};
</script>

<style lang="scss"></style>
