@font-face {
	font-family: 'icon-ym';
	/* Project id 3271933 */
	src: url('https://at.alicdn.com/t/c/font_3271933_5ls6rfb4d6e.woff2?t=1716364842247') format('woff2'),
		url('https://at.alicdn.com/t/c/font_3271933_5ls6rfb4d6e.woff?t=1716364842247') format('woff'),
		url('https://at.alicdn.com/t/c/font_3271933_5ls6rfb4d6e.ttf?t=1716364842247') format('truetype');
}

.icon-ym {
	font-family: "icon-ym" !important;
	font-size: 16px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.icon-ym-hLine:before {
	content: "\e951";
}

.icon-ym-vLine:before {
	content: "\e953";
}

.icon-ym-header-notice:before {
	content: "\e95f";
}

.icon-ym-header-fullscreen1:before {
	content: "\e917";
}

.icon-ym-cell:before {
	content: "\e928";
}

.icon-ym-header-search:before {
	content: "\e92e";
}

.icon-ym-header-config:before {
	content: "\e92f";
}

.icon-ym-flow-minus1:before {
	content: "\e932";
}

.icon-ym-flow-plus:before {
	content: "\e954";
}

.icon-ym-header-fullscreen-exit1:before {
	content: "\e956";
}

.icon-ym-flowToSign:before {
	content: "\e95d";
}

.icon-ym-flowDoing:before {
	content: "\e95e";
}

.icon-ym-header-chat:before {
	content: "\e907";
}

.icon-ym-header-sys-toggle:before {
	content: "\e90d";
}

.icon-ym-schedule:before {
	content: "\e910";
}

.icon-ym-caret-left:before {
	content: "\e95c";
}

.icon-ym-btn-export1:before {
	content: "\e959";
}

.icon-ym-caret-right:before {
	content: "\e95a";
}

.icon-ym-btn-import1:before {
	content: "\e95b";
}

.icon-ym-rect:before {
	content: "\e955";
}

.icon-ym-combination:before {
	content: "\e957";
}

.icon-ym-oval:before {
	content: "\e958";
}

.icon-ym-header-star-fill:before {
	content: "\e952";
}

.icon-ym-flow-align-right:before {
	content: "\e934";
}

.icon-ym-flow-align-bottom:before {
	content: "\e935";
}

.icon-ym-flow-aerial-view:before {
	content: "\e938";
}

.icon-ym-flow-align-middle:before {
	content: "\e949";
}

.icon-ym-flow-align-center:before {
	content: "\e94a";
}

.icon-ym-beautify-vertical:before {
	content: "\e950";
}

.icon-ym-flow-align-top:before {
	content: "\e90a";
}

.icon-ym-flow-node-timer1:before {
	content: "\e911";
}

.icon-ym-flow-focus:before {
	content: "\e915";
}

.icon-ym-tip:before {
	content: "\e923";
}

.icon-ym-flow-shortcut:before {
	content: "\e92b";
}

.icon-ym-flow-align-left:before {
	content: "\e92c";
}

.icon-ym-beautify-horizontal:before {
	content: "\e930";
}

.icon-ym-flow-move:before {
	content: "\e936";
}

.icon-ym-flow-node-subFlow:before {
	content: "\e93a";
}

.icon-ym-flow-node-end:before {
	content: "\e93d";
}

.icon-ym-flow-node-branch:before {
	content: "\e943";
}

.icon-ym-flow-node-system-task:before {
	content: "\e904";
}

.icon-ym-flow-node-condition:before {
	content: "\e919";
}

.icon-ym-flow-selection:before {
	content: "\e91d";
}

.icon-ym-flow-history:before {
	content: "\e922";
}

.icon-ym-redo:before {
	content: "\e925";
}

.icon-ym-undo:before {
	content: "\e927";
}

.icon-ym-flow-node-timer:before {
	content: "\e92a";
}

.icon-ym-flow-node-approve:before {
	content: "\e92d";
}

.icon-ym-flow-node-interflow:before {
	content: "\e933";
}

.icon-ym-recovery:before {
	content: "\e94f";
}

.icon-ym-share-cancel:before {
	content: "\e94e";
}

.icon-ym-upload1:before {
	content: "\e91f";
}

.icon-ym-add-folder:before {
	content: "\e94d";
}

.icon-ym-tile-mode:before {
	content: "\e94b";
}

.icon-ym-folder:before {
	content: "\e94c";
}

.icon-ym-app-more:before {
	content: "\e916";
}

.icon-ym-add-cancel:before {
	content: "\e940";
}

.icon-ym-app-rename:before {
	content: "\e941";
}

.icon-ym-flow-node-start:before {
	content: "\e942";
}

.icon-ym-app-role-toggle:before {
	content: "\e944";
}

.icon-ym-auth-group:before {
	content: "\e945";
}

.icon-ym-app-delete:before {
	content: "\e946";
}

.icon-ym-app-share:before {
	content: "\e947";
}

.icon-ym-app-schedule:before {
	content: "\e948";
}

.icon-ym-dark:before {
	content: "\e937";
}

.icon-ym-app-todo:before {
	content: "\e939";
}

.icon-ym-app-download:before {
	content: "\e93b";
}

.icon-ym-flow-minus:before {
	content: "\e93c";
}

.icon-ym-app-move:before {
	content: "\e93e";
}

.icon-ym-app-document:before {
	content: "\e93f";
}

.icon-ym-header-star:before {
	content: "\e929";
}

.icon-ym-msg-read-all:before {
	content: "\e931";
}

.icon-ym-more-vertical:before {
	content: "\e91e";
}

.icon-ym-more:before {
	content: "\e920";
}

.icon-ym-header-fullscreen:before {
	content: "\e921";
}

.icon-ym-nav-home-fill:before {
	content: "\e924";
}

.icon-ym-tip-filled:before {
	content: "\e926";
}

.icon-ym-flow-line:before {
	content: "\e912";
}

.icon-ym-header-refresh:before {
	content: "\e913";
}

.icon-ym-shareMe-filled:before {
	content: "\e914";
}

.icon-ym-share-filled:before {
	content: "\e918";
}

.icon-ym-trash-filled:before {
	content: "\e91a";
}

.icon-ym-header-lang:before {
	content: "\e91b";
}

.icon-ym-app-entrust:before {
	content: "\e91c";
}

.icon-ym-release:before {
	content: "\e90e";
}

.icon-ym-star-filled:before {
	content: "\e90f";
}

.icon-ym-header-role-toggle:before {
	content: "\e906";
}

.icon-ym-light:before {
	content: "\e908";
}

.icon-ym-header-fullscreen-exit:before {
	content: "\e909";
}

.icon-ym-org-tree:before {
	content: "\e90b";
}

.icon-ym-app-checkAll:before {
	content: "\e90c";
}

.icon-ym-thumb-mode:before {
	content: "\e905";
}

.icon-ym-generator-steps:before {
	content: "\e7ed";
}

.icon-ym-portal-custom-echart:before {
	content: "\e903";
}

.icon-ym-signature1:before {
	content: "\e902";
}

.icon-ym-launch:before {
	content: "\e901";
}

.icon-ym-turn:before {
	content: "\e8fa";
}

.icon-ym-roll-call:before {
	content: "\e8fb";
}

.icon-ym-comment-img:before {
	content: "\e8fc";
}

.icon-ym-condition:before {
	content: "\e8fd";
}

.icon-ym-generator-iframe:before {
	content: "\e8fe";
}

.icon-ym-edit:before {
	content: "\e8ff";
}

.icon-ym-comment-file:before {
	content: "\e900";
}

.icon-ym-emoji:before {
	content: "\e8f7";
}

.icon-ym-generator-location:before {
	content: "\e8f8";
}

.icon-ym-chat:before {
	content: "\e8f9";
}

.icon-ym-synForThird:before {
	content: "\e8f6";
}

.icon-ym-authGroup:before {
	content: "\e8f2";
}

.icon-ym-fail:before {
	content: "\e8f3";
}

.icon-ym-success:before {
	content: "\e8f4";
}

.icon-ym-clock:before {
	content: "\e8f5";
}

.icon-ym-view:before {
	content: "\e8f1";
}

.icon-ym-shortcut:before {
	content: "\e8f0";
}

.icon-ym-liveData:before {
	content: "\e8ef";
}

.icon-ym-generator-autoComplete:before {
	content: "\e8eb";
}

.icon-ym-contacts:before {
	content: "\e8ec";
}

.icon-ym-portal-text:before {
	content: "\e8ed";
}

.icon-ym-portal-mapChart:before {
	content: "\e8ee";
}

.icon-ym-generator-tableGrid:before {
	content: "\e8db";
}

.icon-ym-portal-card:before {
	content: "\e8dc";
}

.icon-ym-portal-carousel:before {
	content: "\e8dd";
}

.icon-ym-generator-insertCol:before {
	content: "\e8de";
}

.icon-ym-generator-insertRow:before {
	content: "\e8df";
}

.icon-ym-btn-batch:before {
	content: "\e8e0";
}

.icon-ym-portal-collapse:before {
	content: "\e8e1";
}

.icon-ym-portal-rankList:before {
	content: "\e8e2";
}

.icon-ym-portal-image:before {
	content: "\e8e3";
}

.icon-ym-portal-tab:before {
	content: "\e8e4";
}

.icon-ym-portal-video:before {
	content: "\e8e5";
}

.icon-ym-generator-TableSettings:before {
	content: "\e8e6";
}

.icon-ym-btn-common:before {
	content: "\e8e7";
}

.icon-ym-portal-iframe:before {
	content: "\e8e8";
}

.icon-ym-portal-timeAxis:before {
	content: "\e8e9";
}

.icon-ym-portal-schedule:before {
	content: "\e8ea";
}

.icon-ym-flowEntrust-app:before {
	content: "\e8da";
}

.icon-ym-btn-download:before {
	content: "\e8d9";
}

.icon-ym-btn-upload:before {
	content: "\e8d7";
}

.icon-ym-right:before {
	content: "\e8d4";
}

.icon-ym-left:before {
	content: "\e8d5";
}

.icon-ym-arrow-down:before {
	content: "\e8d6";
}

.icon-ym-add-flow:before {
	content: "\e8d8";
}

.icon-ym-launchFlow:before {
	content: "\e8d2";
}

.icon-ym-funcFlow:before {
	content: "\e8d3";
}

.icon-ym-accountConfig:before {
	content: "\e8cb";
}

.icon-ym-sendConfig:before {
	content: "\e8cc";
}

.icon-ym-smsConfig:before {
	content: "\e8cd";
}

.icon-ym-emailConfig:before {
	content: "\e8ce";
}

.icon-ym-msgCenter:before {
	content: "\e8cf";
}

.icon-ym-msgTemplate:before {
	content: "\e8d0";
}

.icon-ym-msgMonitor:before {
	content: "\e8d1";
}

.icon-ym-logo-webhook:before {
	content: "\e8ca";
}

.icon-ym-logo-github:before {
	content: "\e8b0";
}

.icon-ym-logo-feishu:before {
	content: "\e8c5";
}

.icon-ym-logo-dingding:before {
	content: "\e8c6";
}

.icon-ym-logo-wechat:before {
	content: "\e8c7";
}

.icon-ym-logo-qq:before {
	content: "\e8c8";
}

.icon-ym-logo-wxWork:before {
	content: "\e8c9";
}

.icon-ym-position1:before {
	content: "\e8c3";
}

.icon-ym-generator-popupTableSelect:before {
	content: "\e8c4";
}

.icon-ym-gradeManage:before {
	content: "\e8c2";
}

.icon-ym-fold:before {
	content: "\e829";
}

.icon-ym-customForm:before {
	content: "\e857";
}

.icon-ym-branch:before {
	content: "\e8b1";
}

.icon-ym-codeGeneration1:before {
	content: "\e8b2";
}

.icon-ym-filter:before {
	content: "\e8b3";
}

.icon-ym-download:before {
	content: "\e8b4";
}

.icon-ym-functionForm:before {
	content: "\e8b5";
}

.icon-ym-unfold:before {
	content: "\e8b6";
}

.icon-ym-node:before {
	content: "\e8b7";
}

.icon-ym-nav-home:before {
	content: "\e8b8";
}

.icon-ym-systemForm:before {
	content: "\e8b9";
}

.icon-ym-generator-link:before {
	content: "\e8ba";
}

.icon-ym-timer:before {
	content: "\e8bb";
}

.icon-ym-generator-alert:before {
	content: "\e8bc";
}

.icon-ym-generator-group1:before {
	content: "\e8bd";
}

.icon-ym-generator-role:before {
	content: "\e8be";
}

.icon-ym-systemToggle:before {
	content: "\e8bf";
}

.icon-ym-upload:before {
	content: "\e8c0";
}

.icon-ym-generator-department1:before {
	content: "\e8c1";
}

.icon-ym-signature:before {
	content: "\e8af";
}

.icon-ym-yingyong:before {
	content: "\e81e";
}

.icon-ym-generator-section:before {
	content: "\e853";
}

.icon-ym-lianxi:before {
	content: "\e873";
}

.icon-ym-bigScreenExampl:before {
	content: "\e755";
}

.icon-ym-appForm:before {
	content: "\e756";
}

.icon-ym-authorize:before {
	content: "\e75c";
}

.icon-ym-dataApplication:before {
	content: "\e75d";
}

.icon-ym-dataDictionary:before {
	content: "\e76c";
}

.icon-ym-codeGeneration:before {
	content: "\e76f";
}

.icon-ym-documentPreviewExample:before {
	content: "\e77b";
}

.icon-ym-dataInterface:before {
	content: "\e78f";
}

.icon-ym-bigScreenDesign:before {
	content: "\e7a7";
}

.icon-ym-dataSync:before {
	content: "\e7ab";
}

.icon-ym-extend:before {
	content: "\e7b1";
}

.icon-ym-flowForm:before {
	content: "\e7b2";
}

.icon-ym-dataModel:before {
	content: "\e7bb";
}

.icon-ym-formExample:before {
	content: "\e7c8";
}

.icon-ym-department:before {
	content: "\e812";
}

.icon-ym-dataSource:before {
	content: "\e816";
}

.icon-ym-flowLaunch:before {
	content: "\e81b";
}

.icon-ym-flowForm1:before {
	content: "\e81f";
}

.icon-ym-formDesign:before {
	content: "\e827";
}

.icon-ym-highchartsWordcloud:before {
	content: "\e82c";
}

.icon-ym-dataBackup:before {
	content: "\e830";
}

.icon-ym-flowEntrust:before {
	content: "\e831";
}

.icon-ym-role:before {
	content: "\e832";
}

.icon-ym-information:before {
	content: "\e834";
}

.icon-ym-portalDesign:before {
	content: "\e83b";
}

.icon-ym-highchartsColumn:before {
	content: "\e83c";
}

.icon-ym-onlineUser:before {
	content: "\e83f";
}

.icon-ym-sysMonitor:before {
	content: "\e842";
}

.icon-ym-sysCache:before {
	content: "\e843";
}

.icon-ym-organization:before {
	content: "\e845";
}

.icon-ym-flowTodo:before {
	content: "\e847";
}

.icon-ym-pause:before {
	content: "\e849";
}

.icon-ym-report-icon-preview-pageIndex:before {
	content: "\e84c";
}

.icon-ym-sysProvince:before {
	content: "\e850";
}

.icon-ym-sysNotice:before {
	content: "\e851";
}

.icon-ym-wf-finishedProduct:before {
	content: "\e852";
}

.icon-ym-reportExample:before {
	content: "\e85d";
}

.icon-ym-wf-crmOrder:before {
	content: "\e864";
}

.icon-ym-signetExample:before {
	content: "\e866";
}

.icon-ym-position:before {
	content: "\e867";
}

.icon-ym-permission:before {
	content: "\e868";
}

.icon-ym-user:before {
	content: "\e871";
}

.icon-ym-wf-expenseExpenditure:before {
	content: "\e877";
}

.icon-ym-onlineDevelopment:before {
	content: "\e87a";
}

.icon-ym-sysBillRule:before {
	content: "\e88b";
}

.icon-ym-system:before {
	content: "\e892";
}

.icon-ym-reportDesign:before {
	content: "\e896";
}

.icon-ym-sysQuartz:before {
	content: "\e897";
}

.icon-ym-workFlow:before {
	content: "\e89e";
}

.icon-ym-sysIcon:before {
	content: "\e8a4";
}

.icon-ym-huibao:before {
	content: "\e8a5";
}

.icon-ym-webForm:before {
	content: "\e8a6";
}

.icon-ym-wf-staffOvertime:before {
	content: "\e8a7";
}

.icon-ym-tree-manage1:before {
	content: "\e8a8";
}

.icon-ym-webDesign:before {
	content: "\e8a9";
}

.icon-ym-wf-receiptSign:before {
	content: "\e8aa";
}

.icon-ym-windows:before {
	content: "\e8ab";
}

.icon-ym-systemTemplate:before {
	content: "\e8ac";
}

.icon-ym-sysLog:before {
	content: "\e8ad";
}

.icon-ym-sysConfig:before {
	content: "\e8ae";
}

.icon-ym-header-pannel:before {
	content: "\e7cf";
}

.icon-ym-search:before {
	content: "\e804";
}

.icon-ym-header-message:before {
	content: "\e810";
}

.icon-ym-generator-jobs:before {
	content: "\e819";
}

.icon-ym-clean:before {
	content: "\e83e";
}

.icon-ym-header-IM:before {
	content: "\e899";
}

.icon-ym-header-language:before {
	content: "\e8a3";
}

.icon-ym-flowDone-app:before {
	content: "\e7a6";
}

.icon-ym-flowLaunch-app:before {
	content: "\e7b3";
}

.icon-ym-addFlow:before {
	content: "\e7b8";
}

.icon-ym-flowCopy-app:before {
	content: "\e7bf";
}

.icon-ym-flowTodo-app:before {
	content: "\e8a2";
}

.icon-ym-mobile:before {
	content: "\e7dc";
}

.icon-ym-pc:before {
	content: "\e7ee";
}

.icon-ym-video-play:before {
	content: "\e8a1";
}

.icon-ym-header-collapse1:before {
	content: "\e809";
}

.icon-ym-xitong:before {
	content: "\e814";
}

.icon-ym-full-screen1:before {
	content: "\e7d0";
}

.icon-ym-file-html1:before {
	content: "\e7d1";
}

.icon-ym-generator-area:before {
	content: "\e7d2";
}

.icon-ym-generator-modifytime:before {
	content: "\e7d3";
}

.icon-ym-generator-fold:before {
	content: "\e7d4";
}

.icon-ym-generator-founder:before {
	content: "\e7d5";
}

.icon-ym-generator-line:before {
	content: "\e7d6";
}

.icon-ym-generator-department:before {
	content: "\e7d7";
}

.icon-ym-generator-function:before {
	content: "\e7d8";
}

.icon-ym-generator-notice:before {
	content: "\e7d9";
}

.icon-ym-generator-Provinces:before {
	content: "\e7da";
}

.icon-ym-generator-popup-attr:before {
	content: "\e7dd";
}

.icon-ym-generator-divider:before {
	content: "\e7de";
}

.icon-ym-generator-radio:before {
	content: "\e7df";
}

.icon-ym-generator-menu:before {
	content: "\e7e0";
}

.icon-ym-generator-popup:before {
	content: "\e7e1";
}

.icon-ym-generator-input:before {
	content: "\e7e2";
}

.icon-ym-generator-rich-text:before {
	content: "\e7e3";
}

.icon-ym-generator-Panel:before {
	content: "\e7e4";
}

.icon-ym-generator-table:before {
	content: "\e7e5";
}

.icon-ym-generator-radar:before {
	content: "\e7e6";
}

.icon-ym-generator-password:before {
	content: "\e7e7";
}

.icon-ym-generator-component:before {
	content: "\e7e8";
}

.icon-ym-generator-station:before {
	content: "\e7e9";
}

.icon-ym-generator-rate:before {
	content: "\e7ea";
}

.icon-ym-generator-subFlow1:before {
	content: "\e7eb";
}

.icon-ym-generator-number:before {
	content: "\e7ec";
}

.icon-ym-generator-flow:before {
	content: "\e7ef";
}

.icon-ym-generator-color:before {
	content: "\e7f0";
}

.icon-ym-generator-label:before {
	content: "\e7f1";
}

.icon-ym-generator-nature:before {
	content: "\e7f2";
}

.icon-ym-generator-select:before {
	content: "\e7f3";
}

.icon-ym-generator-slider:before {
	content: "\e7f4";
}

.icon-ym-generator-qrcode:before {
	content: "\e7f5";
}

.icon-ym-generator-group:before {
	content: "\e7f6";
}

.icon-ym-generator-time:before {
	content: "\e7f7";
}

.icon-ym-generator-user:before {
	content: "\e7f8";
}

.icon-ym-generator-modifier:before {
	content: "\e7f9";
}

.icon-ym-h5:before {
	content: "\e7fa";
}

.icon-ym-generator-tree:before {
	content: "\e7fb";
}

.icon-ym-gonggao:before {
	content: "\e7fc";
}

.icon-ym-generator-kanban:before {
	content: "\e7fd";
}

.icon-ym-generator-textarea:before {
	content: "\e7fe";
}

.icon-ym-generator-todo:before {
	content: "\e7ff";
}

.icon-ym-header-loginOut:before {
	content: "\e800";
}

.icon-ym-header-expand:before {
	content: "\e801";
}

.icon-ym-generator-time-range:before {
	content: "\e802";
}

.icon-ym-header-lockScreen:before {
	content: "\e803";
}

.icon-ym-generator-switch:before {
	content: "\e805";
}

.icon-ym-header-feedBack:before {
	content: "\e806";
}

.icon-ym-header-skins:before {
	content: "\e807";
}

.icon-ym-global-role:before {
	content: "\e808";
}

.icon-ym-highchartsBullet:before {
	content: "\e80b";
}

.icon-ym-header-userInfo:before {
	content: "\e80c";
}

.icon-ym-generator-upload:before {
	content: "\e80d";
}

.icon-ym-generator-todolist:before {
	content: "\e80e";
}

.icon-ym-highchartsArea:before {
	content: "\e80f";
}

.icon-ym-highchartsFunnel:before {
	content: "\e811";
}

.icon-ym-highcharts:before {
	content: "\e813";
}

.icon-ym-header-about:before {
	content: "\e815";
}

.icon-ym-importAndExportExample:before {
	content: "\e817";
}

.icon-ym-highchartsScatter:before {
	content: "\e818";
}

.icon-ym-header-language1:before {
	content: "\e81a";
}

.icon-ym-mpMenu:before {
	content: "\e81c";
}

.icon-ym-group:before {
	content: "\e81d";
}

.icon-ym-nav-close:before {
	content: "\e820";
}

.icon-ym-highchartsLine:before {
	content: "\e821";
}

.icon-ym-ios:before {
	content: "\e822";
}

.icon-ym-highchartsBellcurve:before {
	content: "\e823";
}

.icon-ym-mpMaterial:before {
	content: "\e824";
}

.icon-ym-mpMessage:before {
	content: "\e825";
}

.icon-ym-message:before {
	content: "\e826";
}

.icon-ym-java:before {
	content: "\e828";
}

.icon-ym-orderDemo:before {
	content: "\e82a";
}

.icon-ym-position-1:before {
	content: "\e82b";
}

.icon-ym-nav-next:before {
	content: "\e82d";
}

.icon-ym-projectExample:before {
	content: "\e82f";
}

.icon-ym-mapExample:before {
	content: "\e833";
}

.icon-ym-qyUser:before {
	content: "\e835";
}

.icon-ym-scanCode1:before {
	content: "\e836";
}

.icon-ym-report-icon-picture:before {
	content: "\e837";
}

.icon-ym-mpConfig:before {
	content: "\e838";
}

.icon-ym-options1:before {
	content: "\e839";
}

.icon-ym-nav-prev:before {
	content: "\e83a";
}

.icon-ym-scheduleExample:before {
	content: "\e83d";
}

.icon-ym-Refresh:before {
	content: "\e840";
}

.icon-ym-options:before {
	content: "\e841";
}

.icon-ym-saolian:before {
	content: "\e844";
}

.icon-ym-shezhi:before {
	content: "\e846";
}

.icon-ym-sysMenu:before {
	content: "\e848";
}

.icon-ym-barCodeExample:before {
	content: "\e73b";
}

.icon-ym-sysLanguage:before {
	content: "\e84a";
}

.icon-ym-report-icon-pie:before {
	content: "\e84b";
}

.icon-ym-bigScreenExample1:before {
	content: "\e73e";
}

.icon-ym-bigDataExample:before {
	content: "\e73f";
}

.icon-ym-orderExample:before {
	content: "\e84d";
}

.icon-ym-preview-excel-paging:before {
	content: "\e84e";
}

.icon-ym-btn-import:before {
	content: "\e741";
}

.icon-ym-tableExample:before {
	content: "\e84f";
}

.icon-ym-btn-collapse:before {
	content: "\e742";
}

.icon-ym-btn-edit:before {
	content: "\e743";
}

.icon-ym-chartExample:before {
	content: "\e744";
}

.icon-ym-btn-refresh:before {
	content: "\e746";
}

.icon-ym-tree-department:before {
	content: "\e855";
}

.icon-ym-checktriangle:before {
	content: "\e747";
}

.icon-ym-btn-fields:before {
	content: "\e748";
}

.icon-ym-tree-department2:before {
	content: "\e858";
}

.icon-ym-btn-export:before {
	content: "\e749";
}

.icon-ym-tree-normal1:before {
	content: "\e859";
}

.icon-ym-btn-preview:before {
	content: "\e74a";
}

.icon-ym-tree-normal:before {
	content: "\e85a";
}

.icon-ym-tree-organization3:before {
	content: "\e85b";
}

.icon-ym-btn-add:before {
	content: "\e74c";
}

.icon-ym-tree-position2:before {
	content: "\e85c";
}

.icon-ym-btn-expand:before {
	content: "\e74e";
}

.icon-ym-tree-user2:before {
	content: "\e85e";
}

.icon-ym-daiban1:before {
	content: "\e74f";
}

.icon-ym-tree-department1:before {
	content: "\e85f";
}

.icon-ym-daka:before {
	content: "\e750";
}

.icon-ym-tableExample1:before {
	content: "\e860";
}

.icon-ym-tree-manage:before {
	content: "\e861";
}

.icon-ym-android:before {
	content: "\e752";
}

.icon-ym-wf-applyDeliverGoods:before {
	content: "\e862";
}

.icon-ym-darg:before {
	content: "\e753";
}

.icon-ym-wechat1:before {
	content: "\e863";
}

.icon-ym-duties:before {
	content: "\e754";
}

.icon-ym-tree-position1:before {
	content: "\e865";
}

.icon-ym-echartsColumn:before {
	content: "\e757";
}

.icon-ym-delete:before {
	content: "\e758";
}

.icon-ym-echartsCandlestick:before {
	content: "\e759";
}

.icon-ym-visual-studio:before {
	content: "\e869";
}

.icon-ym-btn-remove:before {
	content: "\e75a";
}

.icon-ym-wf-conBilling:before {
	content: "\e86a";
}

.icon-ym-echartsFunnel:before {
	content: "\e75b";
}

.icon-ym-wf-batchPack:before {
	content: "\e86b";
}

.icon-ym-tree-department-1:before {
	content: "\e86c";
}

.icon-ym-wf-articlesWarehous:before {
	content: "\e86d";
}

.icon-ym-emailExample:before {
	content: "\e75e";
}

.icon-ym-wf-applyMeeting:before {
	content: "\e86e";
}

.icon-ym-dingdang:before {
	content: "\e75f";
}

.icon-ym-wf-batchTable:before {
	content: "\e86f";
}

.icon-ym-echartsLineArea:before {
	content: "\e760";
}

.icon-ym-wf-applyBanquet:before {
	content: "\e870";
}

.icon-ym-crm:before {
	content: "\e761";
}

.icon-ym-extend-ambulance:before {
	content: "\e762";
}

.icon-ym-wf-documentApproval:before {
	content: "\e872";
}

.icon-ym-echartsScatter:before {
	content: "\e763";
}

.icon-ym-wf-archivalBorrow:before {
	content: "\e874";
}

.icon-ym-dbLink:before {
	content: "\e764";
}

.icon-ym-wf-debitBill:before {
	content: "\e875";
}

.icon-ym-dot-net:before {
	content: "\e765";
}

.icon-ym-weChat:before {
	content: "\e876";
}

.icon-ym-extend-exclamation-triangle:before {
	content: "\e766";
}

.icon-ym-btn-copy:before {
	content: "\e767";
}

.icon-ym-wf-contractApproval:before {
	content: "\e879";
}

.icon-ym-documentExample:before {
	content: "\e768";
}

.icon-ym-echartsLineBar:before {
	content: "\e769";
}

.icon-ym-wf-documentSigning:before {
	content: "\e87b";
}

.icon-ym-extend-clock:before {
	content: "\e76a";
}

.icon-ym-wf-contractApprovalSheet:before {
	content: "\e87d";
}

.icon-ym-extend-check-circle-o:before {
	content: "\e76b";
}

.icon-ym-wf-leaveApply:before {
	content: "\e87f";
}

.icon-ym-wf-letterService:before {
	content: "\e880";
}

.icon-ym-erweima:before {
	content: "\e76d";
}

.icon-ym-wf-officeSupplies:before {
	content: "\e881";
}

.icon-ym-echartsBarAcross:before {
	content: "\e76e";
}

.icon-ym-wf-payDistribution:before {
	content: "\e882";
}

.icon-ym-wf-incomeRecognition:before {
	content: "\e883";
}

.icon-ym-extend-eye:before {
	content: "\e770";
}

.icon-ym-wf-postBatchTab:before {
	content: "\e884";
}

.icon-ym-extend-circle:before {
	content: "\e771";
}

.icon-ym-wf-quotationApproval:before {
	content: "\e885";
}

.icon-ym-extend-bar-chart:before {
	content: "\e772";
}

.icon-ym-wf-rewardPunishment:before {
	content: "\e886";
}

.icon-ym-extend-mail-forward:before {
	content: "\e773";
}

.icon-ym-wf-monthlyReport:before {
	content: "\e887";
}

.icon-ym-extend-car:before {
	content: "\e774";
}

.icon-ym-wf-materialRequisition:before {
	content: "\e888";
}

.icon-ym-extend-meh:before {
	content: "\e775";
}

.icon-ym-wf-procurementMaterial:before {
	content: "\e889";
}

.icon-ym-extend-cog:before {
	content: "\e776";
}

.icon-ym-wf-outboundOrder:before {
	content: "\e88a";
}

.icon-ym-extend-minus-circle:before {
	content: "\e777";
}

.icon-ym-echartsPie:before {
	content: "\e778";
}

.icon-ym-wf-receiptProcessing:before {
	content: "\e88c";
}

.icon-ym-extend-envelope:before {
	content: "\e779";
}

.icon-ym-wf-salesOrder:before {
	content: "\e88d";
}

.icon-ym-compress-screen:before {
	content: "\e77a";
}

.icon-ym-wf-salesSupport:before {
	content: "\e88e";
}

.icon-ym-wf-travelApply:before {
	content: "\e88f";
}

.icon-ym-echartsTree:before {
	content: "\e77c";
}

.icon-ym-wf-paymentApply:before {
	content: "\e890";
}

.icon-ym-extend-star-o:before {
	content: "\e77d";
}

.icon-ym-wf-violationHandling:before {
	content: "\e891";
}

.icon-ym-extend-paper-plane:before {
	content: "\e77e";
}

.icon-ym-extend-history:before {
	content: "\e77f";
}

.icon-ym-wf-purchaseList:before {
	content: "\e893";
}

.icon-ym-echartsGauge:before {
	content: "\e780";
}

.icon-ym-wf-warehouseReceipt:before {
	content: "\e894";
}

.icon-ym-extend-edit:before {
	content: "\e781";
}

.icon-ym-wf-workContactSheet:before {
	content: "\e895";
}

.icon-ym-extend-plane:before {
	content: "\e782";
}

.icon-ym-file-blank:before {
	content: "\e783";
}

.icon-ym-extend-folder-open:before {
	content: "\e784";
}

.icon-ym-wf-travelReimbursement:before {
	content: "\e898";
}

.icon-ym-extend-thumbs-up:before {
	content: "\e785";
}

.icon-ym-extend-truck:before {
	content: "\e786";
}

.icon-ym-wf-supplementCard:before {
	content: "\e89a";
}

.icon-ym-extend-folder:before {
	content: "\e787";
}

.icon-ym-zhanghao:before {
	content: "\e89b";
}

.icon-ym-extend-paperclip:before {
	content: "\e788";
}

.icon-ym-zuzhi:before {
	content: "\e89c";
}

.icon-ym-extend-star:before {
	content: "\e789";
}

.icon-ym-xingcheng:before {
	content: "\e89d";
}

.icon-ym-extend-train:before {
	content: "\e78a";
}

.icon-ym-extend-motorcycle:before {
	content: "\e78b";
}

.icon-ym-wf-vehicleApply:before {
	content: "\e89f";
}

.icon-ym-file-photo:before {
	content: "\e78c";
}

.icon-ym-zujian:before {
	content: "\e8a0";
}

.icon-ym-extend-subway:before {
	content: "\e78d";
}

.icon-ym-extend-envelope-open-o:before {
	content: "\e78e";
}

.icon-ym-file-csv1:before {
	content: "\e790";
}

.icon-ym-extend-pencil:before {
	content: "\e791";
}

.icon-ym-extend-bicycle:before {
	content: "\e792";
}

.icon-ym-extend-save:before {
	content: "\e793";
}

.icon-ym-extend-trash:before {
	content: "\e795";
}

.icon-ym-extend-bus:before {
	content: "\e796";
}

.icon-ym-file-eps:before {
	content: "\e797";
}

.icon-ym-file-ai:before {
	content: "\e798";
}

.icon-ym-extend-share:before {
	content: "\e799";
}

.icon-ym-file-audio:before {
	content: "\e79b";
}

.icon-ym-file-word:before {
	content: "\e79c";
}

.icon-ym-file-pdf:before {
	content: "\e79d";
}

.icon-ym-file-code:before {
	content: "\e79f";
}

.icon-ym-flowCirculate:before {
	content: "\e7a1";
}

.icon-ym-file-ppt:before {
	content: "\e7a4";
}

.icon-ym-file-video:before {
	content: "\e7a5";
}

.icon-ym-file-zip:before {
	content: "\e7a8";
}

.icon-ym-flowDone:before {
	content: "\e7a9";
}

.icon-ym-file-psd:before {
	content: "\e7aa";
}

.icon-ym-gangwei2:before {
	content: "\e7ac";
}

.icon-ym-full-screen:before {
	content: "\e7ad";
}

.icon-ym-file-rar:before {
	content: "\e7ae";
}

.icon-ym-file-visio-1:before {
	content: "\e7af";
}

.icon-ym-file-movie:before {
	content: "\e7b0";
}

.icon-ym-generator-barcode:before {
	content: "\e7b4";
}

.icon-ym-flowDesign:before {
	content: "\e7b5";
}

.icon-ym-formExample1:before {
	content: "\e7b7";
}

.icon-ym-file-xml:before {
	content: "\e7b9";
}

.icon-ym-flowMonitor:before {
	content: "\e7ba";
}

.icon-ym-extend-rocket:before {
	content: "\e7bc";
}

.icon-ym-file-image:before {
	content: "\e7bd";
}

.icon-ym-generator-bar:before {
	content: "\e7be";
}

.icon-ym-generator-date-range:before {
	content: "\e7c0";
}

.icon-ym-generator-date:before {
	content: "\e7c1";
}

.icon-ym-generator-card:before {
	content: "\e7c2";
}

.icon-ym-file-visio:before {
	content: "\e7c3";
}

.icon-ym-generator-dictionary:before {
	content: "\e7c4";
}

.icon-ym-generator-email:before {
	content: "\e7c5";
}

.icon-ym-generator-company:before {
	content: "\e7c6";
}

.icon-ym-generator-annular:before {
	content: "\e7c7";
}

.icon-ym-generator-button:before {
	content: "\e7c9";
}

.icon-ym-generator-count:before {
	content: "\e7ca";
}

.icon-ym-generator-layout:before {
	content: "\e7cb";
}

.icon-ym-file-excel:before {
	content: "\e7cc";
}

.icon-ym-generator-cascader:before {
	content: "\e7cd";
}

.icon-ym-generator-checkbox:before {
	content: "\e7ce";
}

.icon-ym-highchartsGauge:before {
	content: "\e856";
}

.icon-ym-wf-outgoingApply:before {
	content: "\e854";
}

.icon-ym-mac:before {
	content: "\e7db";
}

.icon-ym-appDesign:before {
	content: "\e6f9";
}

.icon-ym-generator-pie:before {
	content: "\e60b";
}

.icon-ym-scanCode:before {
	content: "\e87e";
}

.icon-ym-mpUser:before {
	content: "\e794";
}

.icon-ym-printExample:before {
	content: "\e79a";
}

.icon-ym-qyOrg:before {
	content: "\e79e";
}

.icon-ym-qyMessage:before {
	content: "\e7a0";
}

.icon-ym-generator-subFlow:before {
	content: "\e61b";
}

.icon-ym-reportExample1:before {
	content: "\e7a2";
}

.icon-ym-qyConfig:before {
	content: "\e7a3";
}

.icon-ym-saoyisao:before {
	content: "\e623";
}

.icon-ym-highcharts-Pie:before {
	content: "\e7b6";
}

.icon-ym-report-icon-area:before {
	content: "\e63e";
}

.icon-ym-report-icon-arrow-down:before {
	content: "\e63f";
}

.icon-ym-report-icon-align-left:before {
	content: "\e640";
}

.icon-ym-report-icon-align-top:before {
	content: "\e641";
}

.icon-ym-report-icon-border-all:before {
	content: "\e642";
}

.icon-ym-report-icon-align-bottom:before {
	content: "\e643";
}

.icon-ym-report-icon-align-right:before {
	content: "\e644";
}

.icon-ym-report-icon-bold:before {
	content: "\e645";
}

.icon-ym-report-icon-border-bottom:before {
	content: "\e646";
}

.icon-ym-report-icon-align-center:before {
	content: "\e647";
}

.icon-ym-report-icon-border-right:before {
	content: "\e648";
}

.icon-ym-report-icon-bubble:before {
	content: "\e649";
}

.icon-ym-report-icon-diagram:before {
	content: "\e64a";
}

.icon-ym-report-icon-enlarge:before {
	content: "\e64b";
}

.icon-ym-report-icon-align-middle:before {
	content: "\e64c";
}

.icon-ym-report-icon-preview-pagePre:before {
	content: "\e64e";
}

.icon-ym-report-icon-border-left:before {
	content: "\e64f";
}

.icon-ym-report-icon-font:before {
	content: "\e652";
}

.icon-ym-report-icon-border-top:before {
	content: "\e653";
}

.icon-ym-report-icon-background-color:before {
	content: "\e656";
}

.icon-ym-report-icon-border-custom:before {
	content: "\e657";
}

.icon-ym-report-icon-horizontal-column:before {
	content: "\e65c";
}

.icon-ym-report-icon-column:before {
	content: "\e65d";
}

.icon-ym-report-icon-font-type:before {
	content: "\e65e";
}

.icon-ym-report-icon-import-excel:before {
	content: "\e65f";
}

.icon-ym-report-icon-restore:before {
	content: "\e662";
}

.icon-ym-report-icon-oblique-head:before {
	content: "\e663";
}

.icon-ym-report-icon-preview-pdf:before {
	content: "\e664";
}

.icon-ym-report-icon-preview-pageNext:before {
	content: "\e665";
}

.icon-ym-report-icon-preview-print:before {
	content: "\e666";
}

.icon-ym-report-icon-preview-pageLast:before {
	content: "\e667";
}

.icon-ym-report-icon-preview-excel-paging:before {
	content: "\e669";
}

.icon-ym-report-icon-radar:before {
	content: "\e66a";
}

.icon-ym-report-icon-border-none:before {
	content: "\e66b";
}

.icon-ym-report-icon-preview-word:before {
	content: "\e66c";
}

.icon-ym-report-icon-scatter:before {
	content: "\e66f";
}

.icon-ym-report-icon-preview-printPreview:before {
	content: "\e670";
}

.icon-ym-report-icon-preview-excel-with-paging-sheet:before {
	content: "\e671";
}

.icon-ym-report-icon-fontsize:before {
	content: "\e672";
}

.icon-ym-report-icon-search-setting:before {
	content: "\e673";
}

.icon-ym-report-icon-preview-excel:before {
	content: "\e674";
}

.icon-ym-report-icon-undo:before {
	content: "\e675";
}

.icon-ym-report-icon-shrink:before {
	content: "\e676";
}

.icon-ym-report-icon-configuration:before {
	content: "\e677";
}

.icon-ym-report-icon-preview-refresh:before {
	content: "\e678";
}

.icon-ym-report-icon-underline:before {
	content: "\e679";
}

.icon-ym-report-icon-italic:before {
	content: "\e67b";
}

.icon-ym-report-icon-preview-printPdf:before {
	content: "\e67c";
}

.icon-ym-report-icon-merge-cell:before {
	content: "\e67a";
}

.icon-ym-report-icon-polar-plot:before {
	content: "\e650";
}

.icon-ym-report-icon-page-preview:before {
	content: "\e668";
}

.icon-ym-report-icon-doughnut:before {
	content: "\e66e";
}

.icon-ym-report-icon-preview:before {
	content: "\e655";
}

.icon-ym-pageDesign:before {
	content: "\e684";
}

.icon-ym-customUrl:before {
	content: "\e685";
}

.icon-ym-btn-clearn:before {
	content: "\e80a";
}

.icon-ym-tree-organization1:before {
	content: "\e82e";
}

.icon-ym-generator-documents:before {
	content: "\e878";
}

.icon-ym-generator-createtime:before {
	content: "\e87c";
}