<template>
  <view>
    <view>
      <view>{{ newsData.title }}</view>
      <view>
        <text>{{ formatDate(newsData.publishTime) }}</text>
        <text>{{ newsData.source }}</text>
        <text v-if="newsData.author">作者: {{ newsData.author }}</text>
      </view>
    </view>
    <image :src="newsData.coverImage" mode="widthFix"
      v-if="newsData.coverImage && newsData.coverImage !== define.baseURL"></image>
    <view>
      <rich-text :nodes="newsData.content"></rich-text>
    </view>
  </view>
</template>

<script>
import { getNewsDetail } from "@/api/flow-up/news";

export default {
  data() {
    return {
      newsData: {
        id: "",
        title: "",
        publishTime: "",
        source: "",
        author: "",
        coverImage: "",
        content: "",
      },
      relatedNews: [],
      isLoading: true,
    };
  },
  async onLoad(options) {
    if (!options.id) {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
      return;
    }

    uni.showLoading({
      title: "加载中...",
      mask: true,
    });

    try {
      // 并行请求新闻详情和相关新闻
      const [detailRes, relatedRes] = await Promise.all([
        getNewsDetail(options.id),
      ]);

      if (detailRes.code === 200 && detailRes.data) {
        this.newsData = {
          id: detailRes.data.id,
          title: detailRes.data.title,
          publishTime: detailRes.data.publishTime,
          source: detailRes.data.source,
          author: detailRes.data.author,
          coverImage: this.define.baseURL + detailRes.data.coverImage,
          content: this.formatContent(detailRes.data.content),
        };
      } else {
        throw new Error("获取新闻详情失败");
      }
    } catch (error) {
      console.error("加载新闻详情失败:", error);
      uni.showToast({
        title: "加载失败，请重试",
        icon: "none",
      });
    } finally {
      uni.hideLoading();
      this.isLoading = false;
    }
  },
  methods: {
    viewDetail(item) {
      uni.redirectTo({
        url: `/pages/news/detail?id=${item.id}`,
      });
    },

    goBack() {
      uni.navigateBack();
    },

    formatContent(content) {
      if (!content) return "";

      // 如果content是HTML字符串，直接返回
      if (typeof content === "string" && content.includes("<")) {
        return content;
      }

      // 如果是普通文本，转换为HTML格式
      return content
        .split("\n")
        .map((text) => `<p>${text}</p>`)
        .join("");
    },

    formatDate(dateString) {
      if (!dateString) return '';
      // 简单的日期格式化方法
      const date = new Date(dateString);
      const year = String(date.getFullYear());
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },
};
</script>
