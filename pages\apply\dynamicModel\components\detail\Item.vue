<template>
  <view
    :class="{ 'item-card': config.xundaKey === 'card' }"
    v-if="
      !config.noShow &&
      (!config.visibility ||
        (Array.isArray(config.visibility) && config.visibility.includes('app')))
    "
  >
    <template v-if="config.layout === 'colFormItem'">
      <XundaText
        v-if="config.xundaKey == 'text'"
        :content="item.content"
        :textStyle="item.textStyle"
      />
      <XundaDivider
        v-else-if="config.xundaKey === 'divider'"
        :content="item.content"
      />
      <XundaGroupTitle
        v-else-if="config.xundaKey == 'groupTitle'"
        :content="item.content"
        :content-position="item.contentPosition"
        :helpMessage="item.helpMessage"
        @groupIcon="clickIcon(item)"
      />
      <u-form-item
        v-else
        :label="realLabel"
        :prop="item.__vModel__"
        :label-width="labelWidth"
        :left-icon="leftIcon"
        :left-icon-style="{ color: '#a8aaaf' }"
        @clickIcon="clickIcon(item)"
      >
        <XundaUploadFile
          v-if="config.xundaKey == 'uploadFile'"
          v-model="config.defaultValue"
          detailed
        />
        <view
          class="preview-image-box"
          v-else-if="config.xundaKey === 'uploadImg'"
        >
          <image
            v-for="(cItem, ci) in item.__config__.defaultValue"
            :key="ci"
            class="u-preview-image"
            :src="define.baseURL + cItem.url"
            mode="aspectFill"
            @tap.stop="
              doPreviewImage(
                define.baseURL + cItem.url,
                item.__config__.defaultValue
              )
            "
          ></image>
        </view>
        <XundaColorPicker
          v-else-if="config.xundaKey === 'colorPicker'"
          v-model="config.defaultValue"
          :colorFormat="item.colorFormat"
          disabled
        />
        <XundaRate
          v-else-if="config.xundaKey === 'rate'"
          v-model="config.defaultValue"
          :max="item.count"
          :allowHalf="item.allowHalf"
          disabled
        />
        <XundaEditor
          v-else-if="config.xundaKey === 'editor'"
          v-model="config.defaultValue"
          detailed
        />
        <view
          v-else-if="config.xundaKey === 'relationForm'"
          class="xunda-detail-text"
          style="color: rgb(41, 121, 255)"
          @click.native="toDetail(item)"
        >
          {{ item.name }}
        </view>
        <view
          class="xunda-detail-text"
          v-else-if="config.xundaKey === 'popupSelect'"
          >{{ item.name }}</view
        >
        <XundaBarcode
          v-else-if="config.xundaKey == 'barcode'"
          :staticText="item.staticText"
          :width="item.width"
          :height="item.height"
          :format="item.format"
          :dataType="item.dataType"
          :lineColor="item.lineColor"
          :background="item.background"
          :relationField="item.relationField + '_id'"
          :formData="formData"
        />
        <XundaQrcode
          v-else-if="config.xundaKey == 'qrcode'"
          :staticText="item.staticText"
          :width="item.width"
          :dataType="item.dataType"
          :colorDark="item.colorDark"
          :colorLight="item.colorLight"
          :relationField="item.relationField + '_id'"
          :formData="formData"
        />
        <XundaInputNumber
          v-else-if="config.xundaKey == 'inputNumber'"
          v-model="config.defaultValue"
          :step="item.step"
          :max="item.max || 999999999999999"
          :min="item.min || -999999999999999"
          :disabled="item.disabled"
          :placeholder="item.placeholder"
          :isAmountChinese="item.isAmountChinese"
          :thousands="item.thousands"
          :addonAfter="item.addonAfter"
          :addonBefore="item.addonBefore"
          :controls="item.controls"
          :precision="item.precision"
          detailed
        />
        <XundaCalculate
          v-else-if="config.xundaKey === 'calculate' && item.isStorage == 0"
          :expression="item.expression"
          :vModel="item.__vModel__"
          :config="config"
          :formData="formData"
          v-model="config.defaultValue"
          :precision="item.precision"
          :isAmountChinese="item.isAmountChinese"
          :thousands="item.thousands"
        />
        <!--start labelwidth=0-->
        <XundaLink
          v-else-if="config.xundaKey == 'link'"
          :content="item.content"
          :href="item.href"
          :target="item.target"
          :textStyle="item.textStyle"
        />
        <XundaAlert
          v-else-if="config.xundaKey == 'alert'"
          :type="item.type"
          :title="item.title"
          :tagIcon="item.tagIcon"
          :showIcon="item.showIcon"
          :closable="item.closable"
          :description="item.description"
          :closeText="item.closeText"
        />
        <XundaButton
          v-else-if="config.xundaKey == 'button'"
          :buttonText="item.buttonText"
          :align="item.align"
          :type="item.type"
          :disabled="item.disabled"
        />
        <XundaSlider
          v-else-if="config.xundaKey == 'slider'"
          v-model="config.defaultValue"
          :step="item.step"
          :min="item.min || 0"
          :max="item.max || 100"
          disabled
        />
        <XundaSign
          v-else-if="config.xundaKey == 'sign'"
          v-model="config.defaultValue"
          detailed
        />
        <XundaSignature
          v-else-if="config.xundaKey == 'signature'"
          v-model="config.defaultValue"
          detailed
        />
        <XundaLocation
          v-else-if="config.xundaKey == 'location'"
          v-model="config.defaultValue"
          :enableLocationScope="item.enableLocationScope"
          detailed
        />
        <!--end  labelwidth=0-->
        <template v-else>
          <view
            class="xunda-detail-text"
            v-if="config.xundaKey === 'calculate'"
          >
            <view>{{ toThousands(config.defaultValue, item) }}</view>
            <view v-if="item.isAmountChinese" style="color: #999">
              {{ xunda.getAmountChinese(getValue(item)) }}
            </view>
          </view>
          <XundaInput
            v-else-if="config.xundaKey == 'input'"
            v-model="config.defaultValue"
            detailed
            :useMask="item.useMask"
            :maskConfig="item.maskConfig"
            :addonBefore="item.addonBefore"
            :addonAfter="item.addonAfter"
          />
          <view class="xunda-detail-text" v-else>{{ getValue(item) }}</view>
        </template>
      </u-form-item>
    </template>
    <template v-else>
      <view
        class="xunda-card"
        v-if="config.xundaKey === 'card' || config.xundaKey === 'row'"
      >
        <view
          class="xunda-card-cap u-line-1 u-flex"
          v-if="item.header"
          @click="clickIcon(item)"
        >
          {{ item.header }}
          <u-icon
            :name="config.tipLabel ? 'question-circle-fill' : ''"
            class="u-m-l-10"
            color="#a0acb7"
          />
        </view>
        <Item
          v-for="(child, index) in config.children"
          :key="config.renderKey + index"
          :item="child"
          :formConf="formConf"
          :formData="formData"
          @toDetail="toDetail"
          @clickIcon="clickIcon"
        />
      </view>
      <template v-if="config.xundaKey === 'table'">
        <view class="xunda-table">
          <view class="xunda-table-title u-line-1" @click="clickIcon(item)">
            {{ config.label }}
            <u-icon
              v-if="config.tipLabel"
              :name="'question-circle-fill'"
              class="u-m-l-10"
              color="#a0acb7"
            />
          </view>
          <view
            v-for="(column, columnIndex) in config.defaultValue"
            :key="columnIndex"
          >
            <view class="xunda-table-item-title">
              <view class="xunda-table-item-title-num"
                >({{ columnIndex + 1 }})</view
              >
            </view>
            <view
              class="form-item-box"
              v-for="(childItem, cIndex) in config.children"
              :key="cIndex"
            >
              <u-form-item
                :label="
                  childItem.__config__.showLabel
                    ? childItem.__config__.label
                    : ''
                "
                :label-width="
                  childItem.__config__.labelWidth
                    ? childItem.__config__.labelWidth * 1.5
                    : undefined
                "
                @clickIcon="clickIcon(childItem)"
                :left-icon="
                  childItem.__config__.tipLabel &&
                  childItem.__config__.showLabel &&
                  childItem.__config__.label
                    ? 'question-circle-fill'
                    : ''
                "
                :left-icon-style="{ color: '#a0acb7' }"
                v-if="
                  !childItem.__config__.noShow &&
                  (!childItem.__config__.visibility ||
                    (Array.isArray(childItem.__config__.visibility) &&
                      childItem.__config__.visibility.includes('app')))
                "
              >
                <template
                  v-if="
                    ['relationFormAttr', 'popupAttr'].includes(
                      childItem.__config__.xundaKey
                    )
                  "
                >
                  <view class="xunda-detail-text" v-if="!childItem.__vModel__">
                    {{
                      column[
                        childItem.relationField.split("_xundaTable_")[0] +
                          "_" +
                          childItem.showField
                      ]
                    }}
                  </view>
                  <view class="xunda-detail-text" v-else>
                    {{ column[childItem.__vModel__] }}
                  </view>
                </template>
                <view
                  v-else-if="childItem.__config__.xundaKey === 'relationForm'"
                  class="xunda-detail-text"
                  style="color: rgb(41, 121, 255)"
                  @click.native="
                    toTableDetail(
                      childItem,
                      column[childItem.__vModel__ + '_id']
                    )
                  "
                >
                  {{ column[childItem.__vModel__] }}
                </view>
                <XundaSign
                  v-else-if="childItem.__config__.xundaKey == 'sign'"
                  v-model="column[childItem.__vModel__]"
                  detailed
                />
                <XundaSignature
                  v-else-if="childItem.__config__.xundaKey == 'signature'"
                  v-model="column[childItem.__vModel__]"
                  detailed
                />
                <XundaLocation
                  v-else-if="childItem.__config__.xundaKey == 'location'"
                  v-model="column[childItem.__vModel__]"
                  :enableLocationScope="item.enableLocationScope"
                  detailed
                />
                <XundaUploadFile
                  v-else-if="childItem.__config__.xundaKey === 'uploadFile'"
                  v-model="column[childItem.__vModel__]"
                  detailed
                />
                <XundaUploadImg
                  v-else-if="childItem.__config__.xundaKey === 'uploadImg'"
                  v-model="column[childItem.__vModel__]"
                  detailed
                />
                <XundaInputNumber
                  v-else-if="childItem.__config__.xundaKey == 'inputNumber'"
                  v-model="column[childItem.__vModel__]"
                  :step="childItem.step"
                  :max="childItem.max"
                  :min="childItem.min"
                  :disabled="childItem.disabled"
                  :placeholder="childItem.placeholder"
                  :isAmountChinese="childItem.isAmountChinese"
                  :thousands="childItem.thousands"
                  :addonAfter="childItem.addonAfter"
                  :addonBefore="childItem.addonBefore"
                  :controls="childItem.controls"
                  :precision="childItem.precision"
                  detailed
                />
                <XundaCalculate
                  v-else-if="
                    childItem.__config__.xundaKey === 'calculate' &&
                    childItem.isStorage == 0
                  "
                  :expression="childItem.expression"
                  :vModel="childItem.__vModel__"
                  :config="childItem.__config__"
                  :formData="formData"
                  v-model="column[childItem.__vModel__]"
                  :precision="childItem.precision"
                  :isAmountChinese="childItem.isAmountChinese"
                  :thousands="childItem.thousands"
                />
                <XundaRate
                  v-else-if="childItem.__config__.xundaKey === 'rate'"
                  :max="childItem.count"
                  v-model="column[childItem.__vModel__]"
                  :allowHalf="childItem.allowHalf"
                  disabled
                />
                <XundaSlider
                  v-else-if="childItem.__config__.xundaKey == 'slider'"
                  v-model="column[childItem.__vModel__]"
                  :step="childItem.step"
                  :min="childItem.min || 0"
                  :max="childItem.max || 100"
                  disabled
                />
                <template v-else>
                  <view
                    class="xunda-detail-text"
                    v-if="childItem.__config__.xundaKey === 'calculate'"
                  >
                    <view>{{
                      toThousands(column[childItem.__vModel__], childItem)
                    }}</view>
                    <view v-if="childItem.isAmountChinese" style="color: #999">
                      {{ xunda.getAmountChinese(column[childItem.__vModel__]) }}
                    </view>
                  </view>
                  <XundaInput
                    v-else-if="childItem.__config__.xundaKey == 'input'"
                    v-model="column[childItem.__vModel__]"
                    detailed
                    :useMask="childItem.useMask"
                    :maskConfig="childItem.maskConfig"
                    :addonBefore="childItem.addonBefore"
                    :addonAfter="childItem.addonAfter"
                  />
                  <view class="xunda-detail-text" v-else>{{
                    column[childItem.__vModel__]
                  }}</view>
                </template>
              </u-form-item>
            </view>
          </view>
          <view
            class="xunda-table-item"
            v-if="item.showSummary && summaryField.length"
          >
            <view class="xunda-table-item-title u-flex u-row-between">
              <text class="xunda-table-item-title-num"
                >{{ item.__config__.label }}合计</text
              >
            </view>
            <view class="u-p-l-20 u-p-r-20 form-item-box">
              <u-form-item
                v-for="(item, index) in summaryField"
                :label="item.__config__.label"
                :key="item.__vModel__"
              >
                <u-input input-align="right" v-model="item.value" disabled />
              </u-form-item>
            </view>
          </view>
        </view>
      </template>
      <view
        v-else-if="config.xundaKey === 'steps'"
        style="background-color: #fff; padding: 15px 0"
      >
        <view class="step-container">
          <u-steps
            :list="config.children"
            name="title"
            :mode="item.simple ? 'dot' : 'number'"
            :current="config.active"
            @change="config.active = $event"
          >
          </u-steps>
        </view>
        <view v-for="(itemSub, i) in config.children" :key="i">
          <view v-if="i === config.active">
            <Item
              v-for="(childItem, childIndex) in itemSub.__config__.children"
              :key="childIndex"
              :item="childItem"
              :formConf="formConf"
              :formData="formData"
              @toDetail="toDetail"
              @clickIcon="clickIcon"
            />
          </view>
        </view>
      </view>
      <view class="xunda-tab" v-if="config.xundaKey === 'tab'">
        <u-tabs
          is-scroll
          :list="config.children"
          name="title"
          v-model="tabCurrent"
          @change="onTabChange"
        />
        <view v-for="(pane, i) in config.children" :key="i">
          <view v-show="i == tabCurrent">
            <Item
              v-for="(childItem, childIndex) in pane.__config__.children"
              :key="childIndex"
              :item="childItem"
              :formConf="formConf"
              :formData="formData"
              @toDetail="toDetail"
              @clickIcon="clickIcon"
            />
          </view>
        </view>
      </view>
      <template v-if="config.xundaKey === 'collapse'">
        <u-collapse
          :head-style="{ 'padding-left': '20rpx' }"
          :accordion="item.accordion"
          ref="collapseRef"
        >
          <u-collapse-item
            :title="pane.title"
            v-for="(pane, i) in config.children"
            :key="i"
            :open="config.active.indexOf(pane.name) > -1"
          >
            <Item
              v-for="(child, j) in pane.__config__.children"
              :key="child.__config__.renderKey"
              :item="child"
              :formConf="formConf"
              :formData="formData"
              @toDetail="toDetail"
              @clickIcon="clickIcon"
            />
          </u-collapse-item>
        </u-collapse>
      </template>
    </template>
  </view>
</template>
<script>
// #ifdef MP
import Item from "./Item.vue"; //兼容小程序
// #endif
const specialList = ["link", "editor", "button", "alert"];
export default {
  name: "Item",
  // #ifdef MP
  components: {
    Item,
  },
  // #endif
  props: {
    item: {
      type: Object,
      required: true,
    },
    formConf: {
      type: Object,
      required: true,
    },
    formData: {
      type: Object,
    },
  },
  computed: {
    config() {
      return this.item.__config__;
    },
    labelWidth() {
      if (specialList.indexOf(this.config.xundaKey) > -1) return 0;
      return this.config.labelWidth ? this.config.labelWidth * 1.5 : undefined;
    },
    label() {
      return this.config.showLabel &&
        specialList.indexOf(this.config.xundaKey) < 0
        ? this.config.label
        : "";
    },
    realLabel() {
      return this.label ? this.label + (this.formConf.labelSuffix || "") : "";
    },
    leftIcon() {
      return this.config.tipLabel && this.label && this.config.showLabel
        ? "question-circle-fill"
        : "";
    },
  },
  data() {
    return {
      tabCurrent: 0,
      tableData: [],
      summaryField: [],
    };
  },
  created() {
    this.handleSummary();
    this.handleTab();
  },
  mounted() {
    if (this.config.xundaKey === "collapse") {
      this.$refs.collapseRef && this.$refs.collapseRef.init();
    }
    uni.$on("initCollapse", () => {
      this.$refs.collapseRef && this.$refs.collapseRef.init();
    });
  },
  methods: {
    handleTab() {
      if (this.config.xundaKey !== "tab") return;
      for (var i = 0; i < this.config.children.length; i++) {
        if (this.config.active == this.config.children[i].name) {
          this.tabCurrent = i;
          break;
        }
      }
    },
    handleSummary() {
      if (this.item.__config__.xundaKey !== "table") return;
      const val = this.item.__config__.defaultValue;
      let summaryField = this.item.summaryField || [];
      this.summaryField = [];
      this.tableData = this.item.__config__.children || [];
      for (let i = 0; i < summaryField.length; i++) {
        for (let o = 0; o < this.tableData.length; o++) {
          const item = this.tableData[o];
          if (
            this.tableData[o].__vModel__ === summaryField[i] &&
            !item.__config__.noShow
          ) {
            this.summaryField.push({
              value: "",
              ...item,
            });
          }
        }
      }
      this.$nextTick(() => this.getTableSummaries(val, this.item));
    },
    toThousands(val, column) {
      if (val) {
        let valList = val.toString().split(".");
        let num = Number(valList[0]);
        let newVal = column.thousands ? num.toLocaleString() : num;
        return valList[1] ? newVal + "." + valList[1] : newVal;
      } else {
        return val;
      }
    },
    getTableSummaries(newVal, config) {
      for (let i = 0; i < this.summaryField.length; i++) {
        let val = 0;
        for (let j = 0; j < newVal.length; j++) {
          if (newVal[j][this.summaryField[i].__vModel__]) {
            let data = isNaN(newVal[j][this.summaryField[i].__vModel__])
              ? 0
              : Number(newVal[j][this.summaryField[i].__vModel__]);
            val += data;
          }
        }
        let realVal =
          val && !Number.isInteger(val) ? Number(val).toFixed(2) : val;
        if (this.summaryField[i].thousands)
          realVal = Number(realVal).toLocaleString("zh");
        this.summaryField[i].value = realVal;
      }
    },
    clickIcon(e) {
      this.$emit("clickIcon", e);
    },
    onTabChange(index) {
      if (this.tabCurrent === index) return;
      this.tabCurrent = index;
      this.$emit("tab-change", this.item, index);
      this.$nextTick(() => {
        uni.$emit("initCollapse");
        uni.$emit("updateCode");
      });
    },
    doPreviewImage(current, imageList) {
      const images = imageList.map((item) => this.define.baseURL + item.url);
      uni.previewImage({
        urls: images,
        current: current,
        success: () => {},
        fail: () => {
          uni.showToast({
            title: "预览图片失败",
            icon: "none",
          });
        },
      });
    },
    toDetail(item) {
      this.$emit("toDetail", item);
    },
    toTableDetail(item, value) {
      item.__config__.defaultValue = value;
      this.$emit("toDetail", item);
    },
    getValue(item) {
      if (Array.isArray(item.__config__.defaultValue)) {
        if (["timeRange", "dateRange"].includes(item.__config__.xundaKey)) {
          return item.__config__.defaultValue.join("");
        }
        return item.__config__.defaultValue.join();
      }
      return item.__config__.defaultValue;
    },
  },
};
</script>
