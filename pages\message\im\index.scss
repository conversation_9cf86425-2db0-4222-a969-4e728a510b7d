@font-face {
  font-family: "HMfont-home";
  src: url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2');
}

.icon {
  font-family: "HMfont-home" !important;
  font-size: 56rpx;
  font-style: normal;
  color: #333;

  &.biaoqing:before {
    content: "\e797";
  }

  &.jianpan:before {
    content: "\e7b2";
  }

  &.yuyin:before {
    content: "\e805";
  }

  &.tupian:before {
    content: "\e639";
  }

  &.chehui:before {
    content: "\e904";
  }

  &.luyin:before {
    content: "\e905";
  }

  &.luyin2:before {
    content: "\e677";
  }

  &.other-voice:before {
    content: "\e667";
  }

  &.my-voice:before {
    content: "\e906";
  }

  &.hongbao:before {
    content: "\e626";
  }

  &.tupian2:before {
    content: "\e674";
  }

  &.paizhao:before {
    content: "\e63e";
  }

  &.add:before {
    content: "\e655";
  }

  &.close:before {
    content: "\e607";
  }

  &.to:before {
    content: "\e675";
  }
}

page {
  background-color: #f0f2f6;
}

.im-v {
	.notData-box {
		width: 100%;
		height: 100%;
		justify-content: center;
		align-items: center;
		padding-top: 400rpx;
	
		.notData-inner {
			width: 280rpx;
			height: 308rpx;
			align-items: center;
	
	
			.iconImg {
				width: 100%;
				height: 100%;
			}
	
			.notData-inner-text {
				padding: 30rpx 0;
				color: #909399;
			}
		}
	}
  .msg-end {
    padding: 40rpx 0;
    font-size: 28rpx;
    text-align: center;
    color: #999;
  }

  .hidden {
    display: none !important;
  }

  .popup-layer {
    &.showLayer {
      transform: translate3d(0, -42vw, 0);
    }

    transition: all .15s linear;
    width: 100%;
    height: 42vw;
    padding: 20rpx 2%;
    background-color: #f2f2f2;
    position: fixed;
    z-index: 20;
    top: 100%;

    .emoji-swiper {
      height: 40vw;

      swiper-item {
        display: flex;
        align-content: flex-start;
        flex-wrap: wrap;

        .emoji-item {
          width: 12vw;
          height: 12vw;
          display: flex;
          justify-content: center;
          align-items: center;

          .emoji-item-img {
            width: 8.4vw;
            height: 8.4vw;
          }
        }
      }
    }

    .more-layer {
      width: 100%;
      height: 42vw;

      .list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .box {
          width: 18vw;
          height: 18vw;
          border-radius: 20rpx;
          background-color: #fff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0 3vw 2vw 3vw;

          .icon {
            font-size: 70rpx;
          }
        }
      }
    }
  }

  .input-box {
    width: 100%;
    box-sizing: border-box;
    min-height: 80rpx;
    padding: 24rpx 32rpx;
    background-color: #f2f2f2;
    display: flex;
    align-items: flex-end;
    position: fixed;
    z-index: 20;
    bottom: 0;

    &.showLayer {
      transform: translate3d(0, -42vw, 0);
    }

    transition: all .15s linear;

    .input-box-icon {
      flex-shrink: 0;
      height: 56rpx;
      width: 56rpx;
      margin-bottom: 6rpx;
      margin-right: 16rpx;

      &.add {
        margin-right: 0;
      }
    }

    .send-btn {
      flex-shrink: 0;
      width: 90rpx;
      margin-bottom: 6rpx;
      height: 56rpx;
      line-height: 56rpx;
      background: #339AFF;
      color: #fff;
      border-radius: 6rpx;
      font-size: 24rpx;
      text-align: center;
      margin-left: 16rpx;
    }

    .voice-mode {
      flex: 1;
      height: 68rpx;
      border-radius: 16rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
      background-color: #fff;
      color: #555;
      margin-right: 16rpx;

      &.recording {
        background-color: #e5e5e5;
      }
    }

    .text-mode {
      flex: 1;
      min-height: 68rpx;
      display: flex;
      background-color: #fff;
      border-radius: 16rpx;
      margin-right: 16rpx;

      .input-area {
        width: 100%;
        padding: 16rpx;
        display: flex;
        align-items: center;
        max-height: 100rpx;
        overflow-y: scroll;

        textarea {
          width: 100%;
          font-size: 28rpx;
        }
      }
    }
  }

  .record {
    width: 40vw;
    height: 40vw;
    position: fixed;
    top: 55%;
    left: 30%;
    background-color: rgba(0, 0, 0, .6);
    border-radius: 20rpx;

    .ing {
      width: 100%;
      height: 30vw;
      display: flex;
      justify-content: center;
      align-items: center;

      // 模拟录音音效动画
      @keyframes volatility {
        0% {
          background-position: 0% 130%;
        }

        20% {
          background-position: 0% 150%;
        }

        30% {
          background-position: 0% 155%;
        }

        40% {
          background-position: 0% 150%;
        }

        50% {
          background-position: 0% 145%;
        }

        70% {
          background-position: 0% 150%;
        }

        80% {
          background-position: 0% 155%;
        }

        90% {
          background-position: 0% 140%;
        }

        100% {
          background-position: 0% 135%;
        }
      }

      .icon {
        background-image: linear-gradient(to bottom, #f09b37, #fff 50%);
        background-size: 100% 200%;
        animation: volatility 1.5s ease-in-out -1.5s infinite alternate;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        font-size: 150rpx;
        color: #f09b37;
      }
    }

    .cancel {
      width: 100%;
      height: 30vw;
      display: flex;
      justify-content: center;
      align-items: center;

      .icon {
        color: #fff;
        font-size: 150rpx;
      }
    }

    .tis {
      width: 100%;
      height: 10vw;
      display: flex;
      justify-content: center;
      font-size: 28rpx;
      color: #fff;

      &.change {
        color: #f09b37;
      }
    }
  }

  .msg-list {

    padding: 0 24rpx;
	.msg-list-item {
		display: flex;
		align-items: flex-start;
		padding: 20rpx 0;
		&.msg-list-item-l {
			flex-direction: row;

			.avatar {
				flex-shrink: 0;
				width: 80rpx;
				height: 80rpx;
				margin-right: 20rpx;
			}
		}
		&.msg-list-item-r {
			flex-direction: row-reverse;

			.avatar {
				flex-shrink: 0;
				width: 80rpx;
				height: 80rpx;
				margin-left: 20rpx;
			}
		}
		.msg-text {
			max-width: 70%;
			min-height: 50rpx;
			border-radius: 4rpx 30rpx 30rpx 30rpx;
			padding: 16rpx 32rpx;
			display: flex;
			align-items: center;
			font-size: 32rpx;
			word-break: break-word;
			flex-wrap: wrap;
			background-color: #fff;
			color: #303133;
			.msg-text-txt {
				font-size: 32rpx;
				line-height: 48rpx;
			}
			.msg-text-emoji {
				vertical-align: top;
				width: 48rpx;
				height: 48rpx;
				display: inline-block;
			}
		}
		.msg-img {
			background-color: transparent;
			padding: 0;
			overflow: hidden;
			image {
				max-width: 350rpx;
				max-height: 350rpx;
				border-radius: 16rpx;
			}
		}
		.msg-voice {
			.icon {
				font-size: 40rpx;
				display: flex;
				align-items: center;
			}
			.icon:after {
				content: " ";
				width: 53rpx;
				height: 53rpx;
				border-radius: 100%;
				position: absolute;
				box-sizing: border-box;
			}
			.length {
				font-size: 28rpx;
			}
			&.play {
			  @keyframes my-play {
				0% {
				  transform: translateX(80%);
				}
	
				100% {
				  transform: translateX(0%);
				}
			  }
	
			  .icon:after {
				border-left: solid 10rpx rgba(186, 230, 253, .8);
				animation: my-play 1s linear infinite;
			  }
			}
		}
	}
    // .msg-list-item {
    //   padding: 20rpx 0;

    //   .content {
    //     width: 100%;
    //     display: flex;

    //     .msg-text {
    //       max-width: 70%;
    //       min-height: 50rpx;
    //       border-radius: 4rpx 30rpx 30rpx 30rpx;
    //       padding: 16rpx 32rpx;
    //       display: flex;
    //       align-items: center;
    //       font-size: 32rpx;
    //       word-break: break-word;
    //       flex-wrap: wrap;
    //       background-color: #fff;
    //       color: #3A3A3A;
				// 	.msg-text-txt{
				// 		font-size: 32rpx;
				// 		line-height: 48rpx;
				// 	}
				// 	.msg-text-emoji{
				// 		vertical-align: top;
				// 		width: 48rpx;
				// 		height: 48rpx;
				// 		display: inline-block;
				// 	}
    //     }

    //     .msg-img {
    //       background-color: transparent;
    //       padding: 0;
    //       overflow: hidden;

    //       image {
    //         max-width: 350rpx;
    //         max-height: 350rpx;
    //         border-radius: 16rpx;
    //       }
    //     }

    //     .msg-voice {

    //       .icon {
    //         font-size: 40rpx;
    //         display: flex;
    //         align-items: center;
    //       }

    //       .icon:after {
    //         content: " ";
    //         width: 53rpx;
    //         height: 53rpx;
    //         border-radius: 100%;
    //         position: absolute;
    //         box-sizing: border-box;
    //       }

    //       .length {
    //         font-size: 28rpx;
    //       }
    //     }
    //   }

    //   .avatar {
    //     // flex-shrink: 0;
    //     // width: 80rpx;
    //     // height: 80rpx;
    //   }

    //   .my {
    //     display: flex;
    //     justify-content: flex-end;

    //     .content {
    //       min-height: 80rpx;
    //       align-items: center;
    //       justify-content: flex-end;

    //       .msg-text {
    //         background-color: #BAE6FD;
    //         border-radius: 30rpx 4rpx 30rpx 30rpx;
    //       }

    //       .msg-voice {
    //         .length {
    //           margin-right: 20rpx;
    //         }

    //         &.play {
    //           @keyframes my-play {
    //             0% {
    //               transform: translateX(80%);
    //             }

    //             100% {
    //               transform: translateX(0%);
    //             }
    //           }

    //           .icon:after {
    //             border-left: solid 10rpx rgba(186, 230, 253, .8);
    //             animation: my-play 1s linear infinite;
    //           }
    //         }
    //       }
    //     }

    //     .avatar {
    //       margin-left: 16rpx;
    //     }
    //   }

    //   .other {
    //     display: flex;

    //     .avatar {
    //       margin-right: 16rpx;
    //     }

    //     .content {
    //       flex-wrap: wrap;

    //       .msg-voice {
    //         .icon {
    //           color: #333;
    //         }

    //         .length {
    //           margin-left: 20rpx;
    //         }

    //         &.play {
    //           @keyframes other-play {
    //             0% {
    //               transform: translateX(-80%);
    //             }

    //             100% {
    //               transform: translateX(0%);
    //             }
    //           }

    //           .icon:after {
    //             border-right: solid 10rpx rgba(255, 255, 255, .8);

    //             animation: other-play 1s linear infinite;
    //           }
    //         }
    //       }
    //     }
    //   }
    // }
  }
}