/* process.env.NODE_ENV设置生产环境模式 */
// #ifndef MP
const baseURL = process.env.NODE_ENV === "production" ? "https://wx.libo.gzbit.cn:22443" : "https://wx.libo.gzbit.cn:22443"
const webSocketUrl = process.env.NODE_ENV === "production" ? "wss://wx.libo.gzbit.cn:22443/api/message/websocket" :
	"wss://wx.libo.gzbit.cn:22443/api/message/websocket"
const report = process.env.NODE_ENV === 'production' ? 'https://wx.libo.gzbit.cn:22443/Report' : 'http://192.168.1.165:8200'
const flow = process.env.NODE_ENV === 'production' ? 'https://wx.libo.gzbit.cn:22443' : 'https://wx.libo.gzbit.cn:22443/'
// #endif

// #ifdef MP
const baseURL = "https://wx.libo.gzbit.cn:22443"
const webSocketUrl = "wss://wx.libo.gzbit.cn:22443/api/message/websocket"
const report = 'https://wx.libo.gzbit.cn:22443'
const flow = 'https://wx.libo.gzbit.cn:22443/'
// #endif

const define = {
	copyright: "Copyright @ 2025 兴义市人民医院神经外科",
	sysVersion: "V1.0.1",
	baseURL, // 接口前缀
	report,
	flow,
	webSocketUrl,
	comUploadUrl: baseURL + '/api/file/Uploader/',
	timeout: 1000000,
	aMapWebKey: '44afb8705fd4a1d0ee2d44d5a175f0cd',
	cipherKey: 'EY8WePvjM5GGwQzn', // 加密key
}
export default define