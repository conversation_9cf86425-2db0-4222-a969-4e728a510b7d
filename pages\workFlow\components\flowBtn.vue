<template>
	<view class="flowBefore-actions u-flex-col" :style="{'height': '88rpx'}"
		v-if="hasComment ||  (actionList.length || rightBtnList.length) ">
		<view class="u-flex" style="width: 100%;">
			<view class="u-flex buttom-btn-left-inner" @click="loading ? showAction = false : showAction = true"
				v-if="actionList.length">
				<view class="u-flex-col" style="align-items: center">
					<u-icon name="more-dot-fill" size="28"></u-icon>
					<view class="u-m-t-6">更多</view>
				</view>
			</view>
			<template v-if="opType != 2">
				<view class="u-flex buttom-btn-left-inner" @click.stop="handleBtn('save')"
					v-if="btnInfo?.hasSaveBtn && !hideSaveBtn">
					<view class="u-flex-col" style="align-items: center">
						<text class="icon-ym icon-ym-extend-save"></text>
						<view class="u-m-t-6">{{saveBtnText || '暂存'}}</view>
					</view>
				</view>
				<view class="u-flex buttom-btn-left-inner reject" @click.stop="handleBtn('reject')"
					v-if="btnInfo?.hasRejectBtn && opType != 2">
					<view class="u-flex-col" style="align-items: center">
						<u-icon name="minus-circle" size="28"></u-icon>
						<view class="u-m-t-6">拒绝</view>
					</view>
				</view>
			</template>

			<view class="rightBtn u-flex">
				<u-button class="buttom-btn" @click.stop="handleBtn('comment')" v-if="!rightBtnList.length"
					:loading="loading">
					评论
				</u-button>
				<template v-if="opType != 2">
					<u-button v-for="btn,index in rightBtnList" :key="index" class="buttom-btn"
						@click.stop="handleBtn(btn.id)" :style="{'width':btn.width}" :loading="loading"
						:type="btn?.type">
						{{ btn.fullName}}
					</u-button>
				</template>
				<template class="" v-else>
					<u-button v-for="btn,index in todoBtnList" :key="index" class="buttom-btn"
						@click.stop="handleBtn(btn.id)" :style="{'width':btn.width}" :loading="loading"
						:type="btn?.type">
						{{ btn.fullName}}
					</u-button>
				</template>
			</view>
		</view>
		<u-action-sheet v-model="showAction" :list="actionList" @click="handleAction" />
	</view>
</template>
<script>
	export default {
		props: {
			actionList: {
				type: Array,
				default: () => ([])
			},
			rightBtnList: {
				type: Array,
				default: () => ([])
			},
			todoBtnList: {
				type: Array,
				default: () => ([])
			},
			btnInfo: {
				type: Object,
				default: () => ({})
			},
			opType: {
				type: [String, Number],
				default: ''
			},
			saveBtnText: {
				type: String,
				default: ''
			},
			btnLoading: {
				type: Boolean,
				default: false
			},
			hideSaveBtn: {
				type: Boolean,
				default: false
			},
			hasSignFor: {
				type: Boolean,
				default: false
			},
			hasComment: {
				type: Boolean,
				default: false
			}
		},
		computed: {
			loading() {
				return this.btnLoading
			},
			isShowApproval() {
				return this.opType == '3' && (this.btnInfo?.hasRejectBtn || this.btnInfo?.hasAuditBtn)
			}
		},
		data() {
			return {
				showAction: false
			}
		},
		methods: {
			handleBtn(type) {
				if (!this.loading) this.$emit('handleBtn', type)
			},
			//更多按钮
			handleAction(index) {
				this.handleBtn(this.actionList[index].id)
			}
		}
	}
</script>

<style lang="scss">
	.flowBefore-actions {
		.poup-btn {
			background-color: #fff;
			width: 100%;
			height: 88rpx;
			padding: 0 20rpx;
			color: #7f7f7f;
			border-bottom: 1rpx solid #d7d7d7;

			.btn {
				background-color: #f2f2f2;
				border-radius: 8rpx;
				height: 60rpx;
				padding-left: 10rpx;
				width: 100%;
				line-height: 60rpx;
			}
		}
	}
</style>