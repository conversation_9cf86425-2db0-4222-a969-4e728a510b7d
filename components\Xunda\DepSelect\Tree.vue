<template>
  <u-popup
    class="xunda-tree-select-popup"
    mode="right"
    :popup="false"
    v-model="showPopup"
    length="auto"
    :z-index="uZIndex"
    width="100%"
    @close="close"
  >
    <view class="xunda-tree-select-body">
      <view class="xunda-tree-select-title">
        <text
          class="icon-ym icon-ym-report-icon-preview-pagePre backIcon"
          @tap="close()"
        ></text>
        <view class="title">部门选择</view>
      </view>
      <view class="xunda-tree-select-search">
        <u-search
          placeholder="请输入关键词搜索"
          v-model="keyword"
          height="72"
          :show-action="false"
          @change="handleSearch"
          bg-color="#f0f2f6"
          shape="square"
        >
        </u-search>
      </view>
      <view class="xunda-tree-selected">
        <view class="xunda-tree-selected-head">
          <view>已选</view>
          <view v-if="multiple" class="clear-btn" @click="setCheckAll"
            >清空列表</view
          >
        </view>
        <view class="xunda-tree-selected-box">
          <scroll-view scroll-y="true" class="select-list">
            <u-tag
              closeable
              @close="delSelect(index)"
              v-for="(list, index) in selectList"
              :key="index"
              :text="list.fullName"
              class="u-selectTag"
            />
          </scroll-view>
        </view>
      </view>
      <view class="xunda-tree-select-tree">
        <scroll-view :scroll-y="true" style="height: 100%">
          <ly-tree
            v-if="selectType === 'all'"
            :props="realProps"
            :node-key="realProps.value"
            :load="loadNode"
            lazy
            :tree-data="lazyOptions"
            show-node-icon
            :defaultExpandAll="false"
            @node-click="handleNodeClick"
          />

          <ly-tree
            v-else
            ref="tree"
            :node-key="realProps.value"
            :tree-data="options"
            :show-checkbox="false"
            @node-click="handleNodeClick"
            :props="realProps"
            :show-node-icon="true"
            :show-radio="false"
            :filter-node-method="filterNode"
            :highlight-current="true"
          />
        </scroll-view>
      </view>
      <view class="xunda-tree-select-actions">
        <u-button class="buttom-btn" @click="close()">取消</u-button>
        <u-button
          class="buttom-btn"
          type="primary"
          @click.stop="handleConfirm()"
          >确定</u-button
        >
      </view>
    </view>
  </u-popup>
</template>

<script>
var _self;
const defaultProps = {
  label: "fullName",
  value: "id",
  icon: "icon",
  children: "children",
};
import { selectAsyncList } from "@/api/common.js";
export default {
  props: {
    options: {
      type: Array,
      default() {
        return [];
      },
    },
    selectedData: {
      type: Array,
      default() {
        return [];
      },
    },
    ids: {
      default: "",
    },
    selectType: {
      default: "",
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: [String, Number],
      default: 0,
    },
    props: {
      type: Object,
      default: () => ({
        label: "fullName",
        value: "id",
        icon: "icon",
        children: "children",
        isLeaf: "isLeaf",
      }),
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      moving: false,
      selectList: [],
      selectListId: [],
      newListId: [],
      keyword: "",
      showPopup: false,
      lazyOptions: [],
    };
  },
  created() {
    _self = this;
  },
  watch: {
    // 在select弹起的时候，重新初始化所有数据
    modelValue: {
      handler(val) {
        this.showPopup = val;
        if (val) setTimeout(() => this.init(), 10);
      },
      immediate: true,
    },
  },
  computed: {
    uZIndex() {
      return this.zIndex ? this.zIndex : this.$u.zIndex.popup;
    },
    realProps() {
      return {
        ...defaultProps,
        ...this.props,
      };
    },
  },
  methods: {
    init() {
      this.keyword = "";
      this.selectListId = Array.isArray(this.ids)
        ? JSON.parse(
            JSON.stringify(this.ids.filter((item) => item.trim() !== ""))
          )
        : [];
      this.selectList = JSON.parse(JSON.stringify(this.selectedData));
    },
    loadNode(node, resolve) {
      let id = node.key === null ? 0 : node.key;
      let level = node.level;
      selectAsyncList(id).then((res) => {
        const list = res.data.list.map((value, i) => ({
          id: value.id,
          fullName: value.fullName,
          isLeaf: level >= _self.level ? true : value.isLeaf,
          type: value.type,
          icon: value.icon,
        }));
        resolve(list);
      });
    },
    handleNodeClick(obj) {
      let data = obj.data;
      if (data.type === "company") return;
      if (this.multiple) {
        this.selectListId.push(data.id);
        this.selectListId = [...new Set(this.selectListId)];
      } else {
        this.selectList = [];
        this.selectListId = data.id;
      }
      this.selectList.push({
        fullName: this.selectType === "all" ? data.fullName : data.lastFullName,
        id: data.id,
      });
      //数组对象去重--[...new Set(this.selectList.map(JSON.stringify))].map(JSON.parse)
      //数组去重--[...new Set(this.selectList)]
      this.selectList = [...new Set(this.selectList.map(JSON.stringify))].map(
        JSON.parse
      );
    },
    delSelect(index) {
      this.selectList.splice(index, 1);
      this.selectListId.splice(index, 1);
    },
    setCheckAll() {
      this.selectListId = [];
      this.selectList = [];
      this.$refs.tree.setCheckAll(false);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.realProps.label].indexOf(value) !== -1;
    },
    handleSearch(val) {
      this.$refs.tree && this.$refs.tree.filter(val);
    },
    handleConfirm() {
      this.$emit("confirm", this.selectList, this.selectListId);
      this.close();
    },
    close() {
      this.$emit("close", false);
    },
  },
};
</script>
