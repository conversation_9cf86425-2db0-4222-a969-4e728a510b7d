<template>
  <view class="patient-list-container">
    <!-- 顶部搜索和筛选区域 -->
    <view class="header-section">
      <view class="search-card">
        <view class="search-input-wrapper">
          <view class="search-icon">🔍</view>
          <input
            v-model="listQuery.keyword"
            placeholder="搜索患者姓名、身份证、住院号..."
            class="search-input"
            @input="search"
          />
          <view class="clear-btn" v-if="listQuery.keyword" @click="clearSearch"
            >✕</view
          >
        </view>
      </view>

      <!-- 筛选按钮 -->
      <view class="filter-buttons">
        <view class="filter-btn" @click="toggleFilter">
          <view class="filter-icon">📋</view>
          <text class="filter-text">筛选</text>
        </view>
        <view class="sort-btn" @click="toggleSort">
          <view class="sort-icon">📊</view>
          <text class="sort-text">排序</text>
        </view>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view class="filter-panel" v-if="showFilter">
      <view class="filter-content">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
          <view class="close-btn" @click="toggleFilter">✕</view>
        </view>
        <view class="filter-form">
          <view class="form-item">
            <text class="form-label">患者姓名</text>
            <input
              v-model="searchForm.name"
              placeholder="请输入患者姓名"
              class="form-input"
            />
          </view>
          <view class="form-item">
            <text class="form-label">性别</text>
            <picker
              :value="getSexIndex()"
              :range="getSexOptions()"
              range-key="fullName"
              @change="onSexChange"
            >
              <view class="picker-display">
                {{ getSexText() || "请选择性别" }}
              </view>
            </picker>
          </view>
          <view class="form-item">
            <text class="form-label">身份证号</text>
            <input
              v-model="searchForm.idCard"
              placeholder="请输入身份证号"
              class="form-input"
            />
          </view>
          <view class="form-item">
            <text class="form-label">住院号</text>
            <input
              v-model="searchForm.admissionNo"
              placeholder="请输入住院号"
              class="form-input"
            />
          </view>
        </view>
        <view class="filter-actions">
          <view class="action-btn secondary-btn" @click="reset">重置</view>
          <view class="action-btn primary-btn" @click="applyFilter">确定</view>
        </view>
      </view>
    </view>

    <!-- 排序面板 -->
    <view class="sort-panel" v-if="showSort">
      <view class="sort-content">
        <view class="sort-header">
          <text class="sort-title">排序方式</text>
          <view class="close-btn" @click="toggleSort">✕</view>
        </view>
        <view class="sort-options">
          <view
            class="sort-option"
            v-for="(item, index) in sortOptions"
            :key="index"
            :class="{ active: sortValue.includes(item.value) }"
            @click="selectSort(item)"
          >
            <text class="option-text">{{ item.label }}</text>
            <view class="check-icon" v-if="sortValue.includes(item.value)"
              >✓</view
            >
          </view>
        </view>
        <view class="sort-actions">
          <view class="action-btn secondary-btn" @click="handleSortReset"
            >清空</view
          >
          <view class="action-btn primary-btn" @click="handleSortSearch"
            >确定</view
          >
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="list-section">
      <mescroll-uni
        ref="mescrollRef"
        @init="mescrollInit"
        @down="downCallback"
        @up="upCallback"
        :up="upOption"
        :fixed="false"
      >
        <view class="patient-list">
          <view
            class="patient-card"
            v-for="(item, index) in list"
            :key="index"
            @click="goDetail(item)"
          >
            <view class="card-header">
              <view class="patient-info">
                <view class="patient-avatar">
                  <text class="avatar-text">{{
                    getPatientAvatarText(item.name)
                  }}</text>
                </view>
                <view class="patient-details">
                  <view class="patient-name">{{
                    item.name || "未知患者"
                  }}</view>
                  <view class="patient-meta">
                    <text class="meta-item">{{ item.age || "未知" }}岁</text>
                    <text class="meta-separator">·</text>
                    <text class="meta-item">{{ getSexText(item.sex) }}</text>
                  </view>
                </view>
              </view>
              <view class="follow-status" :class="getFollowStatusClass(item)">
                {{ getFollowStatusText(item) }}
              </view>
            </view>

            <view class="card-content">
              <view class="info-row">
                <view class="info-item">
                  <text class="info-label">住院号</text>
                  <text class="info-value">{{
                    item.admissionNo || "暂无"
                  }}</text>
                </view>
                <view class="info-item">
                  <text class="info-label">随访次数</text>
                  <text class="info-value">{{ item.fcount || 0 }}次</text>
                </view>
              </view>

              <view class="info-row">
                <view class="info-item full-width">
                  <text class="info-label">身份证号</text>
                  <text class="info-value">{{
                    formatIdCard(item.idCard)
                  }}</text>
                </view>
              </view>

              <view class="info-row" v-if="item.dischargeDate">
                <view class="info-item full-width">
                  <text class="info-label">出院日期</text>
                  <text class="info-value">{{
                    formatDate(item.dischargeDate)
                  }}</text>
                </view>
              </view>
            </view>

            <view class="card-actions">
              <view class="action-item" @click.stop="goToVisitRecord(item)">
                <view class="action-icon">📋</view>
                <text class="action-text">随访</text>
              </view>
              <view class="action-item" @click.stop="editPatient(item)">
                <view class="action-icon">✏️</view>
                <text class="action-text">编辑</text>
              </view>
              <view
                class="action-item"
                @click.stop="deletePatient(item, index)"
              >
                <view class="action-icon">🗑️</view>
                <text class="action-text">删除</text>
              </view>
            </view>
          </view>

          <!-- 空状态 -->
          <view class="empty-state" v-if="list.length === 0 && !loading">
            <view class="empty-icon">👥</view>
            <text class="empty-text">暂无患者信息</text>
            <text class="empty-desc">点击右下角按钮添加新的患者</text>
          </view>
        </view>
      </mescroll-uni>
    </view>

    <!-- 添加按钮 -->
    <view class="add-btn" @click="addPage()">
      <view class="add-icon">+</view>
    </view>
  </view>
</template>

<script>
import resources from "@/libs/resources.js";
import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
import { getDictionaryDataSelector } from "@/api/common";
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
import request from "@/utils/request";

import { getList, del, columns } from "@/api/flow-up/patient";
export default {
  mixins: [MescrollMixin],
  components: {},
  data() {
    return {
      isAuthority: true,
      icon: resources.message.nodata,
      sortValue: [],
      searchForm: {},
      downOption: {
        use: true,
        auto: false,
      },
      dataOptions: {},
      upOption: {
        page: {
          num: 0,
          size: 20,
          time: null,
        },
        empty: {
          use: true,
          icon: resources.message.nodata,
          tip: "暂无数据",
          fixed: true,
          zIndex: 5,
        },
        textNoMore: "没有更多数据",
        toTop: {
          bottom: 250,
        },
      },
      list: [],
      columns: columns,
      listQuery: {
        moduleId: "***********",
        sidx: "",
        keyword: "",
        json: "",
      },
      options: [
        {
          text: "删除",
          style: {
            backgroundColor: "#dd524d",
          },
        },
      ],
      sortOptions: [
        {
          label: "主键降序",
          sidx: "id",
          value: "-id",
          sort: "desc",
        },
        {
          label: "主键升序",
          sidx: "id",
          value: "id",
          sort: "asc",
        },
        {
          label: "姓名降序",
          sidx: "name",
          value: "-name",
          sort: "desc",
        },
        {
          label: "姓名升序",
          sidx: "name",
          value: "name",
          sort: "asc",
        },

        {
          label: "年龄降序",
          sidx: "age",
          value: "-age",
          sort: "desc",
        },
        {
          label: "年龄升序",
          sidx: "age",
          value: "age",
          sort: "asc",
        },

        {
          label: "性别降序",
          sidx: "sex",
          value: "-sex",
          sort: "desc",
        },
        {
          label: "性别升序",
          sidx: "sex",
          value: "sex",
          sort: "asc",
        },

        {
          label: "身份证降序",
          sidx: "idCard",
          value: "-idCard",
          sort: "desc",
        },
        {
          label: "身份证升序",
          sidx: "idCard",
          value: "idCard",
          sort: "asc",
        },

        {
          label: "住院号降序",
          sidx: "admissionNo",
          value: "-admissionNo",
          sort: "desc",
        },
        {
          label: "住院号升序",
          sidx: "admissionNo",
          value: "admissionNo",
          sort: "asc",
        },

        {
          label: "详细地址降序",
          sidx: "addressDetail",
          value: "-addressDetail",
          sort: "desc",
        },
        {
          label: "详细地址升序",
          sidx: "addressDetail",
          value: "addressDetail",
          sort: "asc",
        },

        {
          label: "出院日期降序",
          sidx: "dischargeDate",
          value: "-dischargeDate",
          sort: "desc",
        },
        {
          label: "出院日期升序",
          sidx: "dischargeDate",
          value: "dischargeDate",
          sort: "asc",
        },

        {
          label: "入院日期降序",
          sidx: "admissionDate",
          value: "-admissionDate",
          sort: "desc",
        },
        {
          label: "入院日期升序",
          sidx: "admissionDate",
          value: "admissionDate",
          sort: "asc",
        },

        {
          label: "入院诊断降序",
          sidx: "admittingDiagnosis",
          value: "-admittingDiagnosis",
          sort: "desc",
        },
        {
          label: "入院诊断升序",
          sidx: "admittingDiagnosis",
          value: "admittingDiagnosis",
          sort: "asc",
        },

        {
          label: "管床医师降序",
          sidx: "pipeBedPhysician",
          value: "-pipeBedPhysician",
          sort: "desc",
        },
        {
          label: "管床医师升序",
          sidx: "pipeBedPhysician",
          value: "pipeBedPhysician",
          sort: "asc",
        },

        {
          label: "随访总人数降序",
          sidx: "tnumber",
          value: "-tnumber",
          sort: "desc",
        },
        {
          label: "随访总人数升序",
          sidx: "tnumber",
          value: "tnumber",
          sort: "asc",
        },

        {
          label: "随访应完成人数降序",
          sidx: "dnumber",
          value: "-dnumber",
          sort: "desc",
        },
        {
          label: "随访应完成人数升序",
          sidx: "dnumber",
          value: "dnumber",
          sort: "asc",
        },

        {
          label: "随访率降序",
          sidx: "rate",
          value: "-rate",
          sort: "desc",
        },
        {
          label: "随访率升序",
          sidx: "rate",
          value: "rate",
          sort: "asc",
        },

        {
          label: "随访次数降序",
          sidx: "fCount",
          value: "-fCount",
          sort: "desc",
        },
        {
          label: "随访次数升序",
          sidx: "fCount",
          value: "fCount",
          sort: "asc",
        },

        {
          label: "随访类型降序",
          sidx: "type",
          value: "-type",
          sort: "desc",
        },
        {
          label: "随访类型升序",
          sidx: "type",
          value: "type",
          sort: "asc",
        },

        {
          label: "地址降序",
          sidx: "address",
          value: "-address",
          sort: "desc",
        },
        {
          label: "地址升序",
          sidx: "address",
          value: "address",
          sort: "asc",
        },

        {
          label: "纬度降序",
          sidx: "longitude",
          value: "-longitude",
          sort: "desc",
        },
        {
          label: "纬度升序",
          sidx: "longitude",
          value: "longitude",
          sort: "asc",
        },

        {
          label: "经度降序",
          sidx: "latitude",
          value: "-latitude",
          sort: "desc",
        },
        {
          label: "经度升序",
          sidx: "latitude",
          value: "latitude",
          sort: "asc",
        },
      ],
      ableAll: {},
      interfaceRes: {
        id: [],
        name: [],
        age: [],
        sex: [],
        idCard: [],
        admissionNo: [],
        addressDetail: [],
        dischargeDate: [],
        admissionDate: [],
        admittingDiagnosis: [],
        pipeBedPhysician: [],
        tnumber: [],
        dnumber: [],
        rate: [],
        fCount: [],
        type: [],
        address: [],
        longitude: [],
        latitude: [],
      },
      menuId: "",
      columnList: [],
      key: new Date(),
      dataValue: {},
      userInfo: {},
      firstInitSearchData: false,
      tabList: [],
      tabKey: 0,
      optionsObj: {
        defaultProps: {
          label: "fullName",
          value: "enCode",
          multiple: false,
          children: "",
        },
        SexOptions: [],
        FlowTypeOptions: [],
      },
      // 新增状态变量
      showFilter: false,
      showSort: false,
      loading: false,
    };
  },
  onLoad(e) {
    this.userInfo = uni.getStorageSync("userInfo") || {};
    this.menuId = e.menuId;
    this.setDefaultQuery();
    this.dataAll();
    this.getColumnList();
  },
  onShow() {
    this.$nextTick(() => {
      this.mescroll.resetUpScroll();
    });
  },
  onUnload() {
    uni.$off("refresh");
  },
  methods: {
    // 获取患者头像文字
    getPatientAvatarText(name) {
      if (!name) return "患";
      return name.length > 1 ? name.slice(-2) : name;
    },

    // 获取性别文本
    getSexText(sex) {
      if (!sex || !this.optionsObj.SexOptions) return "未知";
      const sexOption = this.optionsObj.SexOptions.find(
        (item) => item.enCode === sex
      );
      return sexOption ? sexOption.fullName : "未知";
    },

    // 获取随访状态文本
    getFollowStatusText(item) {
      const count = item.fcount || 0;
      if (count === 0) return "未随访";
      if (count >= 3) return "已完成";
      return `已随访${count}次`;
    },

    // 获取随访状态样式类
    getFollowStatusClass(item) {
      const count = item.fcount || 0;
      if (count === 0) return "status-none";
      if (count >= 3) return "status-completed";
      return "status-partial";
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return "暂无";
      if (idCard.length <= 8) return idCard;
      return idCard.slice(0, 4) + "****" + idCard.slice(-4);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "暂无";
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    // 切换筛选面板
    toggleFilter() {
      this.showFilter = !this.showFilter;
      if (this.showFilter) {
        this.showSort = false;
      }
    },

    // 切换排序面板
    toggleSort() {
      this.showSort = !this.showSort;
      if (this.showSort) {
        this.showFilter = false;
      }
    },

    // 清空搜索
    clearSearch() {
      this.listQuery.keyword = "";
      this.search();
    },

    // 应用筛选
    applyFilter() {
      this.showFilter = false;
      this.mescroll.resetUpScroll();
    },

    // 选择排序
    selectSort(item) {
      const index = this.sortValue.indexOf(item.value);
      if (index > -1) {
        this.sortValue.splice(index, 1);
      } else {
        this.sortValue.push(item.value);
      }
    },

    // 性别选择器相关方法
    getSexOptions() {
      return this.optionsObj.SexOptions || [];
    },
    getSexIndex() {
      if (!this.searchForm.sex) return 0;
      const options = this.getSexOptions();
      const index = options.findIndex(
        (item) => item.enCode === this.searchForm.sex
      );
      return index >= 0 ? index : 0;
    },
    onSexChange(e) {
      const index = e.detail.value;
      const options = this.getSexOptions();
      if (options[index]) {
        this.searchForm.sex = options[index].enCode;
      }
    },

    // 跳转到随访记录页面
    goToVisitRecord(item) {
      uni.navigateTo({
        url: `/pages/flow-up/doVisitRecord?patientId=${
          item.id
        }&patientName=${encodeURIComponent(
          item.name || "未知患者"
        )}&actionType=add`,
      });
    },

    // 编辑患者
    editPatient(item) {
      uni.navigateTo({
        url: `/pages/flow-up/patient/form?id=${item.id}&actionType=btn_edit`,
      });
    },

    // 删除患者
    deletePatient(item, index) {
      uni.showModal({
        title: "提示",
        content: `确定删除患者 ${item.name || "未知"} 吗？`,
        success: (res) => {
          if (res.confirm) {
            this.delete(item);
          }
        },
      });
    },

    getSelectOptions() {
      getDictionaryDataSelector("Sex").then((res) => {
        this.optionsObj.SexOptions = res.data.list;
      });
      getDictionaryDataSelector("FlowType").then((res) => {
        this.optionsObj.FlowTypeOptions = res.data.list;
      });
    },
    dataAll() {
      this.getSelectOptions();
    },
    openData(e) {},
    //设置默认排序
    setDefaultQuery() {
      const defaultSortConfig = [];
      const sortField = defaultSortConfig.map(
        (o) => (o.sort === "desc" ? "-" : "") + o.field
      );
      this.listQuery.sidx = sortField.join(",");
    },
    //初始化查询的默认数据
    async initSearchData() {
      this.dataValue = JSON.parse(JSON.stringify(this.searchForm));
    },
    // 上拉刷新
    async upCallback(page) {
      if (!this.firstInitSearchData) {
        await this.initSearchData();
        this.firstInitSearchData = true;
      }
      const query = {
        currentPage: page.num,
        pageSize: page.size,
        menuId: this.menuId,
        ...this.listQuery,
        ...this.searchForm,
        dataType: 0,
        myFlag: true,
      };
      getList(query)
        .then((res) => {
          let _list = res.data.list;
          this.mescroll.endSuccess(_list.length);
          if (page.num == 1) this.list = [];
          const list = _list.map((o) => ({
            show: false,
            ...o,
          }));
          this.list = this.list.concat(_list);
        })
        .catch(() => {
          this.mescroll.endSuccess(this.list.length);
        });
    },
    handleClick(index, index1) {
      const item = this.list[index];
      uni.showModal({
        title: "提示",
        content: "确定删除?",
        success: (res) => {
          if (res.confirm) {
            this.delete(item);
          }
        },
      });
    },
    // 删除
    delete(item) {
      del(item.id).then((res) => {
        uni.showToast({
          title: res.msg,
          complete: () => {
            this.$u.toast(res.msg);
            this.mescroll.resetUpScroll();
          },
        });
      });
    },
    open(index) {
      this.list[index].show = true;
      this.list.map((val, idx) => {
        if (index != idx) this.list[idx].show = false;
      });
    },
    search() {
      if (this.isPreview == "1") return;
      this.searchTimer && clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      }, 300);
    },
    // 跳转详情页
    goDetail(item) {
      let id = item.id;
      let btnType = "";
      let btnList = [];
      btnList.push("btn_edit");
      btnList.push("btn_detail");
      if (btnList.length == 0) return;
      this.jumPage(id, btnList);
    },
    // 跳转添加页
    addPage() {
      this.jumPage();
    },
    jumPage(id, btnList) {
      let idVal = id ? "&id=" + id : "";
      let idList = [];
      for (let i = 0; i < this.list.length; i++) {
        idList.push(this.list[i].id);
      }
      let idListVal = "&idList=" + idList;
      if (!id) {
        uni.navigateTo({
          url: "./form?menuId=" + this.menuId + "&jurisdictionType=btn_add",
        });
      } else if (btnList.includes("btn_detail")) {
        uni.navigateTo({
          url:
            "./detail?menuId=" +
            this.menuId +
            "&btnList=" +
            btnList +
            idVal +
            idListVal,
        });
      } else if (btnList.includes("btn_edit")) {
        uni.navigateTo({
          url:
            "./form?menuId=" +
            this.menuId +
            "&jurisdictionType=btn_edit&btnList=" +
            btnList +
            idVal +
            idListVal,
        });
      }
    },
    // 权限列
    getColumnList() {
      let columnPermissionList = [];
      let _appColumnList = this.columns;
      for (let i = 0; i < _appColumnList.length; i++) {
        columnPermissionList.push(_appColumnList[i]);
      }
      this.columnList = columnPermissionList;
    },
    // 操作点击
    cellClick(item) {
      const findIndex = this.sortValue.findIndex((o) => o === item.value);
      if (findIndex < 0) {
        const findLikeIndex = this.sortValue.findIndex(
          (o) => o.indexOf(item.sidx) > -1
        );
        if (findLikeIndex > -1) this.sortValue.splice(findLikeIndex, 1);
        this.sortValue.push(item.value);
      } else {
        this.sortValue.splice(findIndex, 1);
      }
    },
    // 重置排序
    handleSortReset() {
      this.sortValue = [];
    },
    // 排序搜索
    handleSortSearch() {
      if (this.sortValue.length) {
        this.listQuery.sidx = this.sortValue.join(",");
      } else {
        this.setDefaultQuery();
      }
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    // 重置查询条件
    reset() {
      this.searchForm = JSON.parse(JSON.stringify(this.dataValue));
      this.key = new Date();
    },
    // 查询检索
    closeDropdown() {
      this.$refs.uDropdown.close();
      this.$nextTick(() => {
        this.list = [];
        this.mescroll.resetUpScroll();
      });
    },
    dataList(data) {
      let _list = data.list;
      return _list;
    },
  },
};
</script>

<style lang="scss">
page {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  min-height: 100vh;
}

.patient-list-container {
  min-height: 100vh;
  padding-bottom: 120rpx;
}

// 顶部搜索和筛选区域
.header-section {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #fff;
  padding: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(25, 118, 210, 0.1);
  border-bottom: 1rpx solid rgba(25, 118, 210, 0.1);

  .search-card {
    background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
    border-radius: 20rpx;
    padding: 16rpx 20rpx;
    margin-bottom: 16rpx;
    border: 1rpx solid rgba(25, 118, 210, 0.1);

    .search-input-wrapper {
      display: flex;
      align-items: center;
      gap: 16rpx;

      .search-icon {
        font-size: 32rpx;
        color: #1976d2;
      }

      .search-input {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        background: transparent;
        border: none;
        outline: none;

        &::placeholder {
          color: #adb5bd;
        }
      }

      .clear-btn {
        width: 32rpx;
        height: 32rpx;
        background: #adb5bd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20rpx;
        color: #fff;
        cursor: pointer;

        &:active {
          background: #6c757d;
        }
      }
    }
  }

  .filter-buttons {
    display: flex;
    gap: 16rpx;

    .filter-btn,
    .sort-btn {
      flex: 1;
      height: 72rpx;
      background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
      border-radius: 16rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8rpx;
      color: #fff;
      font-weight: 600;
      box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 2rpx 8rpx rgba(25, 118, 210, 0.3);
      }

      .filter-icon,
      .sort-icon {
        font-size: 24rpx;
      }

      .filter-text,
      .sort-text {
        font-size: 26rpx;
      }
    }
  }
}

// 筛选面板
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .filter-content {
    width: 100%;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 32rpx;
    max-height: 80vh;
    overflow-y: auto;

    .filter-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;

      .filter-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        background: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #666;
      }
    }

    .filter-form {
      .form-item {
        margin-bottom: 24rpx;

        .form-label {
          font-size: 28rpx;
          color: #1565c0;
          font-weight: 600;
          margin-bottom: 12rpx;
          display: block;
        }

        .form-input {
          width: 100%;
          height: 80rpx;
          padding: 0 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          box-sizing: border-box;

          &:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
            outline: none;
          }

          &::placeholder {
            color: #adb5bd;
          }
        }

        .picker-display {
          width: 100%;
          height: 80rpx;
          padding: 0 20rpx;
          border: 2rpx solid #e9ecef;
          border-radius: 16rpx;
          font-size: 28rpx;
          color: #333;
          background: #fff;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          position: relative;

          &::after {
            content: "▼";
            position: absolute;
            right: 20rpx;
            color: #adb5bd;
            font-size: 20rpx;
          }

          &:active {
            border-color: #1976d2;
            box-shadow: 0 0 0 6rpx rgba(25, 118, 210, 0.1);
          }
        }
      }
    }

    .filter-actions {
      display: flex;
      gap: 16rpx;
      margin-top: 32rpx;

      .action-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        &.primary-btn {
          background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
          color: #fff;
          box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
        }

        &.secondary-btn {
          background: #f8f9fa;
          color: #6c757d;
          border: 2rpx solid #dee2e6;
        }

        &:active {
          transform: translateY(2rpx);
        }
      }
    }
  }
}

// 排序面板
.sort-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .sort-content {
    width: 100%;
    background: #fff;
    border-radius: 24rpx 24rpx 0 0;
    padding: 32rpx;
    max-height: 80vh;
    overflow-y: auto;

    .sort-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 32rpx;

      .sort-title {
        font-size: 32rpx;
        font-weight: 700;
        color: #1565c0;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        background: #f5f5f5;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #666;
      }
    }

    .sort-options {
      .sort-option {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        margin-bottom: 12rpx;
        background: #f8f9fa;
        border-radius: 16rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
          border-color: #1976d2;

          .option-text {
            color: #1976d2;
            font-weight: 600;
          }
        }

        .option-text {
          font-size: 28rpx;
          color: #333;
        }

        .check-icon {
          font-size: 24rpx;
          color: #1976d2;
          font-weight: 700;
        }
      }
    }

    .sort-actions {
      display: flex;
      gap: 16rpx;
      margin-top: 32rpx;

      .action-btn {
        flex: 1;
        height: 80rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28rpx;
        font-weight: 600;
        transition: all 0.3s ease;

        &.primary-btn {
          background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
          color: #fff;
          box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.3);
        }

        &.secondary-btn {
          background: #f8f9fa;
          color: #6c757d;
          border: 2rpx solid #dee2e6;
        }

        &:active {
          transform: translateY(2rpx);
        }
      }
    }
  }
}

// 列表内容
.list-section {
  padding: 20rpx;

  .patient-list {
    .patient-card {
      background: #fff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      padding: 24rpx;
      box-shadow: 0 8rpx 32rpx rgba(25, 118, 210, 0.08);
      border: 1rpx solid rgba(25, 118, 210, 0.1);
      transition: all 0.3s ease;

      &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 16rpx rgba(25, 118, 210, 0.12);
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20rpx;

        .patient-info {
          display: flex;
          align-items: center;
          gap: 16rpx;
          flex: 1;

          .patient-avatar {
            width: 64rpx;
            height: 64rpx;
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;

            .avatar-text {
              color: #fff;
              font-size: 24rpx;
              font-weight: 700;
            }
          }

          .patient-details {
            flex: 1;

            .patient-name {
              font-size: 32rpx;
              font-weight: 700;
              color: #333;
              margin-bottom: 8rpx;
            }

            .patient-meta {
              display: flex;
              align-items: center;
              gap: 8rpx;
              font-size: 24rpx;
              color: #666;

              .meta-separator {
                color: #ccc;
              }
            }
          }
        }

        .follow-status {
          padding: 8rpx 16rpx;
          border-radius: 12rpx;
          font-size: 22rpx;
          font-weight: 600;

          &.status-none {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
          }

          &.status-partial {
            background: rgba(25, 118, 210, 0.1);
            color: #1976d2;
          }

          &.status-completed {
            background: rgba(76, 175, 80, 0.1);
            color: #4caf50;
          }
        }
      }

      .card-content {
        .info-row {
          display: flex;
          gap: 24rpx;
          margin-bottom: 16rpx;

          &:last-child {
            margin-bottom: 0;
          }

          .info-item {
            flex: 1;
            padding: 16rpx;
            background: linear-gradient(135deg, #f8fbff 0%, #e8f4fd 100%);
            border-radius: 12rpx;
            border: 1rpx solid rgba(25, 118, 210, 0.1);

            &.full-width {
              flex: 100%;
            }

            .info-label {
              font-size: 22rpx;
              color: #1565c0;
              font-weight: 600;
              margin-bottom: 4rpx;
              display: block;
            }

            .info-value {
              font-size: 26rpx;
              color: #333;
              font-weight: 500;
              word-break: break-all;
            }
          }
        }
      }

      .card-actions {
        display: flex;
        gap: 12rpx;
        margin-top: 20rpx;
        padding-top: 20rpx;
        border-top: 1rpx solid #f0f0f0;

        .action-item {
          flex: 1;
          height: 64rpx;
          background: #f8f9fa;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8rpx;
          transition: all 0.3s ease;

          &:first-child {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: #fff;
          }

          &:nth-child(2) {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1976d2;
          }

          &:last-child {
            background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
            color: #f44336;
          }

          &:active {
            transform: scale(0.95);
          }

          .action-icon {
            font-size: 20rpx;
          }

          .action-text {
            font-size: 24rpx;
            font-weight: 600;
          }
        }
      }
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 120rpx 40rpx;
      text-align: center;

      .empty-icon {
        font-size: 120rpx;
        color: #e0e0e0;
        margin-bottom: 24rpx;
      }

      .empty-text {
        font-size: 32rpx;
        color: #666;
        font-weight: 600;
        margin-bottom: 12rpx;
      }

      .empty-desc {
        font-size: 26rpx;
        color: #999;
        line-height: 1.5;
      }
    }
  }
}

// 添加按钮
.add-btn {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 12rpx 40rpx rgba(25, 118, 210, 0.4);
  z-index: 100;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    box-shadow: 0 8rpx 24rpx rgba(25, 118, 210, 0.4);
  }

  .add-icon {
    font-size: 48rpx;
    color: #fff;
    font-weight: 300;
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .header-section {
    padding: 16rpx;

    .search-card {
      padding: 12rpx 16rpx;

      .search-input-wrapper {
        gap: 12rpx;

        .search-icon {
          font-size: 28rpx;
        }

        .search-input {
          font-size: 26rpx;
        }
      }
    }

    .filter-buttons {
      gap: 12rpx;

      .filter-btn,
      .sort-btn {
        height: 64rpx;

        .filter-icon,
        .sort-icon {
          font-size: 20rpx;
        }

        .filter-text,
        .sort-text {
          font-size: 24rpx;
        }
      }
    }
  }

  .list-section {
    padding: 16rpx;

    .patient-list {
      .patient-card {
        padding: 20rpx;

        .card-header {
          .patient-info {
            gap: 12rpx;

            .patient-avatar {
              width: 56rpx;
              height: 56rpx;

              .avatar-text {
                font-size: 20rpx;
              }
            }

            .patient-details {
              .patient-name {
                font-size: 28rpx;
              }

              .patient-meta {
                font-size: 22rpx;
              }
            }
          }

          .follow-status {
            padding: 6rpx 12rpx;
            font-size: 20rpx;
          }
        }

        .card-content {
          .info-row {
            gap: 16rpx;

            .info-item {
              padding: 12rpx;

              .info-label {
                font-size: 20rpx;
              }

              .info-value {
                font-size: 24rpx;
              }
            }
          }
        }

        .card-actions {
          gap: 8rpx;

          .action-item {
            height: 56rpx;

            .action-icon {
              font-size: 18rpx;
            }

            .action-text {
              font-size: 22rpx;
            }
          }
        }
      }
    }
  }

  .add-btn {
    width: 96rpx;
    height: 96rpx;
    bottom: 32rpx;
    right: 32rpx;

    .add-icon {
      font-size: 40rpx;
    }
  }
}
</style>
