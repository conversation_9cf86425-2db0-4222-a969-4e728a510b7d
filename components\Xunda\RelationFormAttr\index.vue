<template>
  <view class="xunda-relation-form-attr">
    <u-input
      v-model="innerValue"
      input-align="right"
      disabled
      :placeholder="placeholder"
    />
  </view>
</template>

<script>
import { useBaseStore } from "@/store/modules/base";
const baseStore = useBaseStore();
export default {
  name: "xunda-relation-form-attr",
  props: {
    modelValue: {
      type: [String, Number],
      default: "",
    },
    showField: {
      type: String,
      default: "",
    },
    relationField: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "relationFormAttr",
    },
    isStorage: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      innerValue: "",
      placeholder: "",
    };
  },
  computed: {
    relationData() {
      return baseStore.relationData;
    },
  },
  watch: {
    relationData: {
      handler(val) {
        if (!this.showField || !this.relationField) return;
        let obj = val[this.relationField] || {};
        this.innerValue = obj[this.showField] ? obj[this.showField] : "";
        this.$emit("change", this.innerValue);
      },
      deep: true,
    },
    innerValue(val) {
      this.$emit("update:modelValue", val);
    },
  },
  created() {
    const typeName = this.type === "relationFormAttr" ? "关联表单" : "弹窗选择";
    const tips = this.isStorage == 1 ? "同时会保存入库" : "不会保存";
    this.placeholder = `用于展示${typeName}的属性，且数据${tips}`;
  },
};
</script>

<style lang="scss" scoped>
.xunda-relation-form-attr {
  width: 100%;
  text-align: right;
}
</style>
