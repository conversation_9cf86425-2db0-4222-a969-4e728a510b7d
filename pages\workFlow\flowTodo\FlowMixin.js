/**
 * opType
 * -1 - 我发起的新建/编辑
 * 0 - 我发起的详情
 * 1 - 待签事宜
 * 2 - 待办事宜
 * 3 - 在办事宜
 * 4 - 已办事宜
 * 5 - 抄送事宜
 * 6 - 流程监控
 */
const statusMap = {
	2: [{
			name: '全部',
			status: ''
		},
		{
			name: '协办',
			status: '7'
		},
		{
			name: '退回',
			status: '5'
		},
		{
			name: '超时',
			status: '-2'
		}
	],
	1: [{
			name: '全部',
			status: ''
		},
		{
			name: '协办',
			status: '7'
		},
		{
			name: '退回',
			status: '5'
		},
		{
			name: '超时',
			status: '-2'
		}
	],
	3: [{
			name: '全部',
			status: ''
		},
		{
			name: '待提交',
			status: '0'
		},
		{
			name: '进行中',
			status: '1'
		},
		{
			name: '已完成',
			status: '2'
		}
	],
	4: [{
			name: '全部',
			status: ''
		},
		{
			name: '同意',
			status: '1'
		},
		{
			name: '拒绝',
			status: '2'
		},
		{
			name: '转审',
			status: '3'
		},
		{
			name: '加签',
			status: '4'
		},
		{
			name: '退回',
			status: '5'
		}
	],
	5: [{
			name: '全部',
			status: ''
		},
		{
			name: '已读',
			status: '1'
		},
		{
			name: '未读',
			status: '0'
		}
	]
};

import {
	getOperatorList,
	getFlowLaunchList
} from '@/api/workFlow/template'
export default {
	data() {
		return {
			mescrollTop: 206,
			statusList: [],
			tabsList: [{
				fullName: '待签',
				category: '0'
			}, {
				fullName: '待办',
				category: '1'
			}, {
				fullName: '在办',
				category: '2'
			}, {
				fullName: '发起',
				category: null
			}, {
				fullName: '已办',
				category: '3'
			}, {
				fullName: '抄送',
				category: '4'
			}],
			current: 0,
			subsectionIndex: 0,
			status: ''
		}
	},
	watch: {
		current: {
			handler(val) {
				if (val == 0) this.statusList = []
				if (val == 1) this.statusList = statusMap[1]
				if (val == 2) this.statusList = statusMap[2]
				if (val == 3) this.statusList = statusMap[3]
				if (val == 4) this.statusList = statusMap[4]
				if (val == 5) this.statusList = statusMap[5]
			},
			immediate: true
		},
	},
	onLoad(e) {
		if (e?.tabIndex) this.change(e.tabIndex)
	},
	methods: {
		/* tab1 */
		change(index) {
			let item = this.tabsList[index]
			this.status = ''
			this.keyword = ''
			this.subsectionIndex = 0
			this.current = Number(index);
			this.category = item.category
			if (this.current != 0) {
				this.mescrollTop = 300
			} else {
				this.mescrollTop = 206
			}
			this.$nextTick(() => {
				this.list = [];
				this.mescroll.resetUpScroll();
			})
		},
		/* tab2 */
		subsection(e) {
			let item = this.statusList[e]
			this.status = item.status
			this.subsectionIndex = e
			this.$nextTick(() => {
				this.list = [];
				this.mescroll.resetUpScroll();
			})
		},
		/* 列表数据 */
		upCallback(page) {
			let methods = this.category ? getOperatorList : getFlowLaunchList;
			let query = {
				currentPage: page.num,
				pageSize: page.size,
				keyword: this.keyword,
				category: this.category,
				status: this.status
			}
			methods(query, {
				load: page.num == 1
			}).then(res => {
				this.mescroll.endSuccess(res.data.list.length);
				if (page.num == 1) this.list = [];
				let flowStatus;
				const list = res.data.list.map(o => ({
					'flowStatus': this.getFlowStatus(o.status),
					'opType': this.setOpType(o.status),
					'swipeAction': this.swipeAction(o.status),
					...o
				}))
				this.list = this.list.concat(list);
			}).catch(() => {
				this.mescroll.endErr();
			})
		},
		swipeAction(status) {
			let swipeAction = true
			if (this.current === 3 && !this.category && status == '0') swipeAction = false
			return swipeAction
		},
		/* 设置opType */
		setOpType(status) {
			if (this.current == 3) return status == '0' || status == '9' || status == '8' ? '-1' : 0
			if (this.current == 0) return 1
			if (this.current == 1) return 2
			if (this.current == 2) return 3
			if (this.current == 4) return 4
			if (this.current == 5) return 5
		}
	}
}